package com.paic.ncbs.claim.service.lawsuit;

import com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO;

import java.util.List;

/**
 * <p>
 * 诉讼案件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
public interface ClmsLawsuitCaseService {

    /**
     * 诉讼-诉讼登记
     * @param clmsLawsuitAuditCaseVO
     */
    void saveclmsLawsuitCase(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO);

    /**
     * 根据id获取案件详情
     * @param id
     * @return
     */
    ClmsLawsuitAuditCaseVO getLawsuitCaseById(String id);

    /**
     * 获取案件委托律师
     * @param reportNo,caseTimes
     * @return
     */
    List<Object> getALawyerForYourCase(String reportNo, String caseTimes);

    /**
     * 查询案件下所有诉讼信息
     * @param reportNo,caseTimes
     * @return
     */
    List<ClmsLawsuitAuditCaseVO> getClmsLawsuit(String reportNo,Integer caseTimes);

    /**
     * 获取案件下所有诉讼信息数量
     * @param reportNo,caseTimes
     * @return
     */
    Integer getClmsLawsuitCount(String reportNo, Integer caseTimes);

    /**
     * 获取机构
     * @return
     */
    List<Object> getDepartmentList();

}
