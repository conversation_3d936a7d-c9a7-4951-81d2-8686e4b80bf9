package com.paic.ncbs.claim.dao.entity.report;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 事故信息表
 */
public class ReportAccidentEntity extends EntityDTO {

    private static final long serialVersionUID = -7076285875677312247L;

    private String idClmReportAccident;

    private String reportNo;

    private String accidentCauseLevel1;

    private String accidentCauseLevel2;

    private String accidentCauseLevel3;

    private String accidentCauseLevel4;

    private String accidentDetail;

    private String accidentCityCode;

    private String accidentCountyCode;

    private String accidentPlace;

    private Date accidentDate;

    private String accidentType;

    private String accidentTypeDetail;

    private String accidentResponsibility;

    private String isThirdNotFound;

    private String isViolationLoading;

    private String isTravelAcrossRegion;

    private String isFirstCargoLoss;

    private String isDriverInjured;

    private String isPanssengeInjured;

    private String isThiCarLoss;

    private String isInThiCarInjured;

    private String isOutThiCarInjured;

    private String isInThiCargoLoss;

    private String isOutThiCargoLoss;

    private String migrateFrom;

    private String isCanDriving;

    private String accidentTownCode;

    private String accidentVillageCode;

    private String isOnFreeway;

    private Integer injuredNumber;

    private Integer deatToll;

    private String oneConditionFlag;

    private String damageClimate;

    private String disposeStyle;

    private String occurrenceArea;

    private String reason;

    private String accidentPlaceString;

    private String shanghaiRoadName;

    private String shanghaiRoadId;

    private String accidentPlaceCode;

    private BigDecimal accidentMileage;

    private String accidentPlaceGpsX;

    private String accidentPlaceGpsY;

    private String isDutyClear;

    private String overseasOccur;

    private String accidentArea;

    private String provinceCode;

    private String accidentName;

    private String overseaNationCode;

    public String getOverseaNationCode() {
        return overseaNationCode;
    }

    public void setOverseaNationCode(String overseaNationCode) {
        this.overseaNationCode = overseaNationCode;
    }

    public String getAccidentName() {
        return accidentName;
    }

    public void setAccidentName(String accidentName) {
        this.accidentName = accidentName;
    }

    public String getIdClmReportAccident() {
        return idClmReportAccident;
    }

    public void setIdClmReportAccident(String idClmReportAccident) {
        this.idClmReportAccident = idClmReportAccident == null ? null : idClmReportAccident.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public String getAccidentCauseLevel1() {
        return accidentCauseLevel1;
    }

    public void setAccidentCauseLevel1(String accidentCauseLevel1) {
        this.accidentCauseLevel1 = accidentCauseLevel1 == null ? null : accidentCauseLevel1.trim();
    }

    public String getAccidentCauseLevel2() {
        return accidentCauseLevel2;
    }

    public void setAccidentCauseLevel2(String accidentCauseLevel2) {
        this.accidentCauseLevel2 = accidentCauseLevel2 == null ? null : accidentCauseLevel2.trim();
    }

    public String getAccidentCauseLevel3() {
        return accidentCauseLevel3;
    }

    public void setAccidentCauseLevel3(String accidentCauseLevel3) {
        this.accidentCauseLevel3 = accidentCauseLevel3 == null ? null : accidentCauseLevel3.trim();
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail == null ? null : accidentDetail.trim();
    }

    public String getAccidentCityCode() {
        return accidentCityCode;
    }

    public void setAccidentCityCode(String accidentCityCode) {
        this.accidentCityCode = accidentCityCode == null ? null : accidentCityCode.trim();
    }

    public String getAccidentCountyCode() {
        return accidentCountyCode;
    }

    public void setAccidentCountyCode(String accidentCountyCode) {
        this.accidentCountyCode = accidentCountyCode == null ? null : accidentCountyCode.trim();
    }

    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace == null ? null : accidentPlace.trim();
    }

    public Date getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType == null ? null : accidentType.trim();
    }

    public String getAccidentTypeDetail() {
        return accidentTypeDetail;
    }

    public void setAccidentTypeDetail(String accidentTypeDetail) {
        this.accidentTypeDetail = accidentTypeDetail == null ? null : accidentTypeDetail.trim();
    }

    public String getAccidentResponsibility() {
        return accidentResponsibility;
    }

    public void setAccidentResponsibility(String accidentResponsibility) {
        this.accidentResponsibility = accidentResponsibility == null ? null : accidentResponsibility.trim();
    }

    public String getIsThirdNotFound() {
        return isThirdNotFound;
    }

    public void setIsThirdNotFound(String isThirdNotFound) {
        this.isThirdNotFound = isThirdNotFound == null ? null : isThirdNotFound.trim();
    }

    public String getIsViolationLoading() {
        return isViolationLoading;
    }

    public void setIsViolationLoading(String isViolationLoading) {
        this.isViolationLoading = isViolationLoading == null ? null : isViolationLoading.trim();
    }

    public String getIsTravelAcrossRegion() {
        return isTravelAcrossRegion;
    }

    public void setIsTravelAcrossRegion(String isTravelAcrossRegion) {
        this.isTravelAcrossRegion = isTravelAcrossRegion == null ? null : isTravelAcrossRegion.trim();
    }

    public String getIsFirstCargoLoss() {
        return isFirstCargoLoss;
    }

    public void setIsFirstCargoLoss(String isFirstCargoLoss) {
        this.isFirstCargoLoss = isFirstCargoLoss == null ? null : isFirstCargoLoss.trim();
    }

    public String getIsDriverInjured() {
        return isDriverInjured;
    }

    public void setIsDriverInjured(String isDriverInjured) {
        this.isDriverInjured = isDriverInjured == null ? null : isDriverInjured.trim();
    }

    public String getIsPanssengeInjured() {
        return isPanssengeInjured;
    }

    public void setIsPanssengeInjured(String isPanssengeInjured) {
        this.isPanssengeInjured = isPanssengeInjured == null ? null : isPanssengeInjured.trim();
    }

    public String getIsThiCarLoss() {
        return isThiCarLoss;
    }

    public void setIsThiCarLoss(String isThiCarLoss) {
        this.isThiCarLoss = isThiCarLoss == null ? null : isThiCarLoss.trim();
    }

    public String getIsInThiCarInjured() {
        return isInThiCarInjured;
    }

    public void setIsInThiCarInjured(String isInThiCarInjured) {
        this.isInThiCarInjured = isInThiCarInjured == null ? null : isInThiCarInjured.trim();
    }

    public String getIsOutThiCarInjured() {
        return isOutThiCarInjured;
    }

    public void setIsOutThiCarInjured(String isOutThiCarInjured) {
        this.isOutThiCarInjured = isOutThiCarInjured == null ? null : isOutThiCarInjured.trim();
    }

    public String getIsInThiCargoLoss() {
        return isInThiCargoLoss;
    }

    public void setIsInThiCargoLoss(String isInThiCargoLoss) {
        this.isInThiCargoLoss = isInThiCargoLoss == null ? null : isInThiCargoLoss.trim();
    }

    public String getIsOutThiCargoLoss() {
        return isOutThiCargoLoss;
    }

    public void setIsOutThiCargoLoss(String isOutThiCargoLoss) {
        this.isOutThiCargoLoss = isOutThiCargoLoss == null ? null : isOutThiCargoLoss.trim();
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom == null ? null : migrateFrom.trim();
    }

    public String getIsCanDriving() {
        return isCanDriving;
    }

    public void setIsCanDriving(String isCanDriving) {
        this.isCanDriving = isCanDriving == null ? null : isCanDriving.trim();
    }

    public String getAccidentTownCode() {
        return accidentTownCode;
    }

    public void setAccidentTownCode(String accidentTownCode) {
        this.accidentTownCode = accidentTownCode == null ? null : accidentTownCode.trim();
    }

    public String getAccidentVillageCode() {
        return accidentVillageCode;
    }

    public void setAccidentVillageCode(String accidentVillageCode) {
        this.accidentVillageCode = accidentVillageCode == null ? null : accidentVillageCode.trim();
    }

    public String getIsOnFreeway() {
        return isOnFreeway;
    }

    public void setIsOnFreeway(String isOnFreeway) {
        this.isOnFreeway = isOnFreeway == null ? null : isOnFreeway.trim();
    }

    public Integer getInjuredNumber() {
        return injuredNumber;
    }

    public void setInjuredNumber(Integer injuredNumber) {
        this.injuredNumber = injuredNumber;
    }

    public Integer getDeatToll() {
        return deatToll;
    }

    public void setDeatToll(Integer deatToll) {
        this.deatToll = deatToll;
    }

    public String getOneConditionFlag() {
        return oneConditionFlag;
    }

    public void setOneConditionFlag(String oneConditionFlag) {
        this.oneConditionFlag = oneConditionFlag == null ? null : oneConditionFlag.trim();
    }

    public String getDamageClimate() {
        return damageClimate;
    }

    public void setDamageClimate(String damageClimate) {
        this.damageClimate = damageClimate == null ? null : damageClimate.trim();
    }

    public String getDisposeStyle() {
        return disposeStyle;
    }

    public void setDisposeStyle(String disposeStyle) {
        this.disposeStyle = disposeStyle == null ? null : disposeStyle.trim();
    }

    public String getOccurrenceArea() {
        return occurrenceArea;
    }

    public void setOccurrenceArea(String occurrenceArea) {
        this.occurrenceArea = occurrenceArea == null ? null : occurrenceArea.trim();
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public String getAccidentPlaceString() {
        return accidentPlaceString;
    }

    public void setAccidentPlaceString(String accidentPlaceString) {
        this.accidentPlaceString = accidentPlaceString == null ? null : accidentPlaceString.trim();
    }

    public String getShanghaiRoadName() {
        return shanghaiRoadName;
    }

    public void setShanghaiRoadName(String shanghaiRoadName) {
        this.shanghaiRoadName = shanghaiRoadName == null ? null : shanghaiRoadName.trim();
    }

    public String getShanghaiRoadId() {
        return shanghaiRoadId;
    }

    public void setShanghaiRoadId(String shanghaiRoadId) {
        this.shanghaiRoadId = shanghaiRoadId == null ? null : shanghaiRoadId.trim();
    }

    public String getAccidentPlaceCode() {
        return accidentPlaceCode;
    }

    public void setAccidentPlaceCode(String accidentPlaceCode) {
        this.accidentPlaceCode = accidentPlaceCode == null ? null : accidentPlaceCode.trim();
    }

    public BigDecimal getAccidentMileage() {
        return accidentMileage;
    }

    public void setAccidentMileage(BigDecimal accidentMileage) {
        this.accidentMileage = accidentMileage;
    }

    public String getAccidentPlaceGpsX() {
        return accidentPlaceGpsX;
    }

    public void setAccidentPlaceGpsX(String accidentPlaceGpsX) {
        this.accidentPlaceGpsX = accidentPlaceGpsX == null ? null : accidentPlaceGpsX.trim();
    }

    public String getAccidentPlaceGpsY() {
        return accidentPlaceGpsY;
    }

    public void setAccidentPlaceGpsY(String accidentPlaceGpsY) {
        this.accidentPlaceGpsY = accidentPlaceGpsY == null ? null : accidentPlaceGpsY.trim();
    }

    public String getIsDutyClear() {
        return isDutyClear;
    }

    public void setIsDutyClear(String isDutyClear) {
        this.isDutyClear = isDutyClear == null ? null : isDutyClear.trim();
    }

    public String getOverseasOccur() {
        return overseasOccur;
    }

    public void setOverseasOccur(String overseasOccur) {
        this.overseasOccur = overseasOccur == null ? null : overseasOccur.trim();
    }

    public String getAccidentArea() {
        return accidentArea;
    }

    public void setAccidentArea(String accidentArea) {
        this.accidentArea = accidentArea == null ? null : accidentArea.trim();
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    public String getAccidentCauseLevel4() {
        return accidentCauseLevel4;
    }

    public void setAccidentCauseLevel4(String accidentCauseLevel4) {
        this.accidentCauseLevel4 = accidentCauseLevel4;
    }
}