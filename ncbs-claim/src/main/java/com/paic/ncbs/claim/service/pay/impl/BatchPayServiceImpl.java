package com.paic.ncbs.claim.service.pay.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.pay.ClmBatchPayOperationRecordMapper;
import com.paic.ncbs.claim.dao.mapper.pay.ClmsPaymentPlanMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.pay.*;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.vo.pay.*;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.pay.BatchPayService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.user.DepartmentDefineService;
import com.paic.ncbs.um.model.dto.UserGradeInfoDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import com.paic.ncbs.um.service.UserCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.BpmConstants.INVESTIGATE_PLATFORM_NAME;

@Slf4j
@Service
public class BatchPayServiceImpl implements BatchPayService {
    @Autowired
    private ClmsPaymentPlanMapper clmsPaymentPlanMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private PaymentItemMapper paymentItemMapper;
    @Autowired
    private ClmBatchPayOperationRecordMapper clmBatchPayOperationRecordMapper;
    @Autowired
    private PayInfoNoticeThirdPartyCoreSAO payInfoNoticeThirdPartyCoreSAO;
    @Value("${ncbs.pay.paymentBackURL:http://ncbs-claim.lb.ssdev.com:48915/claim}")
    private String paymentBackURL;
    @Value("${tpa.account}")
    private String tpaAccountId;

    @Autowired
    DepartmentDefineService departmentDefineService;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private PaymentItemService paymentItemService;
    @Autowired
    private CacheService cacheService ;
    @Autowired
    private UserCommonService userCommonService ;


    /**
     * 获取批量支付打包查询页面的机构列表
     * @param
     */
    @Override
    public List<DepartmentVO> getSelectDepartmentList() {
        List<DepartmentVO> departmentVOList =
                departmentDefineService.getSelectDepartmentList();
        String departmentCode = WebServletContext.getDepartmentCode();
        if(!"1".equals(departmentCode)){
            List<DepartmentVO> resultList = departmentVOList
                    .stream()
                    .filter(vo -> vo.getDepartmentCode().equals(departmentCode))
                    .collect(Collectors.toList());
            return resultList;
        }
        return departmentVOList;
    }

    /**
     * 批量支付查询接口
     * @param dto
     */
    @Override
    public BatchPaymentDetailResult queryPaymentDetails(BatchPaymentDetailDTO dto) {
        if (StringUtils.isBlank(dto.getDepartmentAbbrCode())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "机构不能为空");
        }
        if (dto.getEndCaseDateStart() == null || dto.getEndCaseDateEnd() == null) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "结案时间不能为空");
        }
        Pager pager = new Pager();
        pager.setPageIndex(dto.getPager().getPageIndex());
        pager.setPageRows(dto.getPager().getPageRows());
        BatchPaymentDetailResult result = new BatchPaymentDetailResult();
        PageHelper.startPage(dto.getPager().getPageIndex(),dto.getPager().getPageRows(),true);
        List<BatchPaymentDetailVO> resultList = clmsPaymentPlanMapper.queryPaymentDetails(dto);
        PageInfo<BatchPaymentDetailVO> pageInfo =new PageInfo<>(resultList);
        pager.setTotalRows((int)pageInfo.getTotal());
        PageMethod.clearPage();
        result.setBatchPaymentDetailList(resultList);
        result.setPager(pager);
        LogUtil.info("批量支付，查询列表出参={}", JSON.toJSONString(resultList));
        return result;
    }

    /**
     * 批量支付详情列表
     * @param dto
     */
    @Override
    public BatchPaymentDetailResult queryPaymentListByBatchNo(BatchPaymentDetailDTO dto) {
        if (StringUtils.isBlank(dto.getBatchNo())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "批次号不能为空");
        }
        Pager pager = new Pager();
        pager.setPageIndex(dto.getPager().getPageIndex());
        pager.setPageRows(dto.getPager().getPageRows());
        BatchPaymentDetailResult result = new BatchPaymentDetailResult();
        PageHelper.startPage(dto.getPager().getPageIndex(),dto.getPager().getPageRows(),true);
        List<BatchPaymentDetailVO> resultList = clmsPaymentPlanMapper.queryPaymentListByBatchNo(dto);
        PageInfo<BatchPaymentDetailVO> pageInfo =new PageInfo<>(resultList);
        pager.setTotalRows((int)pageInfo.getTotal());
        PageMethod.clearPage();
        result.setBatchPaymentDetailList(resultList);
        result.setPager(pager);
        LogUtil.info("批量支付详情列表，查询列表出参={}", JSON.toJSONString(resultList));
        return result;
    }

    @Override
    @Transactional
    public BatchPackagePaymentVO packagePayment(BatchPackagePaymentDTO dto) {
        if (CollectionUtils.isEmpty(dto.getPaySerialNoList())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "支付流水号集合不能为空");
        }
        List<String> paySerialNoList = dto.getPaySerialNoList();
        LogUtil.info("批量打包开始执行：");
        String key = RedisKeyConstants.PACKAGE_PAYMENT;
        RLock lock = redissonClient.getLock(key);
        BatchPackagePaymentVO resultVO = new BatchPackagePaymentVO();
        try {
            if (lock.tryLock()) {
                //判断集合中结算方式是否存在不等于215(批量结算支付)的数据
                if(clmsPaymentPlanMapper.checkIsBatchPackage(paySerialNoList) > 0){
                    throw new GlobalBusinessException("支付集合中存在不可打包的数据，请刷新页面稍后再试！");
                }
                //判断支付集合是否存在重复打包
                if(clmsPaymentPlanMapper.checkIsRepeatPack(paySerialNoList) > 0){
                    throw new GlobalBusinessException("支付集合中存在已打包的数据，请刷新页面稍后再试！");
                }
                /**
                 * 查询账户信息
                 * 1、选中的数据机构相同、产品相同、支付用途相同
                 * 2、选中的所有数据收款信息相同
                 * * 账户类型 、收款人姓名、证件类型/号码、领款方式、结算方式、银行账号、银行大类、开户行
                 * * 账号类型：个人和公司 证件类型/号码字段不一样
                 * 3、选中的产品不能是退运险，即产品代码='01P00001'或者'01P00004'
                 **/
                List<MergePaymentVO> mergePaymentList =
                    paymentItemMapper.getPackagePaymentAccountList(paySerialNoList);
                if(mergePaymentList.size() > 1){
                    throw new GlobalBusinessException("打包的支付列表中收款信息必须相同！");
                }
                //若选中的是支付用途=费用，那么费用发票类型必须=无发票
                String paymentUsage = mergePaymentList.get(0).getPaymentUsage();
                if("P2".equals(paymentUsage)){
                    List<InvoiceInfoDTO> InvoiceInfoDTOList
                            = paymentItemMapper.getInvoiceNo(paySerialNoList);
                    if(InvoiceInfoDTOList.size() > 1){
                        throw new GlobalBusinessException("打包的支付列表中费用发票类型必须为无发票！");
                    }
                }
                //根据账户信息查询主信息
                for(MergePaymentVO mergePaymentVO: mergePaymentList){
                    //主信息
                    BatchPaymentMainInfo batchPaymentMainInfo =
                            paymentItemMapper.getPackagePaymentMainInfo(paySerialNoList);
                    //将主信息存数据库
                    MergePaymentDTO mergePaymentDTO = new MergePaymentDTO();
                    bulidMergePaymentDTO(mergePaymentList.get(0),mergePaymentDTO,batchPaymentMainInfo);
                    //批次数量
                    mergePaymentDTO.setSumCount(paySerialNoList.size());
                    paymentItemMapper.addClmsMergePayment(mergePaymentDTO);
                    int id = mergePaymentDTO.getId();
                    //更新clm_payment_item表
                    paymentItemMapper.updateBatchPaymentItemId(id,paySerialNoList);
                    resultVO.setSumPayAmount(batchPaymentMainInfo.getSumAmount());
                    resultVO.setBatchNo(batchPaymentMainInfo.getBatchNo());
                }
            } else {
                throw new GlobalBusinessException("批量打包正在处理中，请稍后再试！");
            }
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        LogUtil.info("批量打包执行结束！");
        return resultVO;
    }

    public static String generateBatchNumber() {
        // 获取当前时间并格式化为yyyyMMddHHmmss
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timePart = sdf.format(new Date());

        // 生成6位随机数
        Random random = new Random();
        int randomNum = random.nextInt(900000) + 100000; // 保证是6位数

        return "BP" + timePart + randomNum;
    }

    private void bulidMergePaymentDTO(MergePaymentVO mergePaymentVO,
                                      MergePaymentDTO mergePaymentDTO,
                                      BatchPaymentMainInfo batchPaymentMainInfo) {
        BeanUtils.copyProperties(mergePaymentVO,mergePaymentDTO);
        mergePaymentDTO.setParterCode(batchPaymentMainInfo.getParterCode());
        mergePaymentDTO.setParterName(batchPaymentMainInfo.getParterName());
        mergePaymentDTO.setPayeeName(mergePaymentVO.getPayeeName());
        mergePaymentDTO.setSumAmount(batchPaymentMainInfo.getSumAmount());
        String batchNo = generateBatchNumber();
        mergePaymentDTO.setBatchNo(batchNo);
        mergePaymentDTO.setPayType("02");//银行转账
        batchPaymentMainInfo.setBatchNo(batchNo);
        String userId = WebServletContext.getUserId();
        mergePaymentDTO.setCreatedBy(userId);
        mergePaymentDTO.setUpdatedBy(userId);
        mergePaymentDTO.setMergePaymentStatus("10");//草稿
    }

    /**
     * 查询已打包的数据
     * @param dto
     */
    @Override
    public BatchPaymentResult queryBatchPayDetails(BatchPaymentDTO dto) {
        checkQueryBatchPayParams(dto);
        BatchPaymentResult result = new BatchPaymentResult();
        Pager pager = new Pager();
        pager.setPageIndex(dto.getPager().getPageIndex());
        pager.setPageRows(dto.getPager().getPageRows());
        PageHelper.startPage(dto.getPager().getPageIndex(),dto.getPager().getPageRows(),true);
        List<BatchPaymentVO> resultList = clmsPaymentPlanMapper.queryBatchPayDetails(dto);
        PageInfo<BatchPaymentVO> pageInfo =new PageInfo<>(resultList);
        pager.setTotalRows((int)pageInfo.getTotal());
        result.setBatchPaymentVOList(resultList);
        result.setPager(pager);
        LogUtil.info("查询已打包列表出参={}", JSON.toJSONString(result));
        return result;
    }

    public void checkQueryBatchPayParams(BatchPaymentDTO dto){
        if(StringUtils.isBlank(dto.getDepartmentAbbrCode())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "机构不能为空");
        }
    }

    /**
     * 发起支付
     * @param dto
     */
    @Override
    @Transactional
    public void sendPayment(BatchPaymentDTO dto) {
        if(StringUtils.isBlank(dto.getBatchNo())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "批次号不能为空");
        }
        String batchNo = dto.getBatchNo();
        BatchPaymentInfo batchPaymentInfo = new BatchPaymentInfo();
        //主信息
        MergePaymentVO mergePaymentVO = paymentItemMapper.queryMergePaymentByBatchNo(batchNo);
        if(!"待发起".equals(mergePaymentVO.getMergePaymentStatus())){
            throw new GlobalBusinessException("该批次号已发起支付，请勿重复发起！");
        }
        BatchPaymentMainInfo batchPaymentMainInfo = new BatchPaymentMainInfo();
        BeanUtils.copyProperties(mergePaymentVO,batchPaymentMainInfo);
        batchPaymentMainInfo.setBatchType("3");
        batchPaymentMainInfo.setIsExistInvoice("0");
        batchPaymentMainInfo.setPayStatusReturnURL(paymentBackURL + "/public/pay/mergePaymentBackResult");
        //明细
        List<BatchPaymentDetailInfo> batchPaymentDetailInfo = paymentItemMapper.getPackagePaymentListByBatchNo(batchNo);
        for(BatchPaymentDetailInfo info: batchPaymentDetailInfo){
            info.setCurrency("CNY");
            info.setTaxFee(BigDecimal.ZERO);
        }
        batchPaymentInfo.setBatchPaymentMainInfo(batchPaymentMainInfo);
        batchPaymentInfo.setBatchPaymentDetailInfo(batchPaymentDetailInfo);
        //送收付
        payInfoNoticeThirdPartyCoreSAO.sendMergePayment(dto.getBatchNo(),batchPaymentInfo,batchPaymentDetailInfo);
        MergePaymentVO MergePaymentVO = paymentItemMapper.queryMergePaymentByBatchNo(batchNo);
        String clientName = null;
        String clientBankAccount =  null;
        if(MergePaymentVO != null){
            clientName = MergePaymentVO.getPayeeName();
            clientBankAccount =  MergePaymentVO.getClientBankAccount();
        }
        //操作记录 发起
        BatchPayOperationRecordDTO batchPayOperationRecordDTO = new BatchPayOperationRecordDTO();
        batchPayOperationRecordDTO.setBatchNo(dto.getBatchNo());
        batchPayOperationRecordDTO.setOperationNode("发起");
        batchPayOperationRecordDTO.setPayeeName(clientName);
        batchPayOperationRecordDTO.setPayeeBankAccount(clientBankAccount);
        batchPayOperationRecordDTO.setCreatedBy(WebServletContext.getUserId());
        batchPayOperationRecordDTO.setUpdatedBy(WebServletContext.getUserId());
        batchPayOperationRecordDTO.setDelFlag("N");
        clmBatchPayOperationRecordMapper.addClmBatchPayOperationRecord(batchPayOperationRecordDTO);
    }

    /**
     * 支付修改送支付
     * @param dto
     */
    public void sendBatchPayment(BatchPaymentDTO dto) {
        if(StringUtils.isBlank(dto.getBatchNo())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "批次号不能为空");
        }
        String batchNo = dto.getBatchNo();
        SendBatchPaymentDTO sendBatchPaymentDTO = new SendBatchPaymentDTO();
        //结算收款账号信息
        BatchPayAccountInfo payAccountInfo = new BatchPayAccountInfo();
        bulidPayAccountInfo(payAccountInfo,dto);
        sendBatchPaymentDTO.setPayAccountInfo(payAccountInfo);
        //明细
        List<BatchPaymentDetailInfo> batchPaymentDetailInfo = paymentItemMapper.getPackagePaymentListByBatchNo(batchNo);
        for(BatchPaymentDetailInfo info: batchPaymentDetailInfo){
            info.setCurrency("CNY");
            info.setTaxFee(BigDecimal.ZERO);
        }
        //送收付
        payInfoNoticeThirdPartyCoreSAO.sendBatchPayment(dto.getBatchNo(),sendBatchPaymentDTO,batchPaymentDetailInfo);
    }

    public void bulidPayAccountInfo(BatchPayAccountInfo payAccountInfo, BatchPaymentDTO batchPaymentDTO) {
        payAccountInfo.setSettlementNo(batchPaymentDTO.getBatchNo());
        payAccountInfo.setAccountNo(batchPaymentDTO.getClientBankAccount());
        payAccountInfo.setAccountName(batchPaymentDTO.getClientName());
        //查询账号类型
        MergePaymentVO mergePaymentVO =
                paymentItemMapper.queryMergePaymentByBatchNo(batchPaymentDTO.getBatchNo());
        if("个人".equals(mergePaymentVO.getBankAccountAttribute())){
            payAccountInfo.setBankPayType("1");
        } else {
            payAccountInfo.setBankPayType("0");
        }
        payAccountInfo.setBankCode(batchPaymentDTO.getClientBankCode());
        payAccountInfo.setBankName(batchPaymentDTO.getClientBankName());
        payAccountInfo.setBrachBankName(batchPaymentDTO.getBankDetail());
    }

    @Override
    public void unpackPayment(BatchPaymentDTO dto) {
        if(StringUtils.isBlank(dto.getBatchNo())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "批次号不能为空");
        }
        String batchNo = dto.getBatchNo();
        //主信息
        MergePaymentVO mergePaymentVO = paymentItemMapper.queryMergePaymentByBatchNo(batchNo);
        if("支付成功".equals(mergePaymentVO.getMergePaymentStatus())){
            throw new GlobalBusinessException("该批次号已支付成功，不可解包！");
        }
        //将clms_merge_payment表中merge_payment_status置为90=作废
        paymentItemMapper.updateMergePaymentStatus(dto.getBatchNo(),"90");
        //将clm_payment_item中的id置空
        List<BatchPaymentDetailInfo> infoList = paymentItemMapper.getPackagePaymentListByBatchNo(dto.getBatchNo());
        List<String> list = new ArrayList<>();
        for(BatchPaymentDetailInfo info: infoList){
            list.add(info.getBusinessNo());
        }
        if(!list.isEmpty()){
            paymentItemMapper.updateMergePaymentId(list);
        }

    }

    @Override
    public BatchPaymentVO queryMergePaymentByBatchNo(BatchPaymentDTO dto) {
        if(StringUtils.isBlank(dto.getBatchNo())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "批次号不能为空");
        }
        BatchPaymentVO vo = new BatchPaymentVO();
        MergePaymentVO mergePaymentVO = paymentItemMapper.queryMergePaymentByBatchNo(dto.getBatchNo());
        BeanUtils.copyProperties(mergePaymentVO,vo);
        vo.setDepartmentAbbrCode(mergePaymentVO.getDepartmentCode());
        vo.setDepartmentAbbrName(mergePaymentVO.getDepartmentName());
        vo.setPaymentType(mergePaymentVO.getPaymentUsage());
        List<BatchPayOperationRecordVO> batchPayOperationRecordList =
            clmBatchPayOperationRecordMapper.queryOperationRecordByBatchNo(dto.getBatchNo());
        vo.setBatchPayOperationRecordList(batchPayOperationRecordList);
        return vo;
    }

    @Override
    @Transactional
    public void updateBatchPaymentInfo(BatchPaymentDTO dto) {
        checkUpdateBatchParams(dto);
        String batchNo = dto.getBatchNo();
        //查询该批次号下的支付列表
        List<String> idList = paymentItemMapper.getIdByBatchNo(batchNo);
        if(CollectionUtils.isEmpty(idList)){
            throw new GlobalBusinessException("参数异常");
        }
        //查询该批次号下的收款人信息
        MergePaymentVO mergePaymentVO = paymentItemMapper.queryMergePaymentByBatchNo(batchNo);
        //若修改了收款人姓名，则需要创建任务分类=支付修改审批的任务
        if(!dto.getClientName().equals(mergePaymentVO.getPayeeName())){
            /**
             *  按照机构将任务分配给该机构下角色：理赔管理岗-新
             *  若该机构下无该角色，则自动分配给三星财险总部
             *  若有多个操作人有相同机构的角色权限，则随机分配
             */
            TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
            String gradeName = NcbsConstant.GRADE_MAP.get(BpmConstants.OC_PAY_BACK_MODIFY_REVIEW_BP);
            autoAssignment(taskInfoDTO, gradeName, dto.getDepartmentAbbrCode());
            //生成支付修改审批任务
            bpmService.startProcessOc(dto.getBatchNo(), 1, BpmConstants.OC_PAY_BACK_MODIFY_REVIEW,
                    dto.getBatchNo() +"_REVIEW", taskInfoDTO.getAssigner(),
                    taskInfoDTO.getDepartmentCode());
            String jsonString = JSON.toJSONString(dto);
            //更新理赔合并支付表 clms_merge_payment
            BatchPaymentDTO batchPaymentDTO = new BatchPaymentDTO();
            batchPaymentDTO.setModifyInfo(jsonString);
            batchPaymentDTO.setUpdatedBy(BaseConstant.SYSTEM);
            batchPaymentDTO.setBatchNo(batchNo);
            paymentItemMapper.updateMergePaymentInfo(batchPaymentDTO);
        } else {
            //调用支付接口
            sendBatchPayment(dto);
            //更新支付项目 clm_payment_item
            for(String id: idList){
                PaymentItemDTO itemDTO = getPaymentItemDTO(id);
                BeanUtils.copyProperties(dto, itemDTO);
                itemDTO.setUpdatedBy(WebServletContext.getUserId());
                itemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_11);
                this.updatePaymentItem(itemDTO);
            }
            //更新理赔合并支付表 clms_merge_payment
            dto.setUpdatedBy(WebServletContext.getUserId());
            dto.setMergePaymentStatus(Constants.PAYMENT_ITEM_STATUS_11);
            paymentItemMapper.updateMergePaymentInfo(dto);
            //操作记录 发起
            BatchPayOperationRecordDTO batchPayOperationRecordDTO = new BatchPayOperationRecordDTO();
            batchPayOperationRecordDTO.setBatchNo(dto.getBatchNo());
            batchPayOperationRecordDTO.setOperationNode("发起");
            batchPayOperationRecordDTO.setPayeeName(dto.getClientName());
            batchPayOperationRecordDTO.setPayeeBankAccount(dto.getClientBankAccount());
            batchPayOperationRecordDTO.setCreatedBy(WebServletContext.getUserId());
            batchPayOperationRecordDTO.setUpdatedBy(WebServletContext.getUserId());
            batchPayOperationRecordDTO.setDelFlag("N");
            clmBatchPayOperationRecordMapper.addClmBatchPayOperationRecord(batchPayOperationRecordDTO);
        }
        //操作记录 支付修改审批

    }

    private void autoAssignment(TaskInfoDTO dto, String gradeName, String acceptDepartmentCode) {
        List<String> gradeCodeList = new ArrayList<>();
        dto.setAssigner("");
        dto.setDepartmentCode(acceptDepartmentCode);
        try {
            List<UserGradeInfoDTO> userGradeInfoDTOS = cacheService.querySystemGradeList();
            if (ListUtils.isNotEmpty(userGradeInfoDTOS)) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(gradeName)) {
                    List<String> gradeList = Arrays.asList(gradeName.split(","));
                    for (UserGradeInfoDTO gradeInfo:userGradeInfoDTOS) {
                        for (String grade : gradeList) {
                            if (gradeInfo.getGradeName().equals(grade)){
                                gradeCodeList.add(gradeInfo.getGradeCode());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("initDepartCodeAndAssigner-获取角色列表异常",e);
            return;
        }
        if (ListUtils.isNotEmpty(gradeCodeList)){
            return;
        }
        Set<String> userCodeAll = new HashSet<>();
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setComCode(acceptDepartmentCode);
        for (String gradeCode : gradeCodeList) {
            // 递归 根据机构号和岗位号获取人员userCode
            List<String> userCodes = this.getUserBycomCodeAndGradeCode(userInfoDTO, gradeCode);
            if (ListUtils.isNotEmpty(userCodes)) {
                userCodeAll.addAll(userCodes);
            }
        }
        LogUtil.audit("根据机构号和岗位号获取userCode:{}",JSON.toJSONString(userCodeAll));
        if (userCodeAll.isEmpty()) {
            //如果一直递归到总公司 还是没有找到该岗位的
            dto.setDepartmentCode(ConfigConstValues.HQ_DEPARTMENT);
            return;
        }
        List<String> userCodes = new ArrayList<>(userCodeAll);
        SecureRandom random = new SecureRandom();
        int i = random.nextInt(userCodes.size());
        String userCode = userCodes.get(i);
        dto.setAssigner(userCode);
        dto.setAssigneeName(getUserName(userCode));
        dto.setDepartmentCode(userInfoDTO.getComCode());
        LogUtil.audit("根据机构号和岗位号获取处理人：{}",JSON.toJSONString(dto));
    }

    private String getUserName(String userCode){
        if (BpmConstants.INVESTIGATE_PLATFORM.equals(userCode)) {
            return INVESTIGATE_PLATFORM_NAME;
        }

        if (com.paic.ncbs.claim.common.util.StringUtils.isNotEmpty(userCode) ){
            try {
                UserInfoDTO userInfo = cacheService.queryUserInfo(userCode);
                if (userInfo != null){
                    return userInfo.getUserName();
                }
            }catch (NcbsException e){
                log.info("调用[根据用户编码查询用户信息]接口异常!");
            }
        }
        return "";
    }

    /**
     *
     * 递归 根据机构号和岗位号获取人员userCode
     * @return userCodes
     */
    private List<String> getUserBycomCodeAndGradeCode(UserInfoDTO userInfoDTO,String gradeCode){
        List<String> userCodes = new ArrayList<>();
        try {
            LogUtil.audit("获取处理人入参：userInfoDTO:{},gradeCode:{}", JSON.toJSONString(userInfoDTO),gradeCode);
            List<UserInfoDTO> userInfoDTOS = userCommonService.queryUserInfoList(userInfoDTO.getComCode(), gradeCode);
            LogUtil.audit("获取处理人集合：userInfoDTOS:{}", JSON.toJSONString(userInfoDTOS));
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userInfoDTOS)){
                userCodes = userInfoDTOS.stream().map(UserInfoDTO::getUserCode).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
        }
        if (userCodes.isEmpty()) {
            //因为顶级机构的上级机构是其本身 无法递归终止 要排除
            if (userInfoDTO.getComCode() != null && !ConfigConstValues.HQ_DEPARTMENT.equals(userInfoDTO.getComCode())){
                // 获取上级机构
                DepartmentDTO parentDep = departmentDefineService.queryParentDepartmentInfoByDeptCode(userInfoDTO.getComCode());
                userInfoDTO.setComCode(parentDep.getDepartmentCode());
                userCodes = getUserBycomCodeAndGradeCode(userInfoDTO,gradeCode);
            }
        }
        List<String> accountList = Arrays.stream(tpaAccountId.split(",")).collect(Collectors.toList());
        return userCodes.stream().filter(userCode->!(accountList.contains(userCode) || accountList.contains(userCode.substring(0,3)))).collect(Collectors.toList());
    }

    public void updatePaymentItem(PaymentItemDTO paymentItemDTO) {
        if(null == paymentItemDTO.getUpdatedBy()){
            paymentItemDTO.setUpdatedBy(BaseConstant.SYSTEM);
        }
        paymentItemMapper.updatePaymentItem(paymentItemDTO);
    }

    private PaymentItemDTO getPaymentItemDTO(String idClmPaymentItem) {
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setIdClmPaymentItem(idClmPaymentItem);
        List<PaymentItemDTO> paymentItemList = paymentItemService.getAllPaymentItem(paymentItemDTO);
        if (CollectionUtils.isEmpty(paymentItemList) || paymentItemList.size() > 1){
            throw new GlobalBusinessException("参数异常");
        }
        return paymentItemList.get(CommonConstant.ZERO);
    }


    @Override
    public BatchPaymentDetailResult queryBatchPaymentInfo(BatchPaymentDetailDTO dto) {
        if(StringUtils.isBlank(dto.getDepartmentAbbrCode())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "机构不能为空");
        }
        Pager pager = new Pager();
        pager.setPageIndex(dto.getPager().getPageIndex());
        pager.setPageRows(dto.getPager().getPageRows());
        BatchPaymentDetailResult result = new BatchPaymentDetailResult();
        PageHelper.startPage(dto.getPager().getPageIndex(),dto.getPager().getPageRows(),true);
        List<BatchPaymentDetailVO> resultList = paymentItemMapper.queryBatchPaymentResult(dto);
        PageInfo<BatchPaymentDetailVO> pageInfo =new PageInfo<>(resultList);
        pager.setTotalRows((int)pageInfo.getTotal());
        PageMethod.clearPage();
        result.setBatchPaymentDetailList(resultList);
        result.setPager(pager);
        LogUtil.info("批量支付，查询列表出参={}", JSON.toJSONString(resultList));
        return result;
    }

    public void checkUpdateBatchParams(BatchPaymentDTO dto) {
        if(StringUtils.isBlank(dto.getBatchNo())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "批次号不能为空");
        }
        if(StringUtils.isBlank(dto.getDepartmentAbbrCode())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "机构不能为空");
        }
        if(StringUtils.isBlank(dto.getClientName())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "收款人姓名不能为空");
        }
        if(StringUtils.isBlank(dto.getClientBankAccount())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "银行账号不能为空");
        }
        if(StringUtils.isBlank(dto.getClientBankCode())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "银行大类代码不能为空");
        }
        if(StringUtils.isBlank(dto.getClientBankName())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "银行大类不能为空");
        }
        if(StringUtils.isBlank(dto.getBankDetailCode())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "开户行明细代码不能为空");
        }
        if(StringUtils.isBlank(dto.getBankDetail())){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "开户行明细不能为空");
        }
    }
}
