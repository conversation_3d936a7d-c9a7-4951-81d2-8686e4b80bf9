package com.paic.ncbs.claim.service.common;

public interface RedisService {

    /**
     * 加锁
     * @param key
     * @param value
     * @param expiredTime 过期时间 单位秒
     * @return
     */
    boolean tryLock(String key,String value,Long expiredTime);

    /**
     * 加锁
     * @param key
     * @param value
     * @return
     */
    boolean tryLock(String key,String value);

    /**
     * 释放锁
     * @param key
     * @param value
     * @return
     */
    boolean removeLock(String key,String value);
}
