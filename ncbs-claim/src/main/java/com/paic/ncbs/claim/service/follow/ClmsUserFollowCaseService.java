package com.paic.ncbs.claim.service.follow;

import com.paic.ncbs.claim.model.vo.follow.ClmsUserFollowCaseVO;

import java.util.List;

/**
 * <p>
 * 关注表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
public interface ClmsUserFollowCaseService {

    /**
     * 关注/取消关注
     * @param caseVO
     */
    void followUnfollow(ClmsUserFollowCaseVO caseVO);

    /**
     * 获取当前用户关注案件列表
     * @return
     */
    List<ClmsUserFollowCaseVO> getClmsUserFollowCase();

    /**
     * 获取案件关注状态
     * @param reportNo
     * @param caseTimes
     * @return
     */
    ClmsUserFollowCaseVO getCaseFollowStatus(String reportNo, Integer caseTimes);

}
