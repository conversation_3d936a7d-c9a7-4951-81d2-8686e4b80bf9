package com.paic.ncbs.claim.service.report.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.PlanTermContentEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.*;
import com.paic.ncbs.claim.dao.mapper.checkloss.ChannelProcessMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonAccidentMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonDiagnoseMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonHospitalMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.*;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyRecordMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePlanMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.pet.PetInjureExMapper;
import com.paic.ncbs.claim.dao.mapper.pet.PetInjureMapper;
import com.paic.ncbs.claim.dao.mapper.pet.ReportAccidentPetMapper;
import com.paic.ncbs.claim.dao.mapper.report.*;
import com.paic.ncbs.claim.dao.mapper.riskppt.RiskPropertyMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyClaimCaseMapper;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.dto.report.ReportDomainDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.secondunderwriting.ClmsPolicyHistoryUwInfoDTO;
import com.paic.ncbs.claim.service.report.TelReportTransationService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsPolicyHistoryUwInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

@Service("telReportTransationService")
public class TelReportTransactionServiceImpl implements TelReportTransationService {
    @Autowired
    private CaseProcessMapper caseProcessMapper;
    @Autowired
    private CaseClassMapper caseClassMapper;
    @Autowired
    private PolicyClaimCaseMapper policyClaimCaseMapper;
    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordMapper;
    @Autowired
    private EstimatePolicyMapper estimatePolicyMapper;
    @Autowired
    private EstimatePlanMapper estimatePlanMapper;
    @Autowired
    private EstimateDutyMapper estimateDutyMapper;
    @Autowired
    private CaseBaseMapper caseBaseMapper;
    @Autowired
    private ChannelProcessMapper channelProcessMapper;
    @Autowired
    private PersonAccidentMapper personAccidentMapper;
    @Autowired
    private PersonDiagnoseMapper personDiagnoseMapper;
    @Autowired
    private PersonHospitalMapper personHospitalMapper;
    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;
    @Autowired
    private ReportAccidentMapper reportAccidentMapper;
    @Autowired
    private ReportAccidentExMapper reportAccidentExMapper;
    @Autowired
    private LinkManMapper linkManMapper;
    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private WholeCaseBaseExMapper wholeCaseBaseExMapper;
    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;
    @Autowired
    private AhcsPolicyInfoMapper policyInfoMapper;
    @Autowired
    private AhcsInsuredPresonMapper insuredPersonMapper;
    @Autowired
    private AhcsInsuredPersonExtMapper insuredPersonExtMapper;
    @Autowired
    private AhcsSpecialPromiseMapper specialPromiseMapper;
    @Autowired
    private AhcsPolicyHolderMapper holderMapper;
    @Autowired
    private AhcsCoinsureMapper coinsureMapper;
    @Autowired
    private AhcsPolicyPlanMapper planMapper;
    @Autowired
    private AhcsPolicyDutyMapper dutyMapper;
    @Autowired
    private AhcsPolicyDutyDetailMapper dutyDetailMapper;
    @Autowired
    private DutyAttributeMapper dutyAttributeMapper;
    @Autowired
    private DutyAttributeDetailMapper dutyAttributeDetailMapper;
    @Autowired
    private AhcsPolicyPlanDataMapper ahcsPolicyPlanDataMapper;
    @Autowired
    private ReportAccidentPetMapper reportAccidentPetMapper;
    @Autowired
    private PetInjureMapper petInjureMapper;
    @Autowired
    private PetInjureExMapper petInjureExMapper;

    @Autowired
    private ClmsPolicyHistoryUwInfoService clmsPolicyHistoryUwInfoService;

    @Autowired
    private RiskPropertyMapper riskPropertyMapper;

    @Transactional
    @Override
    public void insertReportDomain(ReportDomainDTO reportDomain) {
        reportInfoMapper.insert(reportDomain.getReportInfo());
        reportInfoExMapper.insert(reportDomain.getReportInfoExs());
        reportAccidentMapper.insert(reportDomain.getReportAccident());
        reportAccidentExMapper.insert(reportDomain.getReportAccidentEx());
        caseBaseMapper.insertList(reportDomain.getCaseBases());
        linkManMapper.insertList(reportDomain.getLinkMans());
        wholeCaseBaseMapper.insert(reportDomain.getWholeCaseBase());
        wholeCaseBaseExMapper.insert(reportDomain.getWholeCaseBaseEx());
        reportCustomerInfoMapper.insert(reportDomain.getReportCustomerInfo());
        policyInfoMapper.insertList(reportDomain.getPolicyInfoList());
        insuredPersonMapper.insertList(reportDomain.getInsuredPresonList());
        insuredPersonExtMapper.insertList(reportDomain.getInsuredPresonExList());
        if(ListUtils.isNotEmpty(reportDomain.getSpecialPromiseList())){
            specialPromiseMapper.insertBatch(reportDomain.getSpecialPromiseList());
        }
        holderMapper.insertBatch(reportDomain.getPolicyHolderList());
        if(ListUtils.isNotEmpty(reportDomain.getCoinsureList())){
            coinsureMapper.insertBatch(reportDomain.getCoinsureList());
        }
        
        planMapper.insertList(reportDomain.getPolicyPlanList());
        dutyMapper.insertList(reportDomain.getPolicyDutyList());
        dutyDetailMapper.insertBatch(reportDomain.getPolicyDutyDetailList());
        if(ListUtils.isNotEmpty(reportDomain.getDutyAttributeList())){
            dutyAttributeMapper.insertList(reportDomain.getDutyAttributeList());
        }
        if(ListUtils.isNotEmpty(reportDomain.getDutyAttributeDetailList())){
            dutyAttributeDetailMapper.insertBatch(reportDomain.getDutyAttributeDetailList());
        }
        caseProcessMapper.addCaseProcess(reportDomain.getCaseProcessDTO());
        caseClassMapper.addCaseClassList(reportDomain.getCaseClassList(),1, ConstValues.SYSTEM);
        policyClaimCaseMapper.addBatchPolicyClaimCase(reportDomain.getPolicyClaimCaseList());
        estimateDutyRecordMapper.addEstimateDutyRecordList(reportDomain.getEstimateDutyRecordList());
        estimatePolicyMapper.addBatchEstimatePolicy(reportDomain.getPolicyList());
        estimatePlanMapper.addBatchEstimatePlan(reportDomain.getPlanList());
        estimateDutyMapper.addBatchEstimateDuty(reportDomain.getDutyList());
        channelProcessMapper.saveChannelProcess(reportDomain.getChannelProcess());
        personAccidentMapper.savePersonAccident(reportDomain.getPersonAccidentDTO());

        //保存小条款

        List<PlanTermContentEntity> list =  reportDomain.getPlanTermContentEntityList();
        if(CollectionUtil.isNotEmpty(list)){
            ahcsPolicyPlanDataMapper.insertTermList(list);
        }
        //保存抄单核保信息
        //保存核保信息
        savePolicyHistoryInfo(reportDomain);


        if(reportDomain.getReportAccidentPet() != null){
            reportAccidentPetMapper.insert(reportDomain.getReportAccidentPet());
        }
        if(reportDomain.getPetInjureDTO() != null){
            petInjureMapper.addPetInjure(reportDomain.getPetInjureDTO());
        }
        if(reportDomain.getPetInjureExList() != null){
            petInjureExMapper.addPetInjureExList(reportDomain.getPetInjureExList());
        }

        // 保存理赔标的信息
        List<CaseRiskPropertyDTO> riskPropertyList = reportDomain.getOnlineReportVO().getRiskPropertyList();
        if(ListUtils.isNotEmpty(riskPropertyList)){
            boolean allMatch = riskPropertyList.stream()
                    .allMatch(riskProperty -> StringUtils.hasText(riskProperty.getReportNo())
                            && StringUtils.hasText(riskProperty.getRiskGroupNo())
                            && StringUtils.hasText(riskProperty.getRiskGroupType()));
            if(allMatch){
                // 理赔标的信息表 CLMS_RISK_PROPERTY_CASE
                riskPropertyMapper.saveCaseRiskPropertyList(riskPropertyList);
            }
        }
    }

    /**
     * 保存保单历史核保信息
     * @param reportDomain
     */
    private void savePolicyHistoryInfo(ReportDomainDTO reportDomain) {
        List<AhcsPolicyDomainDTO> policyDomainDTOS = reportDomain.getAhcsPolicyDomainDTOs();
        try{
            for (AhcsPolicyDomainDTO dto : policyDomainDTOS) {
                List<ClmsPolicyHistoryUwInfoDTO> policyHistoryUwInfoDTOList = dto.getPolicyHistoryUwInfoDTOList();
                if(ObjectUtil.isNotNull(policyHistoryUwInfoDTOList) && policyHistoryUwInfoDTOList.size()>=1){
                    clmsPolicyHistoryUwInfoService.saveBatch(policyHistoryUwInfoDTOList,reportDomain.getReportNo());
                }
            }
            clmsPolicyHistoryUwInfoService.saveClaimPolicyUwInfo(reportDomain.getQueryUwInfoPolicys());
        }catch (Exception e){
            LogUtil.info("线上报案保存历史核保信息异常：",e.getCause());
        }


    }

}
