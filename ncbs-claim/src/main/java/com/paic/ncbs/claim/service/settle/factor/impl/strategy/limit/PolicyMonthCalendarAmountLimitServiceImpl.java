package com.paic.ncbs.claim.service.settle.factor.impl.strategy.limit;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.paic.ncbs.claim.common.util.BigDecimalUtils;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.report.QueryAccidentVo;
import com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.DetailSettleReasonTemplateDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.EverySettleTemplateDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto;
import com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.ExtendedLimitService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 定制化产品方案月赔付次数实现
 */
@Slf4j
@Service
@Order(2)
@RefreshScope
public class PolicyMonthCalendarAmountLimitServiceImpl implements ExtendedLimitService {

    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;

    /**
     * 保单月限额配置方案
     */
    @Value("#{${policyLimit.monthCalendarAmount}}")
    private Map<String, BigDecimal> policyMonthCalendarAmountMap;

    /**
     * 执行保单是否匹配的本扩展
     *
     * @param policyPayDTO
     * @return
     */
    @Override
    public boolean isMatch(PolicyPayDTO policyPayDTO) {
        return policyMonthCalendarAmountMap != null && policyMonthCalendarAmountMap.containsKey(policyPayDTO.getProductPackage());
    }

    @Override
    public void cumulativeLimit(PolicyPayDTO policyPayDTO) {
        if (!isMatch(policyPayDTO)) {
            return;
        }
        log.info("案件:{},配置了自然月限额开始处理！", policyPayDTO.getReportNo());
        List<DutyBillLimitInfoDTO> policyAllBillLimits = new ArrayList<>();
        List<BIllSettleResultDTO> bIllSettleResultDTOLists = new ArrayList<>();
        List<String> planList = new ArrayList<>();
        List<PlanPayDTO> plans = policyPayDTO.getPlanPayArr();
        List<String> shareDutyList = new ArrayList<>();
        plans.forEach(planPayDTO -> {
            List<DutyPayDTO> dutyPayDTOS = planPayDTO.getDutyPayArr();
            planList.add(planPayDTO.getPlanCode());
            dutyPayDTOS.forEach(dutyPayDTO -> {
                shareDutyList.add(dutyPayDTO.getDutyCode());
                List<DutyDetailPayDTO> details = dutyPayDTO.getDutyDetailPayArr();
                details.forEach(detailPayDTO -> {
                    if (CollectionUtil.isNotEmpty(detailPayDTO.getDutyBillLimitInfoDTOList())) {
                        for (DutyBillLimitInfoDTO dutyBillLimitInfoDTO : detailPayDTO.getDutyBillLimitInfoDTOList()) {
                            if (dutyBillLimitInfoDTO.getMonth() != -1) {
                                policyAllBillLimits.add(dutyBillLimitInfoDTO);
                            }
                        }
                    }
                    if (CollectionUtil.isNotEmpty(detailPayDTO.getBillSettleResultDTOList())) {
                        bIllSettleResultDTOLists.addAll(detailPayDTO.getBillSettleResultDTOList());
                    }
                });
            });
        });
        log.info("报案号={}下所有限额数据={}", policyPayDTO.getReportNo(), JsonUtils.toJsonString(policyAllBillLimits));
        checkData(policyAllBillLimits, planList, shareDutyList, policyPayDTO, bIllSettleResultDTOLists);

    }

    private PolicyMonthDto getMonthDto(List<PolicyMonthDto> monthDtoList, Date key) {
        List<PolicyMonthDto> relist = monthDtoList.stream().filter(policyMonthDto -> Objects.equals(key, policyMonthDto.getStartDate())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(relist)){
            return relist.get(0);
        }else {
            PolicyMonthDto monthDto = new PolicyMonthDto();
            monthDto.setStartDate(key);
            relist.add(monthDto);
            return monthDto;
        }

    }

    private void setEverySetttleListValue(List<EverySettleTemplateDTO> everySetttleList, Date billDate, String flag, BigDecimal amount) {
        if (CollectionUtil.isEmpty(everySetttleList)) {
            return;
        }
        String strbillDate = DateUtils.dateFormat(billDate, DateUtils.SIMPLE_DATE_STR);
        for (EverySettleTemplateDTO e : everySetttleList) {
            if (Objects.equals(e.getStrBillDate(), strbillDate)) {
                e.setExceedMonthLimit(flag);
                e.setExceedMonthLimitAmount(BigDecimalUtils.toString(amount));
            }
        }
    }

    /**
     * 更新责任明细发票数据
     *
     * @param dto
     * @param bIllSettleResultDTOLists
     */
    private void updateBillSettleResult(DutyBillLimitInfoDTO dto, List<BIllSettleResultDTO> bIllSettleResultDTOLists) {
        if (Objects.equals("0", dto.getMoreThanMonthLimit())) {
            if (CollectionUtil.isNotEmpty(bIllSettleResultDTOLists)) {
                List<BIllSettleResultDTO> resultDTOS = bIllSettleResultDTOLists.stream().filter(bIllSettleResultDTO -> Objects.equals(bIllSettleResultDTO.getPolicyNo(), dto.getPolicyNo()) && Objects.equals(bIllSettleResultDTO.getPlanCode(), dto.getPlanCode()) && Objects.equals(bIllSettleResultDTO.getDutyCode(), dto.getDutyCode()) && Objects.equals(bIllSettleResultDTO.getBillDate(), dto.getBillDate())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(resultDTOS)) {
                    resultDTOS.forEach(bIllSettleResultDTO -> {
                        String remark = StringUtils.isEmptyStr(bIllSettleResultDTO.getRemark()) ? "" : bIllSettleResultDTO.getRemark();
                        bIllSettleResultDTO.setAutoSettleAmount(BigDecimal.ZERO);
                        bIllSettleResultDTO.setMonthLimit("Y");
                        StringBuffer sremark = new StringBuffer();
                        try {
                            sremark.append(remark).append("发票日:").append(DateUtils.parseToFormatString(dto.getBillDate(), DateUtils.SIMPLE_DATE_STR)).append("所在自然月").append("累计已超月限额本次本日发票可赔付金额为0");
                        } catch (Exception e) {
                            log.error("月限额发票备注拼接异常，reportNo:{}", dto.getReportNo(), e);
                        }
                        bIllSettleResultDTO.setRemark(sremark.toString());
                        log.info("报案号={},发票日={},所在自然月已超月限额 该发票日 责任明细发票理算数据为0", dto.getReportNo(), dto.getBillDate());
                    });
                }
            }
        }
        if (Objects.equals("1", dto.getMoreThanMonthLimit())) {
            List<BIllSettleResultDTO> resultDTOS = bIllSettleResultDTOLists.stream().filter(bIllSettleResultDTO -> Objects.equals(bIllSettleResultDTO.getPolicyNo(), dto.getPolicyNo()) && Objects.equals(bIllSettleResultDTO.getPlanCode(), dto.getPlanCode()) && Objects.equals(bIllSettleResultDTO.getDutyCode(), dto.getDutyCode()) && Objects.equals(bIllSettleResultDTO.getBillDate(), dto.getBillDate())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(resultDTOS)) {
                BigDecimal amount = dto.getSettleClaimAmount();
                for (BIllSettleResultDTO billSettleDto : resultDTOS) {
                    String remark = StringUtils.isEmptyStr(billSettleDto.getRemark()) ? "" : billSettleDto.getRemark();
                    if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                        billSettleDto.setAutoSettleAmount(BigDecimal.ZERO);
                        billSettleDto.setMonthLimit("Y");
                        StringBuffer sremark = new StringBuffer();
                        sremark.append(remark).append("剩余月限额分配到这张发票金额为0,赔付金额为0");
                        billSettleDto.setRemark(sremark.toString());
                        log.info("报案号={},发票日={},所在自然月剩余月限额分配到这张发票金额为0", dto.getReportNo(), dto.getBillDate());

                    } else {
                        if (billSettleDto.getAutoSettleAmount().compareTo(amount) <= 0) {
                            amount = amount.subtract(billSettleDto.getAutoSettleAmount());
                        } else {
                            billSettleDto.setAutoSettleAmount(amount);
                            billSettleDto.setMonthLimit("Y");
                            StringBuffer sremark = new StringBuffer();
                            sremark.append(remark).append("剩余月限额分配到这张发票金额为").append(amount).append("可赔付金额为").append(amount);
                            billSettleDto.setRemark(sremark.toString());
                            log.info("报案号={},发票日={},所在自然月剩余月限额分配到这张发票金额为={}", dto.getReportNo(), dto.getBillDate(), amount);

                        }
                    }
                }
            }
        }
    }

    /**
     * 月限额处理
     *
     * @param planList
     * @param policyPayDTO
     */
    private void checkData(List<DutyBillLimitInfoDTO> policyAllBillLimits, List<String> planList, List<String> shareDutyList, PolicyPayDTO policyPayDTO, List<BIllSettleResultDTO> bIllSettleResultDTOLists) {
        Map<String, List<DutyBillLimitInfoDTO>> strDutyCodeListMap = policyAllBillLimits.stream().collect(Collectors.groupingBy(DutyBillLimitInfoDTO::getDutyCode));
        List<PolicyMonthDto> usedMonthList = getUseMonthList(policyAllBillLimits, planList, shareDutyList, policyPayDTO);
        for (Map.Entry<String, List<DutyBillLimitInfoDTO>> dutyentry : strDutyCodeListMap.entrySet()) {
            List<DutyBillLimitInfoDTO> dutyBillList = dutyentry.getValue();
            log.info("报案号={}，责任限额数据={}", policyPayDTO.getReportNo(), JsonUtils.toJsonString(dutyBillList));
            Map<Date, List<DutyBillLimitInfoDTO>> dateListMap = dutyBillList.stream().collect(Collectors.groupingBy(
                    // 分组条件：获取该月的第一天作为Date类型的key
                    dto -> {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(dto.getBillDate());
                        cal.set(Calendar.DAY_OF_MONTH, 1);
                        cal.set(Calendar.HOUR_OF_DAY, 0);
                        cal.set(Calendar.MINUTE, 0);
                        cal.set(Calendar.SECOND, 0);
                        cal.set(Calendar.MILLISECOND, 0);
                        return cal.getTime();
                    },
                    // 使用TreeMap保证升序排序
                    TreeMap::new,
                    // 收集到List中
                    Collectors.toList()
            ));
            BigDecimal sumAutoAmount = BigDecimal.ZERO;
            Map<String, DetailSettleReasonTemplateDTO> reasonMap = null;
            DutyDetailPayDTO dutyDetailPayDTO = null;
            for (Map.Entry<Date, List<DutyBillLimitInfoDTO>> integerEntry : dateListMap.entrySet()) {
                List<DutyBillLimitInfoDTO> integerBillList = integerEntry.getValue();
                log.info("报案号={},责任限额自然月数据={}", policyPayDTO.getReportNo(), JsonUtils.toJsonString(integerBillList));
                List<PolicyMonthDto> monthDtos = usedMonthList.stream().filter(policyMonthDto -> Objects.equals(policyMonthDto.getStartDate(), integerEntry.getKey())).collect(Collectors.toList());
                PolicyMonthDto monthDto = monthDtos.get(0);
                //自然月已使用的额度
                log.info("报案号={},责任编码={},责任限额数据所在自然月={},剩余月限额={}", policyPayDTO.getReportNo(), dutyentry.getKey(), JsonUtils.toJsonString(monthDto), JsonUtils.toJsonString(monthDto.getAmount()));
                for (DutyBillLimitInfoDTO dutybilllimit : integerBillList) {
                    List<PlanPayDTO> planReasonList = policyPayDTO.getPlanPayArr().stream().filter(planPayDTO -> Objects.equals(dutybilllimit.getPlanCode(), planPayDTO.getPlanCode())).collect(Collectors.toList());
                    List<DutyPayDTO> dutyResons = planReasonList.get(0).getDutyPayArr();
                    List<DutyPayDTO> dutyReason = dutyResons.stream().filter(dutyPayDTO -> Objects.equals(dutybilllimit.getDutyCode(), dutyPayDTO.getDutyCode())).collect(Collectors.toList());
                    List<DutyDetailPayDTO> detailPayDTOS = dutyReason.get(0).getDutyDetailPayArr();
                    if (Objects.isNull(dutyDetailPayDTO)) {
                        dutyDetailPayDTO = detailPayDTOS.get(0);
                    }
                    DetailSettleReasonTemplateDTO detailSettleReasonTemplateDTO = detailPayDTOS.get(0).getDetailSettleReasonTemplateDTO();
                    List<EverySettleTemplateDTO> everySetttleList = detailSettleReasonTemplateDTO.getEverySetttleList();
                    if (Objects.isNull(reasonMap)) {
                        reasonMap = new HashMap<>();
                        reasonMap.put(dutybilllimit.getDutyCode(), detailSettleReasonTemplateDTO);
                    }
                    if (monthDto.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        dutybilllimit.setSettleClaimAmount(BigDecimal.ZERO);
                        monthDto.setAmount(BigDecimal.ZERO);
                        detailPayDTOS.get(0).setAutoSettleAmount(BigDecimal.ZERO);//定制化开发所以取0，如有多个明细的产品 不能这么处理
                        setEverySetttleListValue(everySetttleList, dutybilllimit.getBillDate(), "0", BigDecimal.ZERO);
                        dutybilllimit.setMoreThanMonthLimit("0");
                    } else {
                        if (monthDto.getAmount().compareTo(dutybilllimit.getSettleClaimAmount()) <= 0) {
                            dutybilllimit.setSettleClaimAmount(monthDto.getAmount());
                            detailPayDTOS.get(0).setAutoSettleAmount(monthDto.getAmount());
                            setEverySetttleListValue(everySetttleList, dutybilllimit.getBillDate(), "1", monthDto.getAmount());
                            sumAutoAmount = sumAutoAmount.add(monthDto.getAmount());
                            monthDto.setAmount(BigDecimal.ZERO);
                            dutybilllimit.setMoreThanMonthLimit("1");
                        } else {
                            BigDecimal resude = monthDto.getAmount().subtract(dutybilllimit.getSettleClaimAmount());
                            sumAutoAmount = sumAutoAmount.add(dutybilllimit.getSettleClaimAmount());
                            monthDto.setAmount(resude);
                        }
                    }
                    updateBillSettleResult(dutybilllimit, bIllSettleResultDTOLists);
                }


            }
            //每个责任的总金额
            dutyDetailPayDTO.setAutoSettleAmount(sumAutoAmount);
            if (Objects.nonNull(reasonMap)) {
                for (Map.Entry<String, DetailSettleReasonTemplateDTO> entryDto : reasonMap.entrySet()) {
                    entryDto.getValue().setAutoSettleAmount(BigDecimalUtils.toString(sumAutoAmount));
                }
            }

        }

        log.info("报案号={}，日限额计算={}", policyPayDTO.getReportNo(), JsonUtils.toJsonString(policyAllBillLimits));

    }

    private List<PolicyMonthDto> getUseMonthList(List<DutyBillLimitInfoDTO> policyAllBillLimits, List<String> planList, List<String> shareDutyList, PolicyPayDTO policyPayDTO) {
        List<PolicyMonthDto> monthDtos = new ArrayList<>();
//        List<PolicyMonthDto> monthDtoList = policyPayDTO.getMonthDtoList();
//        Map<Integer, List<DutyBillLimitInfoDTO>> integerListMap = policyAllBillLimits.stream().sorted(Comparator.comparing(DutyBillLimitInfoDTO::getBillDate)).collect(Collectors.groupingBy(i -> i.getBillDate().getMonth() + 1));
        Map<Date, List<DutyBillLimitInfoDTO>> dateListMap = policyAllBillLimits.stream()
                .collect(Collectors.groupingBy(
                        // 分组条件：获取该月的第一天作为Date类型的key
                        dto -> {
                            Calendar cal = Calendar.getInstance();
                            cal.setTime(dto.getBillDate());
                            cal.set(Calendar.DAY_OF_MONTH, 1);
                            cal.set(Calendar.HOUR_OF_DAY, 0);
                            cal.set(Calendar.MINUTE, 0);
                            cal.set(Calendar.SECOND, 0);
                            cal.set(Calendar.MILLISECOND, 0);
                            return cal.getTime();
                        },
                        // 使用TreeMap保证升序排序
                        TreeMap::new,
                        // 收集到List中
                        Collectors.toList()
                ));
        List<PolicyMonthDto> usedMonthList = new ArrayList<>();
        BigDecimal monthLimit = BigDecimal.ZERO;
        if (Objects.nonNull(policyMonthCalendarAmountMap) && policyMonthCalendarAmountMap.containsKey(policyPayDTO.getProductPackage())) {
            monthLimit = policyMonthCalendarAmountMap.get(policyPayDTO.getProductPackage());
        }
        for (Map.Entry<Date, List<DutyBillLimitInfoDTO>> entry : dateListMap.entrySet()) {
            PolicyMonthDto monthDto = getMonthDto(monthDtos, entry.getKey());
            DutyLimitQueryVo dutyLimitQueryVo = new DutyLimitQueryVo();
            dutyLimitQueryVo.setPolicyNo(policyPayDTO.getPolicyNo());
            dutyLimitQueryVo.setReportNo(policyPayDTO.getReportNo());
            dutyLimitQueryVo.setSatrtDate(entry.getKey());
            dutyLimitQueryVo.setEndDate(DateUtil.endOfMonth(entry.getKey()));
            List<DutyBillLimitDto> list = dutyBillLimitInfoMapper.getPolicyAlreadyPayTimes(dutyLimitQueryVo);
            if (CollectionUtil.isEmpty(list)) {
                //初始化剩余月限额为配置的30块钱
                monthDto.setAmount(monthLimit);
            } else {
                //按报案号分组
                Map<String, List<DutyBillLimitDto>> usedListMap = list.stream().collect(Collectors.groupingBy(DutyBillLimitDto::getReportNo));
                BigDecimal usedLimitAmount = BigDecimal.ZERO;
                for (Map.Entry<String, List<DutyBillLimitDto>> en : usedListMap.entrySet()) {
                    if (policyPayDTO.getCaseTimes() > 1) {
                        if (Objects.equals(policyPayDTO.getReportNo(), en.getKey())) {
                            continue;
                        }
                    }
                    List<DutyBillLimitDto> casetimelist = en.getValue();
                    //按赔付次分组
                    Map<Integer, List<DutyBillLimitDto>> caseTimeMap = casetimelist.stream().sorted(Comparator.comparing(DutyBillLimitDto::getCaseTimes).reversed()).collect(Collectors.groupingBy(DutyBillLimitDto::getCaseTimes, LinkedHashMap::new, Collectors.toList()));
                    for (Map.Entry<Integer, List<DutyBillLimitDto>> ent : caseTimeMap.entrySet()) {
                        List<DutyBillLimitDto> dutyList = ent.getValue();
                        for (DutyBillLimitDto dto : dutyList) {
                            usedLimitAmount = usedLimitAmount.add(dto.getSettleClaimAmount());
                        }
                        break;
                    }
                }

                BigDecimal reduceLimit = monthLimit.subtract(usedLimitAmount);
                if (reduceLimit.compareTo(BigDecimal.ZERO) <= 0) {
                    monthDto.setAmount(BigDecimal.ZERO);
                } else {
                    monthDto.setAmount(reduceLimit);
                }
            }
            usedMonthList.add(monthDto);
        }
        return usedMonthList;
    }

}
