package com.paic.ncbs.claim.sao.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.model.dto.reinsurance.*;
import com.paic.ncbs.claim.sao.AbstractBaseSAO;
import com.paic.ncbs.claim.sao.ReinsuranceSAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@RefreshScope
@Service
public class ReinsuranceSAOImpl extends AbstractBaseSAO implements ReinsuranceSAO {

    @Value("${switch.mesh}")
    private Boolean switchMesh;

    @Override
    public ReinsuranceRespDTO repayCal(IntfClaimMainDTO dto) {
        log.info("ReinsuranceSAO.repayCal, url: {}, params: {}", thirdServiceConfig.getRepayCalUrl(), JSON.toJSONString(dto));

        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        HttpEntity<IntfClaimMainDTO> httpEntity = new HttpEntity<>(dto, header);
        ReinsuranceRespDTO respDto;
        if (switchMesh){
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset:utf-8");
            String result = MeshSendUtils.post(thirdServiceConfig.getRepayCalUrl(), JSON.toJSONString(dto),headers);
            respDto = JSON.parseObject(result,ReinsuranceRespDTO.class);
        }else {
            respDto = restTemplate.postForObject(thirdServiceConfig.getRepayCalUrl(), httpEntity, ReinsuranceRespDTO.class);
        }
        log.info("ReinsuranceSAO.repayCal, result: {}", JSON.toJSONString(respDto));
        return respDto;
    }
    /**
     * 查询再保信息
     */
    public List<ReinsuranceRateDTO> queryRepayCal(ReinsuranceReqDTO dto){
        log.info("ReinsuranceSAO.queryRepayCal, url: {}, params: {}", thirdServiceConfig.getQueryRepayCalUrl(),JSON.toJSONString(dto));
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        HttpEntity<ReinsuranceReqDTO> httpEntity = new HttpEntity<>(dto, header);
        List<ReinsuranceRateDTO> respDto = new ArrayList<>();
        String result = "";
        if (switchMesh) {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset:utf-8");
            result = MeshSendUtils.post(thirdServiceConfig.getQueryRepayCalUrl() , JSON.toJSONString(dto), headers);
        } else {
            result = restTemplate.postForObject(thirdServiceConfig.getQueryRepayCalUrl(), httpEntity, String.class);
        }
        if (StringUtils.isNotEmpty(result)) {
            JSONObject jsonObject = JSON.parseObject(result);
            String status = jsonObject.getString("status");
            if ("0".equals(status)) {
                Object data = jsonObject.get("data");
                if(data instanceof JSONArray){
                    JSONArray dataTemp = jsonObject.getJSONArray("data");
                    if(dataTemp!=null && !dataTemp.isEmpty()){
                        respDto = dataTemp.toJavaList(ReinsuranceRateDTO.class);
                    }
                }
            }
        }
        log.info("ReinsuranceSAO.queryRepayCal, result: {}", JSON.toJSONString(result));
        return respDto;
    }
    public List<ReinsurerDTO> queryReinsurer(ReinsuranceReqDTO dto){
        log.info("ReinsuranceSAO.queryReinsurer, url: {}, params: {}", thirdServiceConfig.getQueryReinsurerUrl(),JSON.toJSONString(dto));
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        HttpEntity<ReinsuranceReqDTO> httpEntity = new HttpEntity<>(dto, header);
        List<ReinsurerDTO> respDto = new ArrayList<>();
        String result;
        if (switchMesh) {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset:utf-8");
            result = MeshSendUtils.post(thirdServiceConfig.getQueryReinsurerUrl() , JSON.toJSONString(dto), headers);
        } else {
            result = restTemplate.postForObject(thirdServiceConfig.getQueryReinsurerUrl(), httpEntity, String.class);
        }

        if (StringUtils.isNotEmpty(result)) {
            JSONObject jsonObject = JSON.parseObject(result);
            String status = jsonObject.getString("status");
            if ("0".equals(status)) {
                Object data = jsonObject.get("data");
                if(data instanceof JSONArray){
                    JSONArray dataTemp = jsonObject.getJSONArray("data");
                    if(dataTemp!=null && !dataTemp.isEmpty()){
                        respDto = dataTemp.toJavaList(ReinsurerDTO.class);
                    }
                }
            }
        }
        log.info("ReinsuranceSAO.queryReinsurer, result: {}", JSON.toJSONString(result));
        return respDto;
    }
    public String queryRepolicyNo(ReinsuranceReqDTO dto){
        log.info("ReinsuranceSAO.queryRepolicyNo, url: {}, params: {}", thirdServiceConfig.getQueryRepolicyNoUrl(),JSON.toJSONString(dto));
        String rePolicyNo =  "";
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        HttpEntity<ReinsuranceReqDTO> httpEntity = new HttpEntity<>(dto, header);
        List<ReinsurerDTO> respDto = new ArrayList<>();
        String result;
        if (switchMesh) {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset:utf-8");
            result = MeshSendUtils.post(thirdServiceConfig.getQueryRepolicyNoUrl() , JSON.toJSONString(dto), headers);
        } else {
            result = restTemplate.postForObject(thirdServiceConfig.getQueryRepolicyNoUrl(), httpEntity, String.class);
        }

        if (StringUtils.isNotEmpty(result)) {
            JSONObject jsonObject = JSON.parseObject(result);
            String status = jsonObject.getString("status");
            if ("0".equals(status)) {
                JSONObject dataObj = jsonObject.getJSONObject("data");
                if (dataObj != null){
                    rePolicyNo = dataObj.getString("recertifyNo");
                }
            }
        }
        log.info("ReinsuranceSAO.queryRepolicyNo, result: {}", JSON.toJSONString(result));
        return rePolicyNo;
    }
}
