package com.paic.ncbs.claim.model.dto.print;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 再保账单打印
 */
@Data
public class PrintReinsuranceDTO {
    /**
     * 再保公司中文名称
     */
    private String companyCname;
    /**
     * 再保公司英文名称
     */
    private String companyEname;
    /**
     * 经办人
     */
    private String handler;
    //公司电话
    private String companyTel;
    /**
     * 公司传真
     */
    private String companyFax;
    //报案号
    private String reportNo;
    /**
     * 案件次数
     */
    private Integer caseTimes;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 投保人名称
     */
    private String insuredName;
    /**
     * 保单号
     */
    private String policyNo;
    /**
     * 保单起期
     */
    private String insuranceBeginDate;
    /**
     * 保单止期
     */
    private String insuranceEndDate;
    /**
     * 总保额
     */
    private BigDecimal amount;
    /**
     * 出险日期
     */
    private String accidentDate;
    /**
     * 出险原因
     */
    private String accidentReason;
    /**
     * 币种
     */
    private String currency;
    /**
     * 再保人份额
     */
    private BigDecimal share;
    /**
     * 未决赔款+未决费用
     */
    private BigDecimal lossAmount;
    //份额内的未决赔款+费用
    private BigDecimal shareOfLoss;
    //备注
    private String remarks;
    /**
     * 提交人
     */
    private String submitter;
    /**
     * 打印时间
     */
    private String printTime;
    /**
     * 文档组项ID
     */
    private String documentGroupItemsId;
    /**
     * 打印模板编码
     */
    private String templateCode;

}
