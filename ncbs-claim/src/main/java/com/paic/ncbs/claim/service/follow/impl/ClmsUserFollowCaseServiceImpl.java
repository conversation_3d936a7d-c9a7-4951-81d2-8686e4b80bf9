package com.paic.ncbs.claim.service.follow.impl;

import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.dao.entity.follow.ClmsUserFollowCase;
import com.paic.ncbs.claim.dao.mapper.follow.ClmsUserFollowCaseMapper;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.vo.follow.ClmsUserFollowCaseVO;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.follow.ClmsUserFollowCaseService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 关注表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@Service
public class ClmsUserFollowCaseServiceImpl implements ClmsUserFollowCaseService {

    @Autowired
    private ClmsUserFollowCaseMapper caseMapper;

    @Autowired
    private CaseProcessService caseProcessService;

    @Override
    public void followUnfollow(ClmsUserFollowCaseVO caseVO) {
        String userId = WebServletContext.getUserId();
        ClmsUserFollowCase clmsUserFollowCase = new ClmsUserFollowCase();
        BeanUtils.copyProperties(caseVO, clmsUserFollowCase);
        if (caseVO.getIsFollow().equals(BaseConstant.STRING_1)){
            clmsUserFollowCase.setUserCode(userId);
            clmsUserFollowCase.setCreatedBy(userId);
            clmsUserFollowCase.setUpdatedBy(userId);
            caseMapper.saveClmsUserFollowCase(clmsUserFollowCase);
        }else {
            clmsUserFollowCase.setUserCode(userId);
            caseMapper.delClmsUserFollowCase(clmsUserFollowCase);
        }

    }

    @Override
    public List<ClmsUserFollowCaseVO> getClmsUserFollowCase() {
        String userId = WebServletContext.getUserId();
        List<ClmsUserFollowCase> clmsUserFollowCaseList = caseMapper.getClmsUserFollowCase(userId);
        return clmsUserFollowCaseList.stream().map(clmsUserFollowCase -> {
            ClmsUserFollowCaseVO clmsUserFollowCaseVO = new ClmsUserFollowCaseVO();
            BeanUtils.copyProperties(clmsUserFollowCase, clmsUserFollowCaseVO);
            CaseProcessDTO caseProcessInfo = caseProcessService.getCaseProcessInfo(clmsUserFollowCase.getReportNo(), clmsUserFollowCase.getCaseTimes());
            clmsUserFollowCaseVO.setCaseStatus(CaseProcessStatus.getName(caseProcessInfo.getProcessStatus()));
            return clmsUserFollowCaseVO;
        }).collect(Collectors.toList());
    }

    @Override
    public ClmsUserFollowCaseVO getCaseFollowStatus(String reportNo, Integer caseTimes) {
        String userId = WebServletContext.getUserId();
        ClmsUserFollowCase caseFollowStatus = caseMapper.getCaseFollowStatus(reportNo, caseTimes, userId);
        if (caseFollowStatus != null){
            ClmsUserFollowCaseVO clmsUserFollowCaseVO = new ClmsUserFollowCaseVO();
            BeanUtils.copyProperties(caseFollowStatus, clmsUserFollowCaseVO);
            return clmsUserFollowCaseVO;
        }
        return null;
    }
}
