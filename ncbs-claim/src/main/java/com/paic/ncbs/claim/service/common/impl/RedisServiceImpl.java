package com.paic.ncbs.claim.service.common.impl;

import com.paic.ncbs.claim.service.common.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

@Service("redisService")
public class RedisServiceImpl implements RedisService {

    public static String LOCK_DEL_SCRIPT = "if redis.call('get',KEYS[1]) == ARGV[1] then return redis.call('del',KEYS[1]) else return 0 end";
    public static Long DEFAULT_EXPIRED_TIME = 60L;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public boolean tryLock(String key, String value,Long expiredTime) {
        return redisTemplate.opsForValue().setIfAbsent(key,value,expiredTime, TimeUnit.SECONDS);
    }

    @Override
    public boolean tryLock(String key, String value) {
        return tryLock(key,value,DEFAULT_EXPIRED_TIME);
    }

    @Override
    public boolean removeLock(String key, String value) {
        return redisTemplate.execute(new DefaultRedisScript<>(LOCK_DEL_SCRIPT, Long.class), Arrays.asList(key),value) == 1L;
    }
}
