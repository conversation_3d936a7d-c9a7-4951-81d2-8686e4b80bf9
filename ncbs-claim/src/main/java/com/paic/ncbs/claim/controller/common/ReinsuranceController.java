package com.paic.ncbs.claim.controller.common;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.dao.entity.reinsurance.ReinsBillDTO;
import com.paic.ncbs.claim.dao.mapper.reinsurance.ReinsBillMapper;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceCompensateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Api(tags = "再保")
@RestController
@RequestMapping("/public/reinsurance")
public class ReinsuranceController {

    @Autowired
    private ReinsuranceCompensateService reinsuranceCompensateService;
    @Autowired
    private PrintCoreService printCoreService;
    @Autowired
    private ReinsBillMapper reinsBillMapper;

    @ApiOperation("再保补推")
    @PostMapping(value = "/compensate")
    public ResponseResult<Object> reinsuranceCompensate(@RequestBody (required = false) List<RepayCalDTO> dtos) {
        reinsuranceCompensateService.reinsuranceCompensate(dtos);
        return ResponseResult.success();
    }

    @ApiOperation("再保补推根据理赔送再保的业务环节")
    @PostMapping(value = "/compensateOnBusinessLink")
    public ResponseResult<Object> compensateOnBusinessLink(@RequestBody (required = false) List<RepayCalDTO> dtos) {
        reinsuranceCompensateService.compensateOnBusinessLink(dtos);
        return ResponseResult.success();
    }
    @ApiOperation("再保补推再保账单")
    @PostMapping(value = "/compensateReinsuranceBill")
    public ResponseResult<Object> compensateReinsuranceBill(@RequestBody (required = false) List<RepayCalDTO> dtos) {
        List<ReinsBillDTO> reinsBillDTOList = new ArrayList<>();
        for (RepayCalDTO dto : dtos){
            //判断是否存在未生成成功的再保账单
            ReinsBillDTO reinsBillDTO = new ReinsBillDTO();
            reinsBillDTO.setReportNo(dto.getReportNo());
            reinsBillDTO.setCaseTimes(dto.getCaseTimes());
            reinsBillDTO.setClaimType(dto.getClaimType().getType());
            reinsBillDTOList.addAll(reinsBillMapper.getFailBill(reinsBillDTO) );

        }
        printCoreService.buildReinsBill(reinsBillDTOList);
        return ResponseResult.success();
    }

}
