package com.paic.ncbs.claim.model.vo.reinsurance;

import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.ReinsuranceRateDTO;
import lombok.Data;

import java.util.List;

@Data
public class ReinsApiVO {
    private String reportNo;
    //单证类型
    private String documentType;
    //单证序号
    private String documentOrder;
    //再保人
    private String reinsCode;
    //再保人名称
    private String reinsName;
    //下拉列表
    private List<CodeNameVO> codeNameVOs;
    private String codeType;//1-序号下拉框 2-再保人下拉框
    //消息发送记录列表
    private List<BillSendVO> billSendVOList;
    //单证数据列表
    List<FileDocumentDTO> documentList;
    //接收人列表
    List<RecipientsVO> recipientList;
    //再保信息列表
    List<ReinsuranceRateDTO> reinsuranceRateDTOList;
    //再保地址
    private String reinsURL;
}
