package com.paic.ncbs.claim.service.qualitychecke;

import com.paic.ncbs.claim.dao.entity.qualitychecke.ClmsQualityInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.paic.ncbs.claim.dao.entity.qualitychecke.QualityInfoDetailVO;
import com.paic.ncbs.claim.dao.entity.qualitychecke.QualityInfoWithDetailVO;
import com.paic.ncbs.claim.dao.entity.qualitychecke.QualityQueryVO;
import com.paic.ncbs.claim.model.vo.quality.QualityrequstVO;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimQuanlityCaseVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 案件质检信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
public interface ClmsQualityInfoService extends IService<ClmsQualityInfo> {

    List<WorkBenchTaskVO> getWorkBenchTaskList(ClaimQuanlityCaseVO workBenchTaskQueryVO);

    /**
     * 根据ID查询质检信息
     * @param id 质检信息ID
     * @return 质检信息实体
     */
    ClmsQualityInfo selectQualityInfoById(String id);

    /**
     * 查询所有质检信息
     * @return 质检信息列表
     */
    List<ClmsQualityInfo> selectAllQualityInfo(String qinspector);

    /**
     * 根据条件查询质检信息
     * @param condition 查询条件
     * @return 质检信息列表
     */
    List<ClmsQualityInfo> selectQualityInfoByCondition(ClmsQualityInfo condition);

    /**
     * 根据多种条件查询质检信息
     * @param queryVO 查询条件
     * @return 质检信息列表
     */
    List<QualityInfoWithDetailVO> selectQualityInfoByConditions(QualityQueryVO queryVO);
    /**
     * 更新质检信息
     * @param clmsQualityInfo 质检信息实体
     * @return 更新结果
     */
    boolean updateQualityInfo(QualityInfoDetailVO clmsQualityInfo);

    /**
     * 根据ID删除质检信息
     * @param id 质检信息ID
     * @return 删除结果
     */
    boolean deleteQualityInfoById(String id);

    /**
     * 插入质检信息
     * @param qualityrequstVO 质检信息实体
     * @return 插入结果
     */
    QualityrequstVO insertQualityInfo(QualityrequstVO qualityrequstVO);

    /**
     * 根据报案号、赔付次数和ID关联查询质检信息及详情
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @param id 主键ID
     * @return 质检信息及详情联合查询结果
     */
    QualityInfoDetailVO selectQualityInfoDetailByCondition(String reportNo, Short caseTimes, String id);

    /**
     * 根据报案号和赔付次数更新质检信息
     * @param clmsQualityInfo 质检信息实体
     * @return 更新结果
     */
    String updateQualityInfoByReportNoAndCaseTimes(ClmsQualityInfo clmsQualityInfo);

    /**
     * 根据日期前缀查询最大批次号
     * @param batchNoPrefix 批次号前缀
     * @return 最大批次号
     */
    String selectMaxBatchNoByDate(String batchNoPrefix);

    /**
     * 根据ID删除质检信息及详情
     * @param id 质检信息ID
     * @return 删除结果
     */
    String deleteQualityInfoAndDetailById(String id);

    String getUserName(String userCode);

    ClmsQualityInfo selectQualityInfoWithMaxQiserNo(String reportNo, Short caseTimes);
}
