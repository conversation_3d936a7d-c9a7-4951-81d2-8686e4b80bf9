package com.paic.ncbs.claim.service.other;

import com.paic.ncbs.claim.common.enums.SmsTypeEnum;
import com.paic.ncbs.claim.model.dto.message.SmsInfoDTO;
import com.paic.ncbs.claim.model.vo.record.SmsRecordVO;
import com.paic.ncbs.message.model.dto.SmsResult;

import java.util.List;

public interface SmsInfoService {

    /**
     * 发送业务短信
     *  短信发送：（reportNo + smsTypeEnum + businessId）应该是唯一条件 不可以多次发送 businessId可以为空
     * @param smsTypeEnum
     * @param reportNo
     * @param caseTimes
     * @param businessId 用于关联子任务
     */
    void sendBusinessSmsAsync(SmsTypeEnum smsTypeEnum, String reportNo,Integer caseTimes, String businessId,String requestId);

    /**
     * 发送业务短信
     *  短信发送：（reportNo + smsTypeEnum + businessId）应该是唯一条件 不可以多次发送 businessId可以为空
     * @param smsTypeEnum
     * @param reportNo
     * @param caseTimes
     * @param businessId 用于关联子任务
     */
    void sendBusinessSms(SmsTypeEnum smsTypeEnum, String reportNo,Integer caseTimes, String businessId);

    /**
     * 异步发送
     *
     * @param smsDTO
     */
    void sendSmsByAsync(SmsInfoDTO smsDTO);

    /**
     * 同步发送短信
     *
     * @param smsDTO
     * @return
     */
    void sendSmsBySync(SmsInfoDTO smsDTO);

    /**
     * 查询短信：未实现
     *
     * @param id
     * @return
     */
    SmsResult querySms(String id);

    /**
     * 发送短信
     *
     * @param mobile     手机号
     * @param smsContent 短信内容
     * @return
     */
    SmsResult sendMessage(String mobile, String smsContent);

    /**
     * 发送短信前校验
     *
     * @param reportNo   报案号
     * @return
     */
    Boolean checkIsSend(String reportNo);

    /**
     * 查询报案来源
     *
     * @param reportNo   报案号
     * @return
     */
    String queryReportSubMode(String reportNo);

    /**
     * 新建报案完成给业务员发送短信
     * @param smsTypeEnum
     * @param reportNo
     * @param caseTimes
     * @param businessUeserCode
     */
    void sendBusinessSmsOnCreateReport(SmsTypeEnum smsTypeEnum, String reportNo,Integer caseTimes, String businessUeserCode);


    List<SmsRecordVO> querySmsRecordByReportNo(String reportNo);

}
