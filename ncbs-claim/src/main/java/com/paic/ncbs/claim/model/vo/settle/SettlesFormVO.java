package com.paic.ncbs.claim.model.vo.settle;

import com.paic.ncbs.claim.model.dto.duty.ClmsItemLoss;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.EndorsementDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.vo.checkloss.LossReduceVO;
import com.paic.ncbs.claim.model.vo.report.LinkManVO;
import com.paic.ncbs.claim.model.vo.trace.ClmsPersTraceExpVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
@ApiModel("理算赔付信息")
@Data
public class SettlesFormVO extends EntityDTO {

    private static final long serialVersionUID = 6502646839945881902L;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    @ApiModelProperty("保单赔付信息")
    private List<PolicyPayDTO> policyPayArr;
    @ApiModelProperty("支付项目")
    private List<PaymentItemComData> paymentItemArr;
    @ApiModelProperty("人伤定损信息")
    private List<ClmsPersTraceExpVO> clmsPersTraceExpVOList;
    @ApiModelProperty("批单信息")
    private EndorsementDTO endorInfo;
    @ApiModelProperty("共保信息")
    private List<CoinsureInfoDTO> coinsureInfos;
    @ApiModelProperty("减损信息")
    private LossReduceVO lossReduce;
    @ApiModelProperty("复核结论(1通过，2修改通过，3重新理算)")
    private String verifyConclusion;
    @ApiModelProperty("（核责发送，有风险信息提示时的）不提调说明")
    private String nonSurveyStatement;
    @ApiModelProperty("减损金额")
    private BigDecimal reduceAmount;
    @ApiModelProperty("联系人")
    private List<LinkManVO> linkManList;

    @ApiModelProperty("理算提交状态，0：理算提交 1：理算二次确认提交")
    private String status;
    /**
     * 理算自动提交标识 Y-是
     */
    private String settleAutoSubmit;
    @ApiModelProperty("短信内容")
    private String messageText;

    @ApiModelProperty("损失标的信息")
    private List<ClmsItemLoss> clmsItemLossVO;

    @ApiModelProperty(value = "核赔审批人")
    private String verifyApproved;

    @ApiModelProperty("是否取消负数重开")
    private String isNegativeRestartCancel;
}
