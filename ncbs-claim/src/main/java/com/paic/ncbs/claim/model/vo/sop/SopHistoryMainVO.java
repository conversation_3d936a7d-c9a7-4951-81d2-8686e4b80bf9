package com.paic.ncbs.claim.model.vo.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * SOP版本信息VO
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@ApiModel("SOP版本信息")
public class SopHistoryMainVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private String idSopMain;

    @ApiModelProperty("版本号")
    private String versionNo;

    @ApiModelProperty("SOP名称")
    private String sopName;

    @ApiModelProperty("适用环节")
    private String taskBpmKey;

    @ApiModelProperty("适用环节名称")
    private String taskBpmKeyName;

    @ApiModelProperty("发布人员姓名")
    private String publisherName;

    @ApiModelProperty("状态（01-暂存、02-有效、03-无效）")
    private String status;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveDate;

    @ApiModelProperty("失效日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invalidDate;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sysCtime;

}