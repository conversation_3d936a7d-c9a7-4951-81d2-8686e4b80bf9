package com.paic.ncbs.claim.service.other.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.MailTypeEnum;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.other.TextTemplateEntity;
import com.paic.ncbs.claim.dao.mapper.other.MailInfoMapper;
import com.paic.ncbs.claim.dao.mapper.other.MailSendMapper;
import com.paic.ncbs.claim.dao.mapper.other.TextTemplateMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.message.MailSendDTO;
import com.paic.ncbs.claim.model.dto.message.MailSendParamNew;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.claim.service.other.MailInfoService;
import com.paic.ncbs.claim.service.other.MailSendService;
import com.paic.ncbs.message.model.dto.MailSendAttachmentParam;
import com.paic.ncbs.message.model.dto.MailSendParam;
import com.paic.ncbs.message.model.dto.SmsResult;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.text.StrSubstitutor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.MessageFormat;
import java.util.*;

@Slf4j
@Service
@RefreshScope
public class MailSendServiceImpl implements MailSendService {

    @Autowired
    private MailInfoMapper mailInfoMapper;

    @Autowired
    private MailInfoService mailInfoService;
    @Autowired
    private CacheService  cacheService;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private IOBSFileUploadService iobsFileUploadService;
    @Autowired
    private MailSendMapper mailSendMapper;
    @Autowired
    private TextTemplateMapper textTemplateMapper;
    @Value ("${mail.env}")
    private String mailEnv;
    @Value("${iobs.enable}")
    private boolean iobs;
    @Override
    public void sendMajorCaseMail(String reportNo, String receiver,String cc) {
        try {
            mailInfoService.sendMailByAsync(reportNo,buildMajorCaseParam(reportNo,receiver,cc));
        }catch (Exception e){
            LogUtil.info("发送大案邮件失败"+reportNo ,e);
        }
    }

    @Override
    public void sendCaseMail(String reportNo,String receiver) {
        try {
            List<PolicyInfoDTO> policyList = mailInfoMapper.getMailPolicy(reportNo);
            if(ListUtils.isEmptyList(policyList)){
                return;
            }
            PolicyInfoDTO policyDTO = policyList.get(0);
            String email = Optional.ofNullable(cacheService.queryUserInfo(receiver)).map(UserInfoDTO::getEmail).orElse("");
            if(StringUtils.isEmptyStr(email)){
                LogUtil.info("收件人邮箱为空,reportNo="+reportNo);
                return;
            }
            mailInfoService.sendMailByAsync(reportNo,buildCaseMailParam(reportNo,email,policyDTO.getPolicyNo(),policyDTO.getDepartmentCode()));
        }catch (Exception e){
            LogUtil.info("发送普案邮件失败"+reportNo ,e);
        }
    }

    public MailSendParam buildMajorCaseParam(String reportNo, String receiver,String cc) {
        MailSendParam param = new MailSendParam();
        String title = String.format(Constants.MAJOR_CASE_MAIL_TITLE,reportNo);
        String content = Constants.MAJOR_CASE_MAIL_CONTENT;
        if("test".equals(mailEnv)){
            title = Constants.MAIL_TITLE_TIPS + title;
            content = content+Constants.MAIL_CONTENT_TIPS;
        }

        param.setSubject(title);
//        param.setContent(content);
//        param.setReceiver(receiver);
//        param.setCc(cc);
        return param;
    }

    public MailSendParam buildCaseMailParam(String reportNo, String receiver, String policyNo, String deptCode) {
        String deptName = departmentDefineMapper.queryDepartmentNameByDeptCode(deptCode);
        MailSendParam param = new MailSendParam();
        String title = String.format(Constants.CASE_MAIL_TITLE, reportNo);
        String content = String.format(Constants.CASE_MAIL_CONTENT,policyNo,deptCode+"-"+deptName);
        if("test".equals(mailEnv)){
            title = Constants.MAIL_TITLE_TIPS + title;
            content = content+Constants.MAIL_CONTENT_TIPS;
        }
        param.setSubject(title);
//        param.setContent(content);
//        param.setReceiver(receiver);
        return param;
    }
    public SmsResult sendReinsMail(MailSendDTO mailSendDTO){
        UserInfoDTO userInfoDTO = WebServletContext.getUser();
        MailSendParamNew mailSendParam = buildMailSendParamNew(mailSendDTO);
        //保存邮件
        if(mailSendDTO.getId()==null||mailSendDTO.getId().isEmpty()){
            mailSendDTO.setId(UuidUtil.getUUID());
        }
        mailSendDTO.setCreatedBy(userInfoDTO.getUserCode());
        mailSendDTO.setStatus(Constants.EMAILL_SEND_0);
        mailSendMapper.insert(mailSendDTO);
        SmsResult smsResult = mailInfoService.sendReinsMailBySync(mailSendParam);
        if( Constants.SMS_SEND_SUCCESS.equals(smsResult.getIsSuccess())){
            mailSendDTO.setStatus(Constants.EMAILL_SEND_1);
        }else{
            mailSendDTO.setStatus(Constants.EMAILL_SEND_2);
            mailSendDTO.setFailReason(smsResult.getFailCode()+":"+smsResult.getFailReason());
            if(mailSendDTO.getFailReason().length() > 100){
                mailSendDTO.setFailReason(mailSendDTO.getFailReason().substring(0,100));
            }
        }
        mailSendMapper.updateMailSendById(mailSendDTO);
        return smsResult;
    }

    private MailSendParamNew buildMailSendParamNew(MailSendDTO mailSendDTO) {
        MailSendParamNew mailSendParam = new MailSendParamNew();
        mailSendParam.setFrom(mailSendDTO.getSendFrom());
        mailSendParam.setTo(mailSendDTO.getSendTo());
        mailSendParam.setCc(mailSendDTO.getMailCc());
        mailSendParam.setSubject(mailSendDTO.getTitle());
        mailSendParam.setBody(mailSendDTO.getContent());
        long maxMailSize = 9 * 1024 * 1024;//邮件总大小不可超过10M
        long sumFileSize = 0;
        List<MailSendAttachmentParam> attachmentsDTOs = new ArrayList<>();
        if(mailSendDTO.getAttachments()!=null&& !mailSendDTO.getAttachments().isEmpty()){
            String[] attachments = mailSendDTO.getAttachments().split(";");
            for (String attachment : attachments) {
                String[] attachmentTemp = attachment.split(":",2);
                MailSendAttachmentParam attachmentParam = new MailSendAttachmentParam();
                attachmentParam.setFileName(attachmentTemp[0]);
                String url = attachmentTemp[1];
                if(MailTypeEnum.REINS_ACTUAL.getType().equals(mailSendDTO.getMailType())
                        ||MailTypeEnum.REINS_ESTIMATED.getType().equals(mailSendDTO.getMailType())){//从单证系统获取附件
                    if (iobs) {
                        Calendar nowTime = Calendar.getInstance();
                        nowTime.add(Calendar.MINUTE, 5);//cos文件url有效期5分钟，设置4分钟为扣除调用cos接口时间
                        url = iobsFileUploadService.getPerpetualDownloadUrl(url, attachmentTemp[0]);
                    }
                    byte[] fileBytes = downloadFileAsBytes(url);
                    attachmentParam.setData(fileBytes);
                    sumFileSize += fileBytes.length;
                    if(sumFileSize > maxMailSize){
                        throw new GlobalBusinessException("邮件大小超出限制");
                    }
                }
                attachmentsDTOs.add(attachmentParam);
            }
        }
        mailSendParam.setAttachments(attachmentsDTOs);
        return mailSendParam;
    }
    public byte[] downloadFileAsBytes(String fileUrl) {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(fileUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (InputStream inputStream = connection.getInputStream();
                     ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    return outputStream.toByteArray();
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }
    //根据模板参数生成内容
    public String getContextAndTitle(MailTypeEnum mailTypeEnum, Map<String,String> params){
        TextTemplateEntity textTemplateEntity = new TextTemplateEntity();
        String content = "";
        textTemplateEntity.setTemplateCode(mailTypeEnum.getType());
        List<TextTemplateEntity> textTemplateEntities = textTemplateMapper.getStandardTemplate(textTemplateEntity);
        if(textTemplateEntities!=null){
            content = textTemplateEntities.get(0).getTemplateContent();
            StrSubstitutor strSubstitutor = new StrSubstitutor(params);
            return strSubstitutor.replace(content);
        }else{
            throw new GlobalBusinessException(MessageFormat.format("邮件{0}不存在！", mailTypeEnum.getName()));
        }
    }
}
