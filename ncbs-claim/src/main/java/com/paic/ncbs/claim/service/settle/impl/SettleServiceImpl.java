package com.paic.ncbs.claim.service.settle.impl;

import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.enums.RuleTypeEnums;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.controller.standard.bill.enums.BillEnum;
import com.paic.ncbs.claim.dao.entity.rule.AutoRuleDetailInfoEntity;
import com.paic.ncbs.claim.dao.entity.rule.AutoRuleMainInfoEntity;
import com.paic.ncbs.claim.dao.mapper.other.ClmsSmsTemplateMapper;
import com.paic.ncbs.claim.dao.mapper.report.InsuredPersonMapper;
import com.paic.ncbs.claim.dao.mapper.riskppt.RiskPropertyMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.autoclaimsettle.ClaimRuleResultDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.FeePayDTO;
import com.paic.ncbs.claim.model.dto.message.ClmsSmsTemplateDTO;
import com.paic.ncbs.claim.model.dto.report.InsuredPersonDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyQueryDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.vo.settle.*;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.fee.FeePayService;
import com.paic.ncbs.claim.service.restartcase.RestartCaseService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.rule.AutoRuleRecordService;
import com.paic.ncbs.claim.service.rule.AutoRuleService;
import com.paic.ncbs.claim.service.settle.AutoSettleService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.settle.SettleService;
import com.paic.ncbs.claim.service.verify.VerifyService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;
import static com.paic.ncbs.claim.common.util.BigDecimalUtils.sum;

@RefreshScope
@Service("settleService")
public class SettleServiceImpl implements SettleService {

    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private EstimateService estimateService;
    @Autowired
    private PolicyPayMapper policyPayDao;
    @Autowired
    private AutoSettleService autoSettleService;
    @Autowired
    private FeePayService feePayService;
    @Autowired
    private MedicalBillService medicalBillService;
    @Autowired
    private  DutyBillLimitInfoMapper dutyBillLimitInfoMapper;
    @Autowired
    private AutoRuleService autoRuleService;
    @Autowired
    private AutoRuleRecordService autoRuleRecordService;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private ClmsSmsTemplateMapper clmsSmsTemplateMapper;
    @Autowired
    private InsuredPersonMapper insuredPersonMapper;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private RiskPropertyMapper riskPropertyMapper;
    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;
    @Autowired
    private RestartCaseService restartCaseService;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;

    @Value("${investment.productCode}")
    private String investmentProductCode;

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public List<DutyDetailPayDTO> getAutoSettleAmount(DutyPayDTO dutyPayDTO){
        List<DutyDetailPayDTO> detailPayDTOS = dutyPayDTO.getDutyDetailPayArr();
        detailPayDTOS.forEach(detail->detail.setIdPolicyDuty(dutyPayDTO.getIdCopyDuty()));
        autoSettleService.autoSettle(detailPayDTOS);
        // sumDutyDetailAutoAmount(dutyPayDTO);
        return detailPayDTOS;
    }

    /*private void sumDutyDetailAutoAmount(DutyPayDTO dutyPayDTO){
        BigDecimal dutyAmount = BigDecimal.ZERO;
        for (DutyDetailPayDTO detail : dutyPayDTO.getDutyDetailPayArr()) {
            if(detail.getAutoSettleAmount() != null){
                dutyAmount = dutyAmount.add(detail.getAutoSettleAmount());
            }
        }
        dutyPayDTO.setSettleAmount(dutyAmount);
    }*/

    @Override
    public BatchPayAmountVo getBatchPayAmount(SettlesFormVO formVO) {
        BigDecimal settleAmountSum = BigDecimal.ZERO;
        BigDecimal accommodationAmountSum = BigDecimal.ZERO;
        BigDecimal protocolAmountSum = BigDecimal.ZERO;
        BigDecimal settlePrepaySum = BigDecimal.ZERO;
        BigDecimal settleFeePaySum = BigDecimal.ZERO;
        BigDecimal settlePreFeeSum = BigDecimal.ZERO;
        String indemnityMode = null;
        List<PolicyPayDTO> policyPays = formVO.getPolicyPayArr();
        //可能会修改手动理算金额，需重新计算保单理算总额
        SettleHelper.initSettleAmount(policyPays);
        for(PolicyPayDTO policyPay : policyPays){
            String reportNo = policyPay.getReportNo();
            Integer caseTimes = policyPay.getCaseTimes();
            if(StringUtils.isEmptyStr(reportNo)){
                throw new GlobalBusinessException("报案号不可为空");
            }
            if(Objects.isNull(caseTimes)){
                throw new GlobalBusinessException("赔付次数不可为空");
            }

            String caseNo = policyPay.getCaseNo();

            //SettleBatchInfoDTO partSettleVO = settleBatchService.getPartSettleSum(reportNo, caseTimes, caseNo);
            //SettleBatchInfoDTO partAcmAndProVO = settleBatchService.getPartAcmAndProSum(reportNo, caseTimes, caseNo);
            BigDecimal payFee = policyPayService.getFeePayAmount(policyPay, SettleConst.CLAIM_TYPE_PAY);
            BigDecimal prePayFee = policyPayService.getFeePayAmount(policyPay, SettleConst.CLAIM_TYPE_PRE_PAY);
            for (PlanPayDTO plan : policyPay.getPlanPayArr()) {
                for (DutyPayDTO duty : plan.getDutyPayArr()) {
                    for (DutyDetailPayDTO detail : duty.getDutyDetailPayArr()) {
                        //通融
                        if (ModelConsts.INDEMNITY_MODE_ACCOMMODATE.equals(detail.getIndemnityMode())) {
                            BigDecimal accommodationAmount = BigDecimalUtils.isNullBigDecimal(detail.getSettleAmount()) ? detail.getAutoSettleAmount() : detail.getSettleAmount();
                            accommodationAmountSum = BigDecimalUtils.sum(accommodationAmountSum, accommodationAmount);
                            detail.setAccommodationAmount(accommodationAmount);
                            //合议
                        } else if (ModelConsts.INDEMNITY_MODE_PROTOCOL.equals(detail.getIndemnityMode())) {
                            BigDecimal protocolAmount = BigDecimalUtils.isNullBigDecimal(detail.getSettleAmount()) ? detail.getAutoSettleAmount() : detail.getSettleAmount();
                            detail.setProtocolAmount(protocolAmount);
                            protocolAmountSum = BigDecimalUtils.sum(protocolAmountSum, protocolAmount);
                        }
                    }
                }
            }
            /*
            protocolAmountSum = BigDecimalUtils.sum(partAcmAndProVO.getProtocolAmount(), protocolAmountSum);
            accommodationAmountSum = BigDecimalUtils.sum(partAcmAndProVO.getAccommodationAmount(), accommodationAmountSum);
            settleAmountSum = BigDecimalUtils.sum(partSettleVO.getPolicyPayAmount(), policyPay.getSettleAmount());
            settleFeePaySum = BigDecimalUtils.sum(partSettleVO.getPolicyFee(), payFee);
            settlePreFeeSum = BigDecimalUtils.sum(partSettleVO.getPreFeeAmount(), prePayFee);*/
            settleAmountSum = BigDecimalUtils.sum(settleAmountSum, policyPay.getSettleAmount());
            settleFeePaySum = BigDecimalUtils.sum(settleFeePaySum, payFee);
            settlePreFeeSum = BigDecimalUtils.sum(settlePreFeeSum, prePayFee);
            settlePrepaySum = policyPayDao.getPrePayAmount(reportNo, caseTimes);
            indemnityMode = policyPay.getIndemnityMode();
        }

        BatchPayAmountVo vo = new BatchPayAmountVo();



        // 理算逻辑调整，理算金额
        /*vo.setPolicyPayAmount(settleAmountSum.add(settlePrepaySum));
        vo.setPrePayAmount(settlePrepaySum);
        vo.setFinalPayAmount(settleAmountSum);*/
        vo.setPolicyPayAmount(settleAmountSum);
        vo.setPrePayAmount(settlePrepaySum);
        vo.setFinalPayAmount(settleAmountSum.subtract(settlePrepaySum));

        // 计算前次已支付赔款金额
        if (CollectionUtils.isNotEmpty(policyPays)) {
            PolicyPayDTO policyPay = policyPays.get(0);
            String reportNo = policyPay.getReportNo();
            Integer caseTimes = policyPay.getCaseTimes();
            if (StringUtils.isNotEmpty(reportNo) && Objects.nonNull(caseTimes) && caseTimes > 1) {
                BigDecimal lastPolicyPayAmount = policyPayService.getLastPolicyPayAmount(reportNo, caseTimes);
                vo.setLastPolicyPayAmount(lastPolicyPayAmount);

                // 重开时，本次应支付赔款金额需再减去前次已支付赔款金额
                vo.setFinalPayAmount(vo.getFinalPayAmount().subtract(lastPolicyPayAmount));
            }
        }

        vo.setPolicyFee(settleFeePaySum.add(settlePreFeeSum));
        vo.setPreFeeAmount(settlePreFeeSum);
        vo.setFinalFee(settleFeePaySum);
        vo.setPolicyPayTotal(vo.getPolicyPayAmount().add(vo.getPolicyFee()));
        vo.setFinalPay(vo.getFinalPayAmount().add(vo.getFinalFee()));
        vo.setPrePay(settlePrepaySum.add(settlePreFeeSum));
        vo.setAccommodationAmount(accommodationAmountSum);
        vo.setProtocolAmount(protocolAmountSum);
        if (SettleConst.INDEMNITY_MODE_ACCOMMODATE.equals(indemnityMode)) {
            vo.setOriginalAmount(settleAmountSum.subtract(accommodationAmountSum));
        } else if (SettleConst.INDEMNITY_MODE_PROTOCOL.equals(indemnityMode)) {
            vo.setOriginalAmount(settleAmountSum.subtract(protocolAmountSum));
        }
//        //校验金额是否超出限额
//        if (!CollectionUtils.isEmpty(formVO.getPolicyPayArr().get(0).getPlanPayArr().get(0).getDutyPayArr().get(0).getDutyDetailPayArr())) {
//            this.checkAmount(formVO.getPolicyPayArr().get(0).getPlanPayArr().get(0).getDutyPayArr().get(0).getDutyDetailPayArr().get(0));
//        }
        //返回想通过日期账单提示
        try {
            if (!CollectionUtils.isEmpty(formVO.getPolicyPayArr())) {
                this.getIsSameBillNew(formVO.getPolicyPayArr().get(0).getPolicyNo(),
                        formVO.getPolicyPayArr().get(0).getReportNo(),
                        formVO.getPolicyPayArr().get(0).getCaseTimes(),
                        vo);
            }
        } catch (Exception e) {
            LogUtil.error("返回想通过日期账单提示不符合",e);
        }
        return vo;
    }


    private void getIsSameBill(String policyNo, String reportNo, BatchPayAmountVo vo) {
        String existsSameBillDesc = "";
        String model = "%s日在%s案";
        //目前只考虑单责任，所以直接取0
        List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOs = dutyBillLimitInfoMapper.getBillDate(policyNo);
        //查询本次记录表数据（收单环节并没有保存保单号）
        List<DutyBillLimitInfoDTO> limitBilltByReportNoAndCaseTimes = dutyBillLimitInfoMapper.getLimitBilltByReportNoAndCaseTimes(reportNo, 1);

        //dutyBillLimitInfoDTOs不为空标识有历史单子
        //limitBilltByReportNoAndCaseTimes不为空表示本次案件有单子
        if (!CollectionUtils.isEmpty(dutyBillLimitInfoDTOs) && !CollectionUtils.isEmpty(limitBilltByReportNoAndCaseTimes)) {
            for (DutyBillLimitInfoDTO limitBilltByReportNoAndCaseTime : limitBilltByReportNoAndCaseTimes) {
                dutyBillLimitInfoDTOs.add(limitBilltByReportNoAndCaseTime);
            }
            Calendar cBillDate = Calendar.getInstance();
            for (DutyBillLimitInfoDTO dutyBillLimitInfoDTO : dutyBillLimitInfoDTOs) {
                if (reportNo.equals(dutyBillLimitInfoDTO.getReportNo())) {
                    cBillDate.setTime(dutyBillLimitInfoDTO.getBillDate());
                    continue;
                }
            }
            List<DutyBillLimitInfoDTO> collect = dutyBillLimitInfoDTOs.stream().filter(a -> !reportNo.equals(a.getReportNo())).collect(Collectors.toList());
            //虽然记录表会有多条，但是可能都是本次案件的，只有过滤完本次案件还有剩余，则再继续后续判断
            if (collect.size() > 0) {
                for (int i = 0; i < collect.size(); i++) {
                    Date otherBillDate = collect.get(i).getBillDate();
                    Calendar cOtherbillDate = Calendar.getInstance();
                    cOtherbillDate.setTime(otherBillDate);
                    if (cBillDate.getTimeInMillis() == cOtherbillDate.getTimeInMillis()) {
                        if (i == 0) {
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            String cDate = simpleDateFormat.format(cOtherbillDate.getTime());
                            existsSameBillDesc = String.format(model, cDate, collect.get(i).getReportNo());
                        } else {
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                            String cDate = simpleDateFormat.format(cOtherbillDate.getTime());
                            existsSameBillDesc = existsSameBillDesc + "," + String.format(model, cDate, collect.get(i).getReportNo());
                        }

                    }
                }
            }
        }
        if (StringUtils.isEmptyStr(existsSameBillDesc)) {
            vo.setExistsSameBillDesc(existsSameBillDesc);
        } else {
            vo.setExistsSameBillDesc(existsSameBillDesc + "已赔付");
        }
    }

    private void getIsSameBillNew(String policyNo, String reportNo, Integer caseTimes, BatchPayAmountVo vo) {
        List<RiskPropertyQueryDTO> nameList = new ArrayList<>();
        //相同出险人+相同发票起期在做理算时提醒  1、被保险人（个人） 2、雇员--雇主险根据雇员判断
        //查询客户类型
        InsuredPersonDTO insuredDTO = insuredPersonMapper.getInsuredPersonInfo(reportNo);
        //判断是否是雇主责任险
        boolean employerInsurance = riskPropertyService.isEmployerInsuranceNew(reportNo, policyNo);
        //1、被保险人（个人）
        RiskPropertyQueryDTO riskPropertyQueryDTO = new RiskPropertyQueryDTO();
        if(!employerInsurance && !"Y".equals(insuredDTO.getIsOrganization())){
            riskPropertyQueryDTO.setName(insuredDTO.getName());
            bulidSameBillInfo(riskPropertyQueryDTO, reportNo, caseTimes,
                    insuredDTO.getClientNo(), "1", vo);
        } else if(employerInsurance){
            //2、雇员--雇主险根据雇员判断
            //查询标的类型
            //暂时删除
//            riskPropertyQueryDTO = riskPropertyMapper.getRiskPropertyListByReportNo(reportNo,caseTimes);
//            bulidSameBillInfo(riskPropertyQueryDTO, reportNo, caseTimes, null,
//                    "2", vo);
        }
    }

    private void bulidSameBillInfo(RiskPropertyQueryDTO riskPropertyQueryDTO, String reportNo,
                                   Integer caseTimes, String clientNo,
                                   String flag, BatchPayAmountVo vo) {
        String existsSameBillDesc = "";
        String model = "出险人%s:<br>在案件%s中有相同日期%s的发票。";
        String model2 = "在案件%s中有相同日期%s的发票。";
        List<MedicalBillInfoVO> medicalBillInfoVOs = null;
        //查询本次账单数据
        List<MedicalBillInfoVO> medicalBillInfoList = medicalBillInfoMapper.getThisAllBillDate(reportNo,caseTimes);
        if(riskPropertyQueryDTO != null){
            //1、被保险人（个人）
            if("1".equals(flag)){
                //根据客户代码查询历史账单集合
                medicalBillInfoVOs =
                        medicalBillInfoMapper.getOtherAllBillDate(clientNo,reportNo);
            } else {
                CaseRiskPropertyDTO caseRiskPropertyDTO = new CaseRiskPropertyDTO();
                caseRiskPropertyDTO.setCertificateType(riskPropertyQueryDTO.getCertificateType());
                caseRiskPropertyDTO.setCertificateNo(riskPropertyQueryDTO.getCertificateNo());
                caseRiskPropertyDTO.setName(riskPropertyQueryDTO.getName());
                medicalBillInfoVOs.addAll(medicalBillInfoMapper.getOtherBillDateByClientInfo(caseRiskPropertyDTO));
            }
            if (!CollectionUtils.isEmpty(medicalBillInfoVOs)
                    && !CollectionUtils.isEmpty(medicalBillInfoList)) {
                // 解析列表数据，将对象转换为Map<案件号, 日期集合>
                Map<String, Set<String>> billData = parseList(medicalBillInfoList);//本次
                Map<String, Set<String>> dutyData = parseList(medicalBillInfoVOs);//历史
                // 提取所有日期，找出两个列表共有的日期
                Set<String> allBillDates = billData.values().stream()
                        .flatMap(Set::stream)
                        .collect(Collectors.toSet());
                Set<String> allDutyDates = dutyData.values().stream()
                        .flatMap(Set::stream)
                        .collect(Collectors.toSet());
                // 求日期交集
                Set<String> commonDates = new HashSet<>(allBillDates);
                commonDates.retainAll(allDutyDates);
                // 收集有相同日期的案件及其对应日期
                Map<String, Set<String>> caseDates = new HashMap<>();
                processCaseData(dutyData, commonDates, caseDates);
                // 生成提示信息
                List<String> alerts = new ArrayList<>();
                Boolean f = true;
                for (Map.Entry<String, Set<String>> entry : caseDates.entrySet()) {
                    String caseId = entry.getKey();
                    Set<String> dates = entry.getValue();
                    // 对日期进行排序
                    List<String> sortedDates = new ArrayList<>(dates);
                    Collections.sort(sortedDates);

                    // 拼接日期字符串
                    String dateStr = String.join("、", sortedDates);
                    if(f){
                        alerts.add(String.format(model, riskPropertyQueryDTO.getName(),
                                caseId, dateStr));
                        f = false;
                    } else {
                        alerts.add(String.format(model2, caseId, dateStr));
                    }
                }
                // 合并所有提示信息 前端提示会处理识别成html 加\n无效
                existsSameBillDesc = String.join("<br>", alerts);
            }
        }
        if(StringUtils.isNotEmpty(existsSameBillDesc)){
            vo.setExistsSameBillDesc(existsSameBillDesc);
        }
    }

    private static Map<String, Set<String>> parseList(List<MedicalBillInfoVO> list) {
        Map<String, Set<String>> result = new HashMap<>();
        for (MedicalBillInfoVO dto : list) {
            String caseId = dto.getReportNo();
            Date date = dto.getStartDate();

            if (date != null) {
                String dateStr = dateFormat.format(date);
                result.computeIfAbsent(caseId, k -> new HashSet<>()).add(dateStr);
            }
        }
        return result;
    }

    private static void processCaseData(Map<String, Set<String>> dataMap,
                                        Set<String> commonDates,
                                        Map<String, Set<String>> resultMap) {
        for (Map.Entry<String, Set<String>> entry : dataMap.entrySet()) {
            String caseId = entry.getKey();
            Set<String> dates = entry.getValue();

            // 筛选出属于共同日期的日期
            Set<String> matchedDates = dates.stream()
                    .filter(commonDates::contains)
                    .collect(Collectors.toSet());

            if (!matchedDates.isEmpty()) {
                resultMap.computeIfAbsent(caseId, k -> new HashSet<>()).addAll(matchedDates);
            }
        }
    }

    @Override
    public List<PolicyPayBaseInfoDTO> getPolicyPayBaseInfo(String reportNo, Integer caseTimes) {
        List<PolicyPayBaseInfoDTO> policyPayBaseInfos = policyPayDao.getPolicyPayBaseInfo(reportNo, caseTimes);
        setPolicyShowName(policyPayBaseInfos);
        return policyPayBaseInfos;
    }

    @Override
    @Deprecated
    public List<PolicyPayDTO> distributeFee(List<PolicyPayDTO> policyPays, String claimType, Integer subTimes) {
        if (CollectionUtils.isEmpty(policyPays)) {
            return policyPays;
        }
        PolicyPayDTO policyPay = policyPays.get(0);
        String reportNo = policyPay.getReportNo();
        Integer caseTimes = policyPay.getCaseTimes();
        //清空险种、责任的各项费用
        SettleHelper.clearFee(policyPays);
        //获取保单预估费用信息
        List<EstimatePolicyDTO> estimatePolicyList = estimateService.getEstimateList(reportNo, caseTimes);
        //获取理赔费用信息
        FeePayDTO feePayDTO = new FeePayDTO();
        feePayDTO.setReportNo(reportNo);
        feePayDTO.setCaseTimes(caseTimes);
        feePayDTO.setClaimType(claimType);
        List<FeeInfoDTO> feeInfoList = feePayService.getFeePayByParam(feePayDTO);
        if (CollectionUtils.isEmpty(feeInfoList)) {
            LogUtil.audit("reportNo:{} feeInfoList is empty", reportNo);
            return policyPays;
        }
        List<PolicyPayDTO> policyPrePayList = null;
        LogUtil.audit("reportNo:{},claimType:{}", reportNo, claimType);
        //若赔付类型不是‘2-预赔’，查询预赔保单列表
        if (!ModelConsts.CLAIM_TYPE_PRE_PAY.equals(claimType)) {
            policyPrePayList = getPolicyPrePayInfo(reportNo, caseTimes);
        }
        for (PolicyPayDTO policyPayInfo : policyPays) {
            //计算保单费用总和，和各项费用总和
            FeeAmountVO policyFee = SettleHelper.getPolicyFee(feeInfoList, policyPayInfo.getPolicyNo(), policyPayInfo.getCaseNo());
            List<PlanPayDTO> planPayArr = policyPayInfo.getPlanPayArr();
            if (CollectionUtils.isEmpty(planPayArr)) {
                continue;
            }
            //将费用分摊至各险种
            distributeFeeForPlan(policyPayInfo, estimatePolicyList, policyFee, claimType, policyPrePayList);
        }
        return policyPays;

    }

    private void setPolicyShowName(List<PolicyPayBaseInfoDTO> policyPayBaseInfos) {
        if (CollectionUtils.isEmpty(policyPayBaseInfos)) {
            return;
        }
        LogUtil.audit("#保单赔案信息量为# policyPayBaseInfosSize=" + policyPayBaseInfos.size());

        for (PolicyPayBaseInfoDTO policyPayBase : policyPayBaseInfos) {
            String policyNo = policyPayBase.getPolicyNo();
            String policyCerNo = policyPayBase.getPolicyCerNo();
            if (StringUtils.isEmptyStr(policyCerNo)) {
                policyPayBase.setPolicyShowName(policyNo);
                continue;
            }
            String policyShowName = String.format("%s(%s)", policyNo, policyCerNo);
            policyPayBase.setPolicyShowName(policyShowName);
        }
    }

    private List<PolicyPayDTO> getPolicyPrePayInfo(String reportNo, Integer caseTimes) {
        List<PolicyPayDTO> policyPayList = policyPayService.getByReportNo(reportNo, caseTimes, ModelConsts.CLAIM_TYPE_PRE_PAY);
        if (ListUtils.isNotEmpty(policyPayList)) {
            for (int i = 0; i < policyPayList.size(); i++) {
                PolicyPayDTO policyPayDTO = policyPayList.get(i);
                if (ListUtils.isEmptyList(policyPayDTO.getPlanPayArr())) {
                    policyPayList.remove(i);
                }
            }
        }
        return policyPayList;
    }

    private void distributeFeeForPlan(PolicyPayDTO policyPayInfo, List<EstimatePolicyDTO> estimatePolicyList, FeeAmountVO policyFee, String claimType, List<PolicyPayDTO> policyPrePayList) {
        FeeAmountVO planFeeSum = new FeeAmountVO();
        List<PlanPayDTO> planPays = policyPayInfo.getPlanPayArr();
        EstimatePolicyDTO ePolicy = getEstimatePolicy(estimatePolicyList, policyPayInfo.getPolicyNo(), policyPayInfo.getCaseNo());
        if (ModelConsts.CLAIM_TYPE_PRE_PAY.equals(claimType) || ListUtils.isNotEmpty(policyPrePayList)) {
            BigDecimal ratio = getRatioByPlanSize(policyPayInfo);
            for (PlanPayDTO planPayItem : planPays) {
                LogUtil.audit("#分摊费用-根据理算金额分摊费用 " + planPayItem.getPlanCode());
                LogUtil.audit("planPayAmount={}, policyPayAmount={},险种分摊比例:{}", planPayItem.getSettleAmount(), policyPayInfo.getSettleAmount(), ratio);
                setPlanPayFee(planPayItem, policyFee, planPays, ratio, planFeeSum);
                distributeFeeForDuty(planPayItem, true);
            }
        } else if (BigDecimalUtils.isGreaterZero(policyPayInfo.getSettleAmount())) {
            LogUtil.audit("根据赔付信息分摊费用distributeFeeForPlan.reportNo={}", policyPayInfo.getReportNo());
            for (PlanPayDTO planPayItem : planPays) {
                LogUtil.audit("#分摊费用-根据理算金额分摊费用 " + planPayItem.getPlanCode());
                BigDecimal ratio = nvl(planPayItem.getSettleAmount(), 0).divide(policyPayInfo.getSettleAmount(), 8, BigDecimal.ROUND_HALF_UP);
                LogUtil.audit("planPayAmount={}, policyPayAmount={},险种分摊比例:{}", planPayItem.getSettleAmount(), policyPayInfo.getSettleAmount(), ratio);
                setPlanPayFee(planPayItem, policyFee, planPays, ratio, planFeeSum);
                distributeFeeForDuty(planPayItem, false);
            }
        } else {
            LogUtil.audit("根据预估信息分摊费用");
            distributeFeeByEstimatePlan(policyPayInfo, policyFee, planFeeSum, planPays, ePolicy);
        }
    }

    private EstimatePolicyDTO getEstimatePolicy(List<EstimatePolicyDTO> estimatePolicyList, String policyNo, String caseNo) {
        for (EstimatePolicyDTO estimatePolicy : estimatePolicyList) {
            if (estimatePolicy.getPolicyNo().equals(policyNo) && StringUtils.isEqualStr(caseNo, estimatePolicy.getCaseNo())) {
                return estimatePolicy;
            }
        }
        return null;
    }

    private BigDecimal getRatioByPlanSize(PolicyPayDTO policyPayInfo) {
        int planCount = policyPayInfo.getPlanPayArr().size();
        if (planCount == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal ratio = new BigDecimal(1).divide(new BigDecimal(planCount), 8, BigDecimal.ROUND_DOWN);
        LogUtil.audit("按照险种数量,险种分摊比例:" + ratio);
        return ratio;
    }

    private BigDecimal setPlanPayFee(PlanPayDTO planPayItem, FeeAmountVO policyFee, List<PlanPayDTO> planPays, BigDecimal ratio, FeeAmountVO planFeeSum) {
        if (planPays.indexOf(planPayItem) == planPays.size() - 1) {
            planPayItem.setArbitrageFee(policyFee.getFeeAmountArb().subtract(planFeeSum.getFeeAmountArb()));
            planPayItem.setCommonEstimateFee(policyFee.getFeeAmountCom().subtract(planFeeSum.getFeeAmountCom()));
            planPayItem.setExecuteFee(policyFee.getFeeAmountExe().subtract(planFeeSum.getFeeAmountExe()));
            planPayItem.setLawsuitFee(policyFee.getFeeAmountLaws().subtract(planFeeSum.getFeeAmountLaws()));
            planPayItem.setLawyerFee(policyFee.getFeeAmountLawy().subtract(planFeeSum.getFeeAmountLawy()));
            planPayItem.setVerifyFee(policyFee.getFeeAmountVer().subtract(planFeeSum.getFeeAmountVer()));
            planPayItem.setInquireFee(policyFee.getFeeAmountInq().subtract(planFeeSum.getFeeAmountInq()));
            planPayItem.setOtherFee(policyFee.getFeeAmountOth().subtract(planFeeSum.getFeeAmountOth()));
            planPayItem.setSpecialSurveyFee(policyFee.getFeeAmountSpe().subtract(planFeeSum.getFeeAmountSpe()));
        } else {
            planPayItem.setArbitrageFee(policyFee.getFeeAmountArb().multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            planPayItem.setCommonEstimateFee(policyFee.getFeeAmountCom().multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            planPayItem.setExecuteFee(policyFee.getFeeAmountExe().multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            planPayItem.setLawsuitFee(policyFee.getFeeAmountLaws().multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            planPayItem.setLawyerFee(policyFee.getFeeAmountLawy().multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            planPayItem.setVerifyFee(policyFee.getFeeAmountVer().multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            planPayItem.setInquireFee(policyFee.getFeeAmountInq().multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            planPayItem.setOtherFee(policyFee.getFeeAmountOth().multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            planPayItem.setSpecialSurveyFee(policyFee.getFeeAmountSpe().multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
        }
        BigDecimal arbitrageFee = sum(planFeeSum.getFeeAmountArb(), planPayItem.getArbitrageFee());
        BigDecimal inquireFee = sum(planFeeSum.getFeeAmountInq(), planPayItem.getInquireFee());
        BigDecimal otherFee = sum(planFeeSum.getFeeAmountOth(), planPayItem.getOtherFee());
        BigDecimal pecialSurveyFee = sum(planFeeSum.getFeeAmountSpe(), planPayItem.getSpecialSurveyFee());
        BigDecimal commonEstimateFee = sum(planFeeSum.getFeeAmountCom(), planPayItem.getCommonEstimateFee());
        BigDecimal executeFee = sum(planFeeSum.getFeeAmountExe(), planPayItem.getExecuteFee());
        BigDecimal lawsuitFee = sum(planFeeSum.getFeeAmountLaws(), planPayItem.getLawsuitFee());
        BigDecimal lawyerFee = sum(planFeeSum.getFeeAmountLawy(), planPayItem.getLawyerFee());
        BigDecimal verifyFee = sum(planFeeSum.getFeeAmountVer(), planPayItem.getVerifyFee());
        planFeeSum.setFeeAmountArb(arbitrageFee);
        planFeeSum.setFeeAmountAwa(inquireFee);
        planFeeSum.setFeeAmountAwa(otherFee);
        planFeeSum.setFeeAmountAwa(pecialSurveyFee);
        planFeeSum.setFeeAmountCom(commonEstimateFee);
        planFeeSum.setFeeAmountExe(executeFee);
        planFeeSum.setFeeAmountLaws(lawsuitFee);
        planFeeSum.setFeeAmountLawy(lawyerFee);
        planFeeSum.setFeeAmountVer(verifyFee);
        BigDecimal planFee = sum(planPayItem.getArbitrageFee(), planPayItem.getLawsuitFee(), planPayItem.getCommonEstimateFee(),
                planPayItem.getLawyerFee(), planPayItem.getExecuteFee(), planPayItem.getVerifyFee(), planPayItem.getInquireFee(),
                planPayItem.getOtherFee(),planPayItem.getSpecialSurveyFee());
        return planFee;
    }

    private void distributeFeeForDuty(PlanPayDTO planPayItem, Boolean hasPrePay) {
        List<DutyPayDTO> dutyPays = planPayItem.getDutyPayArr();
        FeeAmountVO dutyFeeSum = new FeeAmountVO();
        for (DutyPayDTO dutyPay : planPayItem.getDutyPayArr()) {
            BigDecimal ratio = BigDecimal.ZERO;
            if (!hasPrePay && BigDecimalUtils.isGreaterZero(planPayItem.getSettleAmount())) {
                ratio = nvl(dutyPay.getSettleAmount(), 0).divide(planPayItem.getSettleAmount(), 8, BigDecimal.ROUND_DOWN);
            } else {
                ratio = BigDecimal.ONE.divide(new BigDecimal(dutyPays.size()), 8, BigDecimal.ROUND_DOWN);
            }
            LogUtil.audit("#分摊费用-根据理算金额分摊责任的费用 " + dutyPay.getDutyCode());
            LogUtil.audit("责任分摊比例:" + ratio);
            setDutyPayFee(dutyPay, planPayItem, ratio, dutyPays, dutyFeeSum);
        }
    }

    private void distributeFeeByEstimatePlan(PolicyPayDTO policyPayInfo, FeeAmountVO policyFee, FeeAmountVO planFeeSum,
                                             List<PlanPayDTO> planPays, EstimatePolicyDTO ePolicy) {
        for (PlanPayDTO planPayItem : planPays) {
            LogUtil.audit("#分摊费用-根据预估分摊费用distributeFeeForPlan.reportNo={}", policyPayInfo.getReportNo());
            BigDecimal ratio = calculateRatioForPlan(policyPayInfo, planPayItem, ePolicy);
            BigDecimal planFee = setPlanPayFee(planPayItem, policyFee, planPays, ratio, planFeeSum);
            EstimatePlanDTO ePlan = getEstimatePlan(ePolicy, planPayItem);
            distributeFeeForDuty(planPayItem, policyPayInfo, ePlan, planFee);
        }
    }

    private BigDecimal setDutyPayFee(DutyPayDTO dutyPay, PlanPayDTO planPayItem, BigDecimal ratio, List<DutyPayDTO> dutyPays, FeeAmountVO dutyFeeSum) {
        if (dutyPays.indexOf(dutyPay) == dutyPays.size() - 1) {
            dutyPay.setArbitrageFee(nvl(planPayItem.getArbitrageFee(), 0).subtract(dutyFeeSum.getFeeAmountArb()));
            dutyPay.setCommonEstimateFee(nvl(planPayItem.getCommonEstimateFee(), 0).subtract(dutyFeeSum.getFeeAmountCom()));
            dutyPay.setExecuteFee(nvl(planPayItem.getExecuteFee(), 0).subtract(dutyFeeSum.getFeeAmountExe()));
            dutyPay.setLawsuitFee(nvl(planPayItem.getLawsuitFee(), 0).subtract(dutyFeeSum.getFeeAmountLaws()));
            dutyPay.setLawyerFee(nvl(planPayItem.getLawyerFee(), 0).subtract(dutyFeeSum.getFeeAmountLawy()));
            dutyPay.setVerifyAppraiseFee(nvl(planPayItem.getVerifyFee(), 0).subtract(dutyFeeSum.getFeeAmountVer()));
            dutyPay.setInquireFee(nvl(planPayItem.getInquireFee(), 0).subtract(dutyFeeSum.getFeeAmountInq()));
            dutyPay.setOtherFee(nvl(planPayItem.getOtherFee(), 0).subtract(dutyFeeSum.getFeeAmountOth()));
            dutyPay.setSpecialSurveyFee(nvl(planPayItem.getSpecialSurveyFee(), 0).subtract(dutyFeeSum.getFeeAmountSpe()));
        } else {
            dutyPay.setArbitrageFee(nvl(planPayItem.getArbitrageFee(), 0).multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            dutyPay.setCommonEstimateFee(nvl(planPayItem.getCommonEstimateFee(), 0).multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            dutyPay.setExecuteFee(nvl(planPayItem.getExecuteFee(), 0).multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            dutyPay.setLawsuitFee(nvl(planPayItem.getLawsuitFee(), 0).multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            dutyPay.setLawyerFee(nvl(planPayItem.getLawyerFee(), 0).multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            dutyPay.setVerifyAppraiseFee(nvl(planPayItem.getVerifyFee(), 0).multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            dutyPay.setInquireFee(nvl(planPayItem.getInquireFee(), 0).multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            dutyPay.setOtherFee(nvl(planPayItem.getOtherFee(), 0).multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
            dutyPay.setSpecialSurveyFee(nvl(planPayItem.getSpecialSurveyFee(), 0).multiply(ratio).setScale(2, BigDecimal.ROUND_DOWN));
        }
        BigDecimal arbitrageFee = sum(dutyFeeSum.getFeeAmountArb(), dutyPay.getArbitrageFee());
        BigDecimal commonEstimateFee = sum(dutyFeeSum.getFeeAmountCom(), dutyPay.getCommonEstimateFee());
        BigDecimal executeFee = sum(dutyFeeSum.getFeeAmountExe(), dutyPay.getExecuteFee());
        BigDecimal lawsuitFee = sum(dutyFeeSum.getFeeAmountLaws(), dutyPay.getLawsuitFee());
        BigDecimal lawyerFee = sum(dutyFeeSum.getFeeAmountLawy(), dutyPay.getLawyerFee());
        BigDecimal verifyFee = sum(dutyFeeSum.getFeeAmountVer(), dutyPay.getVerifyAppraiseFee());

        BigDecimal inquireFee = sum(dutyFeeSum.getFeeAmountVer(), dutyPay.getInquireFee());
        BigDecimal otherFee = sum(dutyFeeSum.getFeeAmountOth(), dutyPay.getOtherFee());
        BigDecimal specialSurveyFee = sum(dutyFeeSum.getFeeAmountSpe(), dutyPay.getSpecialSurveyFee());
        dutyFeeSum.setFeeAmountArb(arbitrageFee);

        dutyFeeSum.setFeeAmountCom(commonEstimateFee);
        dutyFeeSum.setFeeAmountExe(executeFee);
        dutyFeeSum.setFeeAmountLaws(lawsuitFee);
        dutyFeeSum.setFeeAmountLawy(lawyerFee);
        dutyFeeSum.setFeeAmountVer(verifyFee);
        dutyFeeSum.setFeeAmountInq(inquireFee);
        dutyFeeSum.setFeeAmountOth(otherFee);
        dutyFeeSum.setFeeAmountSpe(specialSurveyFee);
        BigDecimal dutyFee = sum(dutyPay.getArbitrageFee(), dutyPay.getCommonEstimateFee(), dutyPay.getExecuteFee(), dutyPay.getLawsuitFee(),
                dutyPay.getLawyerFee(), dutyPay.getVerifyAppraiseFee(),dutyPay.getInquireFee(),dutyPay.getOtherFee(),dutyPay.getSpecialSurveyFee());
        return dutyFee;
    }

    private BigDecimal calculateRatioForPlan(PolicyPayDTO policyPayInfo, PlanPayDTO planPayInfo, EstimatePolicyDTO ePolicyDTO) {
        BigDecimal ratio = BigDecimal.ZERO;
        if (null == ePolicyDTO) {
            return getRatioByPlanSize(policyPayInfo);
        }
        BigDecimal policyEstimateAmount = ePolicyDTO.getEstimateAmount();
        BigDecimal ePolicyFeeSum = sum(ePolicyDTO.getArbitrageFee(),ePolicyDTO.getInquireFee(),ePolicyDTO.getOtherFee(),ePolicyDTO.getSpecialSurveyFee(),
                ePolicyDTO.getLawsuitFee(), ePolicyDTO.getLawyerFee(), ePolicyDTO.getVerifyAppraiseFee());
        if (BigDecimalUtils.isGreaterZero(ePolicyFeeSum)) {
            for (EstimatePlanDTO ePlanDTO : ePolicyDTO.getEstimatePlanList()) {
                if (ePlanDTO.getPlanCode().equals(planPayInfo.getPlanCode())) {
                    BigDecimal ePlanFeeSum = sum(ePlanDTO.getArbitrageFee(), ePlanDTO.getInquireFee(),ePlanDTO.getOtherFee(),ePlanDTO.getSpecialSurveyFee(),
                            ePlanDTO.getCommonEstimateFee(), ePlanDTO.getExecuteFee(), ePlanDTO.getLawsuitFee(),
                            ePlanDTO.getLawyerFee(), ePlanDTO.getVerifyAppraiseFee());
                    ratio = ePlanFeeSum.divide(ePolicyFeeSum, 8, BigDecimal.ROUND_HALF_UP);
                    LogUtil.audit("按照预估费用,险种分摊比例:" + ratio);
                    return ratio;
                }
            }
        } else if (BigDecimalUtils.isGreaterZero(policyEstimateAmount)) {
            for (EstimatePlanDTO ePlanDTO : ePolicyDTO.getEstimatePlanList()) {
                if (ePlanDTO.getPlanCode().equals(planPayInfo.getPlanCode())) {
                    ratio = nvl(ePlanDTO.getEstimateAmount(), 0).divide(ePolicyDTO.getEstimateAmount(), 8,
                            BigDecimal.ROUND_HALF_UP);
                    LogUtil.audit("按照预估赔付金额,险种分摊比例:" + ratio);
                    return ratio;
                }
            }
        } else {
            return getRatioByPlanSize(policyPayInfo);
        }
        return ratio;
    }

    private void distributeFeeForDuty(PlanPayDTO planPayItem, PolicyPayDTO policyPayInfo, EstimatePlanDTO ePlan, BigDecimal planFee) {
        List<DutyPayDTO> dutyPays = planPayItem.getDutyPayArr();
        FeeAmountVO dutyFeeSum = new FeeAmountVO();
        for (DutyPayDTO dutyPay : planPayItem.getDutyPayArr()) {
            BigDecimal ratio = calculateRatioForDuty(planPayItem, dutyPay, ePlan);
            setDutyPayFee(dutyPay, planPayItem, ratio, dutyPays, dutyFeeSum);
        }
    }

    private BigDecimal calculateRatioForDuty(PlanPayDTO planPayItem, DutyPayDTO dutyPay, EstimatePlanDTO ePlan) {
        BigDecimal ratio = BigDecimal.ZERO;
        if (ePlan == null) {
            return getRatioByDutySize(planPayItem);
        }
        BigDecimal ePlanFeeSum = sum(ePlan.getArbitrageFee(), ePlan.getInquireFee(),ePlan.getOtherFee(),ePlan.getSpecialSurveyFee(), ePlan.getCommonEstimateFee(), ePlan.getExecuteFee(), ePlan.getLawsuitFee(), ePlan.getLawyerFee(), ePlan.getVerifyAppraiseFee());
        BigDecimal planEstimateAmount = ePlan.getEstimateAmount();
        if (BigDecimalUtils.isGreaterZero(ePlanFeeSum)) {
            for (EstimateDutyDTO eDuty : ePlan.getEstimateDutyList()) {
                if (eDuty.getDutyCode().equals(dutyPay.getDutyCode())) {
                    BigDecimal eDutyFeeSum = sum(eDuty.getArbitrageFee(),  ePlan.getInquireFee(),ePlan.getOtherFee(),ePlan.getSpecialSurveyFee(), eDuty.getCommonEstimateFee(), eDuty.getExecuteFee(), eDuty.getLawsuitFee(), eDuty.getLawyerFee(), eDuty.getVerifyAppraiseFee());
                    ratio = eDutyFeeSum.divide(ePlanFeeSum, 8, BigDecimal.ROUND_HALF_UP);
                    return ratio;
                }
            }
        } else if (BigDecimalUtils.isGreaterZero(planEstimateAmount)) {
            for (EstimateDutyDTO eDuty : ePlan.getEstimateDutyList()) {
                if (eDuty.getDutyCode().equals(dutyPay.getDutyCode())) {
                    ratio = nvl(eDuty.getEstimateAmount(), 0).divide(ePlan.getEstimateAmount(), 8, BigDecimal.ROUND_HALF_UP);
                    return ratio;
                }
            }
        } else {
            return getRatioByDutySize(planPayItem);
        }
        return ratio;
    }

    private EstimatePlanDTO getEstimatePlan(EstimatePolicyDTO ePolicy,PlanPayDTO planPayItem){
        if(ePolicy == null){
            return null;
        }
        for (EstimatePlanDTO ePlan : ePolicy.getEstimatePlanList()) {
            if (ePlan.getPlanCode().equals(planPayItem.getPlanCode())) {
                return ePlan;
            }
        }
        return null;
    }

    private BigDecimal getRatioByDutySize(PlanPayDTO planPayItem){
        int dutyCount = planPayItem.getDutyPayArr().size();
        if(dutyCount == 0){
            return BigDecimal.ZERO;
        }
        BigDecimal ratio = new BigDecimal(1).divide(new BigDecimal(dutyCount), 8, BigDecimal.ROUND_HALF_UP);
        return ratio;
    }

    @Override
    public void settle(SettlesFormVO settlesFormDTO) {
        String reportNo = settlesFormDTO.getReportNo();
        Integer caseTimes = settlesFormDTO.getCaseTimes();
        policyPayService.sendSettles(settlesFormDTO);
        // 取消负数重开
        if(BaseConstant.STRING_1.equals(settlesFormDTO.getIsNegativeRestartCancel())){
            restartCaseService.negativeRestartCancel(reportNo,caseTimes);
        }
        LogUtil.audit("#理算·开始发送赔付信息成功##报案号{}，赔付次数{}", reportNo, caseTimes);
        // 理算结束后开始自动核赔 异常捕获
        try {
            LogUtil.audit("autoVerify:案件{} {} ,自动核赔开始：", reportNo, caseTimes);
            List<PolicyInfoDTO> policyInfoListByReportNo = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
            // 调用自核规则返回true时封装
            ClaimRuleResultDTO claimRuleResultDTO;
            if(investmentProductCode.contains(policyInfoListByReportNo.get(0).getProductCode())){
                claimRuleResultDTO = autoRuleService.IndependentVerifyRule(reportNo, caseTimes
                        , policyInfoListByReportNo.get(0).getInsuranceBeginTime()
                        , policyInfoListByReportNo.get(0).getInsuranceEndTime());
            }else {
                claimRuleResultDTO = autoRuleService.executeVerifySettleRule(reportNo, caseTimes);
            }
            LogUtil.audit("autoVerify:案件{} {} ,自核返回结果：{}", reportNo, caseTimes, claimRuleResultDTO.isAutoPass());
            if(claimRuleResultDTO.isAutoPass()){
                LogUtil.audit("autoVerify:案件{} {} ,自动核赔业务开始：", reportNo, caseTimes);
                verifyService.autoVerify(reportNo,caseTimes);
                autoRuleRecordService.updateRuleResult(reportNo,caseTimes,BpmConstants.OC_SETTLE_REVIEW, ConstValues.YES);
                LogUtil.audit("autoVerify:案件{} {} ,自动核赔业务结束：");
            }
        } catch (GlobalBusinessException ge){
            // 手动异常不要告警了
            LogUtil.audit("autoVerify:案件{} {} ,自动核赔校验异常：{}", reportNo, caseTimes,ge.getMessage());
            autoRuleRecordService.updateRuleResult(reportNo,caseTimes,BpmConstants.OC_SETTLE_REVIEW,ConstValues.NO);
        } catch (Exception e){
            LogUtil.audit("autoVerify:案件{} {} ,自动核赔异常：{}", reportNo, caseTimes,e.getMessage());
            LogUtil.error("自动核赔异常：", e);
            autoRuleRecordService.updateRuleResult(reportNo,caseTimes,BpmConstants.OC_SETTLE_REVIEW,ConstValues.NO);
        }
    }

    /**
     * 获取风险预警信息
     *
     * @param reportNo
     * @param caseTimes
     */
    @Override
    public RiskWarningVO getRiskWarning(String reportNo, Integer caseTimes) {
        RiskWarningVO riskWarningVO = new RiskWarningVO();
        Map<String, String> tpaRiskWarnMap = new HashMap<>();
        List<MedicalBillInfoVO> medicalBillInfoVOS = medicalBillService.getMedicalBillInfoForPrint(reportNo, caseTimes);
        for (MedicalBillInfoVO medicalBillInfoVO :
                Optional.ofNullable(medicalBillInfoVOS).orElseGet(Collections::emptyList)) {
            List<MedicalBillDetailDTO> medicalBillDetailDTOList = medicalBillInfoVO.getMedicalBillDetailDTOList();
            if (CollectionUtils.isNotEmpty(medicalBillDetailDTOList)) {
                for (MedicalBillDetailDTO medicalBillDetailDTO : medicalBillDetailDTOList) {
                    String[] splitArr =
                            org.apache.commons.lang3.StringUtils.stripToEmpty(medicalBillDetailDTO.getCostComment()).trim().split(Constants.SEPARATOR);
                    for (String item : splitArr) {
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(item)
                                && org.apache.commons.lang3.StringUtils.equalsAny(item,
                                BillEnum.RISK_BILL_204.getCode(),
                                BillEnum.RISK_BILL_205.getCode(),
                                BillEnum.RISK_BILL_208.getCode(),
                                BillEnum.RISK_BILL_209.getCode(),
                                BillEnum.RISK_BILL_210.getCode(),
                                BillEnum.RISK_BILL_211.getCode(),
                                BillEnum.RISK_BILL_212.getCode(),
                                BillEnum.RISK_BILL_213.getCode())) {
                            tpaRiskWarnMap.put(item, "涉及" + BillEnum.getName(item));
                        }
                    }
                }
            }
        }
        if (MapUtils.isNotEmpty(tpaRiskWarnMap)) {
            riskWarningVO.setTpaRiskInfo(String.join(";\n", tpaRiskWarnMap.values()));
        }

        // 这里要默认查自动理算自核
        AutoRuleMainInfoEntity ruleRecord = autoRuleRecordService.getRuleRecord(reportNo, caseTimes, BpmConstants.OC_MANUAL_SETTLE);
        if (null != ruleRecord) {
            if (CollectionUtils.isNotEmpty(ruleRecord.getAutoRuleDetailInfoEntities())) {
                String ruleRiskInfo =
                        ruleRecord.getAutoRuleDetailInfoEntities().stream()
                                .filter(item -> "999999".equals(item.getRuleCode()) || null != RuleTypeEnums.getRuleType(item.getRuleCode()))
                                .map(AutoRuleDetailInfoEntity::getRuleMessage).collect(Collectors.joining(";\n"));
                riskWarningVO.setRuleRiskInfo(ruleRiskInfo);
            }
        }
        return riskWarningVO;
    }

    /**
     * 获取不能自动核赔原因
     *
     * @param reportNo
     * @param caseTimes
     */
    @Override
    public RiskWarningVO getRiskWarningVerify(String reportNo, Integer caseTimes) {
        RiskWarningVO riskWarningVO = new RiskWarningVO();
        // 这里要默认查自动理算自核
        AutoRuleMainInfoEntity ruleRecord = autoRuleRecordService.getRuleRecord(reportNo, caseTimes, BpmConstants.OC_SETTLE_REVIEW);
        if (null != ruleRecord) {
            if (CollectionUtils.isNotEmpty(ruleRecord.getAutoRuleDetailInfoEntities())) {
                String ruleRiskInfo =
                        ruleRecord.getAutoRuleDetailInfoEntities().stream()
                                .map(AutoRuleDetailInfoEntity::getRuleMessage).collect(Collectors.joining("\n"));
                riskWarningVO.setRuleRiskInfo(ruleRiskInfo);
            }
        }
        return riskWarningVO;
    }


    @Override
    public ClmsSmsTemplateDTO getSmsTemplate(String templateClass) {
        return clmsSmsTemplateMapper.getSmsTemplate(templateClass);
    }

    @Override
    public void modifySmsTemplate(String templateDesc,String templateClass) {
        clmsSmsTemplateMapper.modifySmsTemplate(templateDesc, templateClass);
    }
}
