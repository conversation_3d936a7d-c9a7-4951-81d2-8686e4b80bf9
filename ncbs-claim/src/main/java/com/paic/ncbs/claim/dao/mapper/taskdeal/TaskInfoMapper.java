package com.paic.ncbs.claim.dao.mapper.taskdeal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.model.vo.taskdeal.*;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Date;
import java.util.List;
import java.util.Map;

@MapperScan
public interface TaskInfoMapper extends BaseMapper<TaskInfoDTO> {

    void addTaskInfo(TaskInfoDTO taskInfoDTO);

    void modifyTaskInfo(TaskInfoDTO taskInfoDTO);

    void modifyTaskInfoAssigner(TaskInfoDTO taskInfoDTO);

    List<Map<String, String>> getTaskList(@Param("uid") String uid, @Param("defKey")String defKey);

    List<TaskInfoVO> getTaskInfo(TaskInfoDTO taskInfoDTO);

    List<TaskInfoVO> getOutInvestigateTaskInfo(TaskInfoDTO taskInfoDTO);

    /**
     * 获取委托任务信息
     * @param taskInfoDTO 任务信息DTO
     * @return 委托任务信息列表
     */
    List<TaskInfoVO> getEntrustmentTaskInfo(TaskInfoDTO taskInfoDTO);

    int getTaskRecord(TaskInfoDTO taskInfoDTO);

    int getTaskCount(@Param("defKey")String defKey);

    String getIdAhcsTaskInfo(@Param("taskId") String taskId, @Param("assigner") String assigner,@Param("taskDefinitionBpmKey") String taskDefinitionBpmKey);

     String getMajoyProcessDepartmentByReport(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

     TaskInfoDTO findLatestByReportNoAndBpmKey(@Param("reportNo") String reportNo,
                                                     @Param("caseTimes") Integer caseTimes,
                                                     @Param("taskDefinitionBpmKey") String taskDefinitionBpmKey);

    TaskInfoDTO historyDocumentFullDate(@Param("reportNo") String reportNo,
                                              @Param("caseTimes") Integer caseTimes,
                                              @Param("taskDefinitionBpmKey") String taskDefinitionBpmKey);

    void modifyTaskInfoByDefKey(TaskInfoDTO taskInfoDTO);

    void suspendOrActiveTask(TaskInfoDTO taskInfoDTO);
    String getConclusion(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    TaskInfoDTO getTaskInfoForInvestigate(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    String getNoFinishScoreTask(@Param("reportNo") String reportNo,@Param("caseTimes")  Integer caseTimes);

    Integer hasNotFinishTaskByTaskKey(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                      @Param("taskKey") String taskKey,@Param("taskId") String taskId);

    Integer getSuspendTaskCount(TaskInfoDTO taskInfoDTO);

    // update zjtang 获取未处理状态的主流程key
    String getPendingTaskCount(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    void reAssign(@Param("taskId")String taskId,
                  @Param("userId")String userId,
                  @Param("userName")String userName,
                  @Param("comCode")String comCode);

    void updateTask(@Param("taskId")String taskId,@Param("status")String status);

    void updateTaskReback(@Param("taskId")String taskId,@Param("status")String status);


    Integer getNoFinishTaskCount(@Param("userId") String userId,@Param("comCode") String comCode);

    TaskInfoDTO ownNewSuspendProcess(@Param("reportNo") String reportNo,@Param("caseTimes") Integer caseTimes,@Param("caseProcess") List<String>  caseProcess,@Param("status")String  status);

    List<WorkBenchTaskVO> getWorkBenchTaskList(WorkBenchTaskQueryVO workBenchTaskQueryVO);

    String getReportNoById(@Param("taskId") String taskId);

    List<String> getPolicyHolder(@Param("reportNo") String reportNo);

    Date getAccidentDate(@Param("reportNo") String reportNo);

    String getAccidentType(@Param("reportNo") String reportNo);

    TaskInfoDTO selectTaskByTaskId(@Param("taskId")String taskId);

    void suspendNotUpdateDate(TaskInfoDTO dto);

    String getAssigner(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    TaskInfoDTO getTaskDtoByTaskId(@Param("taskId")String taskId);

    void updateTaskDtoByTaskId(@Param("taskId")String taskId,@Param("userId")String userId);

    TaskInfoDTO checkWorkflow(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes, @Param("bpmConstants") String bpmConstants, @Param("status") String status);

    /**
     * 查询处理人信息
     * @param reportNo
     * @param caseTimes
     * @param taskKey
     * @return
     */
    TaskInfoDTO getTaskAssignerName(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                      @Param("taskKey") String taskKey);

    /**
     * 更新处理人为TPA分配的处理人
     * @param taskInfoDTO
     */
    void updateTaskAssigner(TaskInfoDTO taskInfoDTO);

    /**
     * 查询报案跟踪数据是否可以操作
     * @param reportNo
     * @param caseTimes
     * @return
     */
    Integer getTpaTaskInfo(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes);

    /**
     * 查询报案号是否已分配给了TPA
     * @param reportNo
     * @param caseTimes
     * @param taskKey
     * @return
     */
    TaskInfoDTO getTaskIsTPA(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                    @Param("taskKey") String taskKey);


    String getTaskId(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,@Param("taskDefinitionBpmKey") String taskDefinitionBpmKey);

    int deleteTaskInfo(TaskInfoDTO taskInfo);

    int deleteQualityTaskInfo(TaskInfoDTO taskInfo);
    /**
     * 查询收单完成时间
     * @param reportNo
     * @param caseTimes
     * @return
     */
    TaskInfoDTO getCompleteDate(String reportNo, int caseTimes);

    /**
     * 获取主任务挂起后，未处理的任务信息
     * @param taskInfoDTO
     * @return
     */
    List<TaskInfoVO> getUndoTaskInfoList(TaskInfoDTO taskInfoDTO);

    List<ClaimTaskInfoToESVO> getClaimESTaskInfoList(@Param("idAhcsTaskInfo") String idAhcsTaskInfo);

    List<ClaimESPolicyInfoVO> getClaimESPolicyInfoVOList(@Param("reportNo") String reportNo);

    void modifyTaskInfoOnES(@Param("reportNo") String reportNo);

    String getLatestTaskId(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,@Param("taskKey") String taskKey);

    void pushAllTaskInfoToES(@Param("status") String status,@Param("updatedStarTDate") String updatedStarTDate,@Param("updatedEndDate") String updatedEndDate);

    List<TaskInfoDTO> getUndealCaseList();

    ClaimInfoToESVO getClaimInfoToES(String idCase);

    List<TaskInfoDTO> findTaskByReportNoAndBpmKey(@Param("reportNo") String reportNo,
                                                  @Param("taskDefinitionBpmKey") String taskDefinitionBpmKey);

    /**
     * 查询任务表数据
     * @param taskInfoDTO
     * @return
     */
    List<TaskInfoDTO> getTaskInfoList(TaskInfoDTO taskInfoDTO);

    /**
     * 修改
     */
    void updateTaskInfo(TaskInfoDTO taskInfoDTO);

    /**
     * 查询处理人信息
     * @param reportNo
     * @param caseTimes
     * @param taskKey
     * @return
     */
    TaskInfoDTO getTaskAssignerNameNew(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                    @Param("taskKey") String taskKey);

    TaskInfoDTO selectTaskByTaskKey(@Param("taskId")String taskId, @Param("taskKey") String taskKey);

    List<WorkBenchTaskVO> getQualityCaseList(ClaimQuanlityCaseVO workBenchTaskQueryVO);

    String getTaskAssignerNameq(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                               @Param("nodeCode") String nodeCode);
    String getTaskAssignerNameHP(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes,
                                @Param("nodeCode") String nodeCode);
}
