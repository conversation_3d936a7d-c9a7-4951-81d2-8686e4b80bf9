package com.paic.ncbs.claim.controller.follow;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.follow.ClmsUserFollowCaseVO;
import com.paic.ncbs.claim.service.follow.ClmsUserFollowCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 关注表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@Api(tags = "我的关注")
@RestController
@RequestMapping("/follow/clmsUserFollowCaseAction")
public class ClmsUserFollowCaseController {

    @Autowired
    private ClmsUserFollowCaseService caseService;

    @ApiOperation("我的关注-关注/取消关注")
    @PostMapping(value="/followUnfollow")
    public ResponseResult<Object> followUnfollow(@RequestBody ClmsUserFollowCaseVO caseVO) throws GlobalBusinessException {
        caseService.followUnfollow(caseVO);
        return ResponseResult.success();
    }

    @ApiOperation("我的关注-获取当前用户关注列表")
    @GetMapping(value="/getClmsUserFollowCase")
    public ResponseResult<List<ClmsUserFollowCaseVO>> getClmsUserFollowCase() throws GlobalBusinessException {
        return ResponseResult.success(caseService.getClmsUserFollowCase());
    }

    @ApiOperation("我的关注-查询案件关注状态")
    @GetMapping(value="/getCaseFollowStatus/{reportNo}/{caseTimes}")
    public ResponseResult<ClmsUserFollowCaseVO> getCaseFollowStatus(@PathVariable("reportNo") String reportNo,
                                                                    @PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        return ResponseResult.success(caseService.getCaseFollowStatus(reportNo, caseTimes));
    }

}
