package com.paic.ncbs.claim.model.dto.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 报案信息DTO
 */
public class ReportInfoDTO {

    private List<String> superPolicyNos;

    private List<String> policyNos;
    private List<String> idPlyRiskPersons;
    private List<String> inputPolicyNos;
    private String outBizNo;
    private String outReportNo;
    private String planCode;
    private String dutyCode;
    private String dutyDetailCode;
    private String reportMode;
    // 出险现状
    private String insuredApplyStatus;
    // 出险类型
    private String insuredApplyType;
    /**
     * 治疗类型（门诊、住院）
     */
    private String therapyType;
    private String accidentType;
    private String clientNo;
    private String openId;
    private String certificateNo;
    private String clientName;
    private String clientMobile;
    private String birthday;
    private String certificateType;
    private String clientCertificateType;
    private String reportNo;
    private String caseTimes;
    private String caseClass;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date accidentDate;
    private String isSuffice;
    private String remark;
    private String priorityReason;
    private String reportAcceptUm;
    /**
     * 联系人相关信息
     */
    private List<LinkManEntity> linkManList = new ArrayList<LinkManEntity>();
    private String departmentCode;
    private String sendMessage;
    private String isSelfHelp;
    private String isHugeAccident;
    /**
     * 重灾名称
     */
    private String hugeAccidentName;
    private String hugeAccidentType;
    private String hugeAccidentId;
    private String caseType;
    private String accidentStatusDetails;
    private String costEstimate;
    /**
     * 出险原因大类
     */
    private String accidentCauseLevel1;
    /**
     * 出险原因明细类
     */
    private String accidentCauseLevel2;
    /**
     * 新建报案出险原因大类
     */
    private String accidentCauseLevel3;
    /**
     * 新建报案出险原因明细类
     */
    private String accidentCauseLevel4;
    private String accidentDetail;
    private String accidentCity;
    private String accidentCounty;
    private String accidentProvince;
    private String accidentArea;
    private String accidentNation;
    private String accidentPlace;
    private String whetherOutSideAccident;
    private String succorService;
    private String succorServiceCode;
    private String PartnerCode;
    private String isSpecialReport;
    private String documentGroupId;
    private String fileId;
    private String migrateFrom;
    private String reportTravelName;
    private String accidentExtendInfo;
    private String caseSign;
    private String isPolicyReport;
    private String isTransReport;
    private String isAbnormal;
    private String succorCompany;
    private String succorCompanyName;
    private String succorServiceLevel;
    private String succorServiceName;
    private String diseases;
    private String accidentPersonPermanentAddress;
    private String billAccountType;
    private String billAccountNo;
    private Date dischDate;
    private String hospitalName;
    private String hospitalCode;
    private String isSplitReport;
    private String injuryReasonCode;
    // 就诊原因
    private String visitReason;
    /**
     * （非人伤）行李延误
     */
    private ReportAccidentBaggageEntity reportAccidentBaggage;
    /**
     * （非人伤）考试不通过
     */
    private ReportAccidentExamEntity reportAccidentExam;
    /**
     * （非人伤）航班延误
     */
    private ReportAccidentFlightEntity reportAccidentFlight;
    /**
     * （非人伤）财产损失
     */
    private ReportAccidentLossEntity reportAccidentLoss;
    /**
     * （非人伤）其他非人伤
     */
    private ReportAccidentOtherEntity reportAccidentOther;
    /**
     * （非人伤）其他交通工具延误
     */
    private ReportAccidentTrafficEntity reportAccidentTraffic;
    /**
     * （非人伤）旅行变更
     */
    private ReportAccidentTravelEntity reportAccidentTravel;
    /**
     * （非人伤）宠物（选了宠物，其他几项非人伤无法选）
     */
    private ReportAccidentPetEntity reportAccidentPet;

    private String medicalType;
    private List<AntPolicyDTO> antPolicys;
    private HashMap<String, Object> clobAccidentExtend;
    private String caseChannel;
    private String linkmanUm;
    private String linkmanTel;
    private List<String> subCaseClass;
    private String riskGroupNo;
    private String riskGroupName;

    /**
     * 标的信息
     */
    private List<CaseRiskPropertyDTO> riskPropertyList;

    /**
     * 损失类别
     */
    private String lossClass;


    public List<CaseRiskPropertyDTO> getRiskPropertyList() {
        return riskPropertyList;
    }

    public void setRiskPropertyList(List<CaseRiskPropertyDTO> riskPropertyList) {
        this.riskPropertyList = riskPropertyList;
    }

    public String getLossClass() {
        return lossClass;
    }

    public void setLossClass(String lossClass) {
        this.lossClass = lossClass;
    }
    public List<String> getSuperPolicyNos() {
        return superPolicyNos;
    }

    public void setSuperPolicyNos(List<String> superPolicyNos) {
        this.superPolicyNos = superPolicyNos;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    public String getDutyDetailCode() {
        return dutyDetailCode;
    }

    public void setDutyDetailCode(String dutyDetailCode) {
        this.dutyDetailCode = dutyDetailCode;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo;
    }

    public String getClientMobile() {
        return clientMobile;
    }

    public void setClientMobile(String clientMobile) {
        this.clientMobile = clientMobile;
    }

    public String getReportMode() {
        return reportMode;
    }

    public void setReportMode(String reportMode) {
        this.reportMode = reportMode;
    }

    public String getWhetherOutSideAccident() {
        return whetherOutSideAccident;
    }

    public void setWhetherOutSideAccident(String whetherOutSideAccident) {
        this.whetherOutSideAccident = whetherOutSideAccident;
    }

    public String getAccidentPlace() {
        return accidentPlace;
    }

    public void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace;
    }

    public String getAccidentCity() {
        return accidentCity;
    }

    public void setAccidentCity(String accidentCity) {
        this.accidentCity = accidentCity;
    }

    public String getAccidentCounty() {
        return accidentCounty;
    }

    public void setAccidentCounty(String accidentCounty) {
        this.accidentCounty = accidentCounty;
    }

    public String getAccidentProvince() {
        return accidentProvince;
    }

    public void setAccidentProvince(String accidentProvince) {
        this.accidentProvince = accidentProvince;
    }

    public String getAccidentArea() {
        return accidentArea;
    }

    public void setAccidentArea(String accidentArea) {
        this.accidentArea = accidentArea;
    }

    public String getAccidentNation() {
        return accidentNation;
    }

    public void setAccidentNation(String accidentNation) {
        this.accidentNation = accidentNation;
    }

    public List<String> getPolicyNos() {
        return policyNos;
    }

    public void setPolicyNos(List<String> policyNos) {
        this.policyNos = policyNos;
    }

    public String getAccidentDetail() {
        return accidentDetail;
    }

    public void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail;
    }

    public String getHugeAccidentType() {
        return hugeAccidentType;
    }

    public void setHugeAccidentType(String hugeAccidentType) {
        this.hugeAccidentType = hugeAccidentType;
    }

    public String getCostEstimate() {
        return costEstimate;
    }

    public void setCostEstimate(String costEstimate) {
        this.costEstimate = costEstimate;
    }

    public String getAccidentStatusDetails() {
        return accidentStatusDetails;
    }

    public void setAccidentStatusDetails(String accidentStatusDetails) {
        this.accidentStatusDetails = accidentStatusDetails;
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public String getIsHugeAccident() {
        return isHugeAccident;
    }

    public void setIsHugeAccident(String isHugeAccident) {
        this.isHugeAccident = isHugeAccident;
    }

    public String getHugeAccidentName() {
        return hugeAccidentName;
    }

    public void setHugeAccidentName(String hugeAccidentName) {
        this.hugeAccidentName = hugeAccidentName;
    }

    public String getInsuredApplyStatus() {
        return insuredApplyStatus;
    }

    public void setInsuredApplyStatus(String insuredApplyStatus) {
        this.insuredApplyStatus = insuredApplyStatus;
    }

    public String getTherapyType() {
        return therapyType;
    }

    public void setTherapyType(String therapyType) {
        this.therapyType = therapyType;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public String getIsSelfHelp() {
        return isSelfHelp;
    }

    public void setIsSelfHelp(String isSelfHelp) {
        this.isSelfHelp = isSelfHelp;
    }

    public String getSendMessage() {
        return sendMessage;
    }

    public void setSendMessage(String sendMessage) {
        this.sendMessage = sendMessage;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getClientCertificateType() {
        return clientCertificateType;
    }

    public void setClientCertificateType(String clientCertificateType) {
        this.clientCertificateType = clientCertificateType;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(String caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getCaseClass() {
        return caseClass;
    }

    public void setCaseClass(String caseClass) {
        this.caseClass = caseClass;
    }

    public Date getAccidentDate() {
        return accidentDate;
    }

    public void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public String getIsSuffice() {
        return isSuffice;
    }

    public void setIsSuffice(String isSuffice) {
        this.isSuffice = isSuffice;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPriorityReason() {
        return priorityReason;
    }

    public void setPriorityReason(String priorityReason) {
        this.priorityReason = priorityReason;
    }

    public String getReportAcceptUm() {
        return reportAcceptUm;
    }

    public void setReportAcceptUm(String reportAcceptUm) {
        this.reportAcceptUm = reportAcceptUm;
    }

    public List<LinkManEntity> getLinkManList() {
        return linkManList;
    }

    public void setLinkManList(List<LinkManEntity> linkManList) {
        this.linkManList = linkManList;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getHugeAccidentId() {
        return hugeAccidentId;
    }

    public void setHugeAccidentId(String hugeAccidentId) {
        this.hugeAccidentId = hugeAccidentId;
    }

    public ReportAccidentBaggageEntity getReportAccidentBaggage() {
        return reportAccidentBaggage;
    }

    public void setReportAccidentBaggage(ReportAccidentBaggageEntity reportAccidentBaggage) {
        this.reportAccidentBaggage = reportAccidentBaggage;
    }

    public ReportAccidentExamEntity getReportAccidentExam() {
        return reportAccidentExam;
    }

    public void setReportAccidentExam(ReportAccidentExamEntity reportAccidentExam) {
        this.reportAccidentExam = reportAccidentExam;
    }

    public ReportAccidentFlightEntity getReportAccidentFlight() {
        return reportAccidentFlight;
    }

    public void setReportAccidentFlight(ReportAccidentFlightEntity reportAccidentFlight) {
        this.reportAccidentFlight = reportAccidentFlight;
    }

    public ReportAccidentLossEntity getReportAccidentLoss() {
        return reportAccidentLoss;
    }

    public void setReportAccidentLoss(ReportAccidentLossEntity reportAccidentLoss) {
        this.reportAccidentLoss = reportAccidentLoss;
    }

    public ReportAccidentOtherEntity getReportAccidentOther() {
        return reportAccidentOther;
    }

    public void setReportAccidentOther(ReportAccidentOtherEntity reportAccidentOther) {
        this.reportAccidentOther = reportAccidentOther;
    }

    public ReportAccidentTrafficEntity getReportAccidentTraffic() {
        return reportAccidentTraffic;
    }

    public void setReportAccidentTraffic(ReportAccidentTrafficEntity reportAccidentTraffic) {
        this.reportAccidentTraffic = reportAccidentTraffic;
    }

    public ReportAccidentTravelEntity getReportAccidentTravel() {
        return reportAccidentTravel;
    }

    public void setReportAccidentTravel(ReportAccidentTravelEntity reportAccidentTravel) {
        this.reportAccidentTravel = reportAccidentTravel;
    }

    public String getSuccorService() {
        return succorService;
    }

    public void setSuccorService(String succorService) {
        this.succorService = succorService;
    }

    public String getSuccorServiceCode() {
        return succorServiceCode;
    }

    public void setSuccorServiceCode(String succorServiceCode) {
        this.succorServiceCode = succorServiceCode;
    }

    public ReportAccidentPetEntity getReportAccidentPet() {
        return reportAccidentPet;
    }

    public void setReportAccidentPet(ReportAccidentPetEntity reportAccidentPet) {
        this.reportAccidentPet = reportAccidentPet;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getPartnerCode() {
        return PartnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        PartnerCode = partnerCode;
    }

    public String getIsSpecialReport() {
        return isSpecialReport;
    }

    public void setIsSpecialReport(String isSpecialReport) {
        this.isSpecialReport = isSpecialReport;
    }

    public String getDocumentGroupId() {
        return documentGroupId;
    }

    public void setDocumentGroupId(String documentGroupId) {
        this.documentGroupId = documentGroupId;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getMigrateFrom() {
        return migrateFrom;
    }

    public void setMigrateFrom(String migrateFrom) {
        this.migrateFrom = migrateFrom;
    }

    public String getAccidentExtendInfo() {
        return accidentExtendInfo;
    }

    public void setAccidentExtendInfo(String accidentExtendInfo) {
        this.accidentExtendInfo = accidentExtendInfo;
    }

    public String getReportTravelName() {
        return reportTravelName;
    }

    public void setReportTravelName(String reportTravelName) {
        this.reportTravelName = reportTravelName;
    }

    public String getCaseSign() {
        return caseSign;
    }

    public void setCaseSign(String caseSign) {
        this.caseSign = caseSign;
    }

    public String getIsPolicyReport() {
        return isPolicyReport;
    }

    public void setIsPolicyReport(String isPolicyReport) {
        this.isPolicyReport = isPolicyReport;
    }

    public String getIsTransReport() {
        return isTransReport;
    }

    public void setIsTransReport(String isTransReport) {
        this.isTransReport = isTransReport;
    }

    public String getIsAbnormal() {
        return isAbnormal;
    }

    public void setIsAbnormal(String isAbnormal) {
        this.isAbnormal = isAbnormal;
    }

    public String getSuccorCompany() {
        return succorCompany;
    }

    public void setSuccorCompany(String succorCompany) {
        this.succorCompany = succorCompany;
    }

    public String getSuccorCompanyName() {
        return succorCompanyName;
    }

    public void setSuccorCompanyName(String succorCompanyName) {
        this.succorCompanyName = succorCompanyName;
    }

    public String getSuccorServiceLevel() {
        return succorServiceLevel;
    }

    public void setSuccorServiceLevel(String succorServiceLevel) {
        this.succorServiceLevel = succorServiceLevel;
    }

    public String getSuccorServiceName() {
        return succorServiceName;
    }

    public void setSuccorServiceName(String succorServiceName) {
        this.succorServiceName = succorServiceName;
    }

    public List<String> getInputPolicyNos() {
        return inputPolicyNos;
    }

    public void setInputPolicyNos(List<String> inputPolicyNos) {
        this.inputPolicyNos = inputPolicyNos;
    }

    public String getMedicalType() {
        return medicalType;
    }

    public void setMedicalType(String medicalType) {
        this.medicalType = medicalType;
    }

    public String getAccidentPersonPermanentAddress() {
        return accidentPersonPermanentAddress;
    }

    public void setAccidentPersonPermanentAddress(String accidentPersonPermanentAddress) {
        this.accidentPersonPermanentAddress = accidentPersonPermanentAddress;
    }

    public String getBillAccountType() {
        return billAccountType;
    }

    public void setBillAccountType(String billAccountType) {
        this.billAccountType = billAccountType;
    }

    public String getBillAccountNo() {
        return billAccountNo;
    }

    public void setBillAccountNo(String billAccountNo) {
        this.billAccountNo = billAccountNo;
    }

    public String getDiseases() {
        return diseases;
    }

    public void setDiseases(String diseases) {
        this.diseases = diseases;
    }

    public String getOutBizNo() {
        return outBizNo;
    }

    public void setOutBizNo(String outBizNo) {
        this.outBizNo = outBizNo;
    }

    public String getOutReportNo() {
        return outReportNo;
    }

    public void setOutReportNo(String outReportNo) {
        this.outReportNo = outReportNo;
    }

    public List<AntPolicyDTO> getAntPolicys() {
        return antPolicys;
    }

    public void setAntPolicys(List<AntPolicyDTO> antPolicys) {
        this.antPolicys = antPolicys;
    }

    public HashMap<String, Object> getClobAccidentExtend() {
        return clobAccidentExtend;
    }

    public void setClobAccidentExtend(HashMap<String, Object> clobAccidentExtend) {
        this.clobAccidentExtend = clobAccidentExtend;
    }

    public String getCaseChannel() {
        return caseChannel;
    }

    public void setCaseChannel(String caseChannel) {
        this.caseChannel = caseChannel;
    }

    public Date getDischDate() {
        return dischDate;
    }

    public void setDischDate(Date dischDate) {
        this.dischDate = dischDate;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getLinkmanUm() {
        return linkmanUm;
    }

    public void setLinkmanUm(String linkmanUm) {
        this.linkmanUm = linkmanUm;
    }

    public String getLinkmanTel() {
        return linkmanTel;
    }

    public void setLinkmanTel(String linkmanTel) {
        this.linkmanTel = linkmanTel;
    }

    public String getIsSplitReport() {
        return isSplitReport;
    }

    public void setIsSplitReport(String isSplitReport) {
        this.isSplitReport = isSplitReport;
    }

    public List<String> getIdPlyRiskPersons() {
        return idPlyRiskPersons;
    }

    public void setIdPlyRiskPersons(List<String> idPlyRiskPersons) {
        this.idPlyRiskPersons = idPlyRiskPersons;
    }

    public String getInsuredApplyType() {
        return insuredApplyType;
    }

    public void setInsuredApplyType(String insuredApplyType) {
        this.insuredApplyType = insuredApplyType;
    }

    public String getInjuryReasonCode() {
        return injuryReasonCode;
    }

    public void setInjuryReasonCode(String injuryReasonCode) {
        this.injuryReasonCode = injuryReasonCode;
    }

    public String getAccidentCauseLevel1() {
        return accidentCauseLevel1;
    }

    public String getAccidentCauseLevel2() {
        return accidentCauseLevel2;
    }

    public void setAccidentCauseLevel1(String accidentCauseLevel1) {
        this.accidentCauseLevel1 = accidentCauseLevel1;
    }

    public void setAccidentCauseLevel2(String accidentCauseLevel2) {
        this.accidentCauseLevel2 = accidentCauseLevel2;
    }

    public String getVisitReason() {
        return visitReason;
    }

    public void setVisitReason(String visitReason) {
        this.visitReason = visitReason;
    }

    public List<String> getSubCaseClass() {
        return subCaseClass;
    }

    public void setSubCaseClass(List<String> subCaseClass) {
        this.subCaseClass = subCaseClass;
    }

    public String getRiskGroupNo() {
        return riskGroupNo;
    }

    public void setRiskGroupNo(String riskGroupNo) {
        this.riskGroupNo = riskGroupNo;
    }

    public String getRiskGroupName() {
        return riskGroupName;
    }

    public void setRiskGroupName(String riskGroupName) {
        this.riskGroupName = riskGroupName;
    }

    public String getAccidentCauseLevel3() {
        return accidentCauseLevel3;
    }

    public void setAccidentCauseLevel3(String accidentCauseLevel3) {
        this.accidentCauseLevel3 = accidentCauseLevel3;
    }

    public String getAccidentCauseLevel4() {
        return accidentCauseLevel4;
    }

    public void setAccidentCauseLevel4(String accidentCauseLevel4) {
        this.accidentCauseLevel4 = accidentCauseLevel4;
    }
}
