package com.paic.ncbs.claim.mq.producer.impl;

import cn.hutool.json.JSONUtil;
import cn.wesure.cmq.CmqTemplate;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.enums.ReportSubModeEnum;
import com.paic.ncbs.claim.common.enums.SyncCaseStatusEnum;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.casezero.CaseZeroCancelMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.DiagnoseDefineMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonDiagnoseMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.ClaimRejectionApprovalRecordEntityMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper;
import com.paic.ncbs.claim.dao.mapper.verify.VerifyMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.*;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import com.paic.ncbs.claim.model.vo.openapi.OpenReportInfoVO;
import com.paic.ncbs.claim.model.vo.openapi.PolicyVO;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoPageVO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.mqcompensation.MqCompensationService;
import com.paic.ncbs.claim.service.openapi.OpenReportService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RefreshScope
public class MqProducerSyncCaseStatusServiceImpl implements MqProducerSyncCaseStatusService {

    @Autowired
    private EstimateChangeService estimateChangeService;
    @Autowired
    private OpenReportService openReportService;
    @Autowired
    private RiskPropertyService riskPropertyService;

    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private ClaimRejectionApprovalRecordEntityMapper claimRejectionApprovalRecordEntityMapper;
    @Autowired
    private CaseZeroCancelMapper caseZeroCancelMapper;
    @Autowired
    private VerifyMapper verifyMapper;
    @Autowired
    private MqCompensationService mqCompensationService;
    @Autowired
    private PersonDiagnoseMapper personDiagnoseMapper;
    @Autowired
    private DiagnoseDefineMapper diagnoseDefineMapper;
    @Autowired
    private CmqTemplate cmqTemplate;
    @Value("${mq.syncCaseStatus.topic:T-NCBS-CLAIM-SIT}")
    private String topic;
    @Value("${env}")
    private String env;
    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;

    @Value("${mq.tpa.topic}")
    private String tpaTopic;

    @Async("asyncPool")
    @Override
    public void syncCaseStatus(SyncCaseStatusDto dto) {
        LogUtil.audit("同步立案信息校验开始");
        LogUtil.audit("dto==="+ JSONUtil.toJsonStr(dto));
        if (StringUtils.isBlank(dto.getReportNo())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "报案号不能为空");
        }
        if (Objects.isNull(dto.getCaseTimes())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "赔付次数不能为空");
        }
        if (Objects.isNull(dto.getCaseStatus())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "同步状态不能为空");
        }
        if (SyncCaseStatusEnum.ADDMEDIA.equals(dto.getCaseStatus()) && CollectionUtils.isEmpty(dto.getMediaList())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "补充单证不能为空");
        }
        if (SyncCaseStatusEnum.ENDCASE.equals(dto.getCaseStatus()) && StringUtils.isEmpty(dto.getIndemnityConclusion())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "赔付结论不能为空");
        }
        if (SyncCaseStatusEnum.PAYFAILD.equals(dto.getCaseStatus()) && Objects.isNull(dto.getPayFaildInfo())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "支付失败信息不能为空");
        }

        // 屏蔽重开案件
        //重开也推送 modify 2025-08-11
        /*if (dto.getCaseTimes() > 1) {
            return;
        }*/

        OpenReportInfoVO reportInfo = openReportService.getReportInfo(dto.getReportNo(), dto.getCaseTimes());

        // 屏蔽责任险
        if (riskPropertyService.displayRiskProperty(dto.getReportNo(), null)
                && !Arrays.asList(ReportSubModeEnum.OTHER_CHANNEL_REPORT.getType(), ReportSubModeEnum.MINI_PROGRAM.getType()).contains(reportInfo.getReportInfo().getReportSubMode())) {
            return;
        }


        SyncCaseStatusMqDto mqDto = new SyncCaseStatusMqDto();
        mqDto.setStatus(dto.getCaseStatus().getType());
        mqDto.setReportNo(dto.getReportNo());
        mqDto.setCaseTimes(dto.getCaseTimes());
        mqDto.setStatusComment(""); // 暂时写死
        mqDto.setStatusTime(new Date());
        mqDto.setMediaList(dto.getMediaList());
        mqDto.setPayFaildInfo(dto.getPayFaildInfo());
        //收单环节零注提交还未有收单数据，AccidentTime为空不符合渠道要求
        Date accidentDate = reportInfo.getReportInfo().getAccidentDate();
        if (reportInfo.getPeopleHurtVO() != null) {
            if (reportInfo.getPeopleHurtVO().getAccidentVO() != null) {
                reportInfo.getPeopleHurtVO().getAccidentVO().setAccidentTime(accidentDate);
            }
        }
        mqDto.setReportInfoData(reportInfo);
        mqDto.setOperatorName(dto.getOperatorName());
        mqDto.setSupplementsDesc(dto.getSupplementsDesc());
        // 拒赔原因等内容，不予受理说明
        ClaimRejectionApprovalRecordEntity claimRejectionApprovalRecord = claimRejectionApprovalRecordEntityMapper.selectAgreedRecord(dto.getReportNo(), dto.getCaseTimes());
        if (Objects.nonNull(claimRejectionApprovalRecord)) {
            mqDto.setDissmissComment(claimRejectionApprovalRecord.getConclusionCauseDesc());
        } else {
            List<CaseZeroCancelDTO> caseZeroCancelApplyList = caseZeroCancelMapper.getCaseZeroCancelApplyList(dto.getReportNo(), dto.getCaseTimes(), "2");
            if (CollectionUtils.isNotEmpty(caseZeroCancelApplyList)) {
                CaseZeroCancelDTO caseZeroCancelDTO = caseZeroCancelApplyList.get(caseZeroCancelApplyList.size() - 1);
                mqDto.setDissmissComment(caseZeroCancelDTO.getApplyReasonDetails());
            }
        }
        switch (dto.getCaseStatus()) {
            case REGISTER:
                initRegister(dto, mqDto);
                break;
            case PAY:
                initEndcase(dto, mqDto);
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException e) {
                    log.error("推送mq sleep异常");
                }
                break;
            case ENDCASE:
                initEndcase(dto, mqDto);
                break;
            default:
                break;
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            String msg = mapper.writeValueAsString(mqDto);
            LogUtil.audit("env=="+env);
            // 发送mq
            if ("samsung".equals(env)) {
                Stopwatch stopwatch = Stopwatch.createStarted();
                String mqResp = null;
                try {
                    log.info("MQ同步案件信息开始：env={},topic={}, msg={}",env,topic, msg);
                    mqResp = cmqTemplate.sendTopic(topic, msg);
                    log.info("MQ同步案件信息结束：msg={},mqResp={}, useTime={}", msg,mqResp, stopwatch.elapsed(TimeUnit.MILLISECONDS));
                    //一步结案案件mq推送中台
                    if  ((SyncCaseStatusEnum.ENDCASE.equals(dto.getCaseStatus()) || SyncCaseStatusEnum.PAY.equals(dto.getCaseStatus()))
                    && ReportConstant.REPORTSUBMODE_ONLINE_06.equals(reportInfo.getReportInfo().getReportSubMode())) {
                        mqResp = cmqTemplate.sendTopic(tpaTopic, msg);
                        LogUtil.audit("一步结案发送mq topic={},msg={} 发送成功,mqResp={}",tpaTopic,msg,mqResp);
                    }
                } catch (Exception e) {
                    log.error("MqProducerSyncCaseStatusServiceImpl sendTopic error", e);
                    //塞入mq_message 表
                    mqCompensationService.addMqCompensation(topic, msg, BaseConstant.INT_0);
                }

                if (StringUtils.isEmpty(mqResp)) {
                    //塞入mq_message 表
                    mqCompensationService.addMqCompensation(topic, msg, BaseConstant.INT_0);
                }
            }
        } catch (Exception e) {
            log.error("MqProducerSyncCaseStatusServiceImpl.syncCaseStatus error, dto={}", JSON.toJSONString(dto), e);
        }
    }

    /**
     * 立案所需数据
     * @param dto 同步案件状态入参
     * @param mqDto 消息体
     */
    private void initRegister(SyncCaseStatusDto dto, SyncCaseStatusMqDto mqDto) {
        LogUtil.audit("开始组装立案推送clainInfo数据");
        // 立案信息
        ClaimInfoDto claimInfoDto = new ClaimInfoDto();
        List<EstimateChangeDTO> estimateList = estimateChangeService.getPolicyRegisterAmount(dto.getReportNo(), dto.getCaseTimes());
        //诊断信息
        List<PersonDiagnoseDTO> personDiagnoseDTOList =  personDiagnoseMapper.getPersonDiagnoseInfo(dto.getReportNo());
        if (CollectionUtils.isNotEmpty(estimateList)) {
            BigDecimal claimAmount = BigDecimal.ZERO;
            for (EstimateChangeDTO estimateChangeDTO : estimateList) {
                if (Objects.nonNull(estimateChangeDTO.getRegisterAmount())) {
                    claimAmount = claimAmount.add(estimateChangeDTO.getRegisterAmount());
                }
            }
            claimInfoDto.setClaimAmount(claimAmount);
        }
        if(CollectionUtils.isNotEmpty(personDiagnoseDTOList)){
            List<DiagnosisICD> diagnosisICDList = new ArrayList<>();
            for(PersonDiagnoseDTO personDiagnoseDTO : personDiagnoseDTOList){
                DiagnosisICD diagnosisICD = new DiagnosisICD();
                String diagnoseName  = diagnoseDefineMapper.getDiagnoseName(personDiagnoseDTO.getDiagnoseCode());
                diagnosisICD.setDiagnosisICDCode(personDiagnoseDTO.getDiagnoseCode());
                diagnosisICD.setDiagnosisICDName(diagnoseName);
                diagnosisICDList.add(diagnosisICD);
            }
            claimInfoDto.setDiagnosisICDList(diagnosisICDList);
        }
        //发票信息
        MedicalBillInfoDTO medicalBillInfoDTO = new MedicalBillInfoDTO();
        medicalBillInfoDTO.setReportNo(dto.getReportNo());
        medicalBillInfoDTO.setCaseTimes(dto.getCaseTimes());
        MedicalBillInfoPageVO medicalBillInfo = medicalBillInfoMapper.getMedicalBillAllSumAmount(medicalBillInfoDTO);
        if(Objects.nonNull(medicalBillInfo)) {
            claimInfoDto.setBillAmount(medicalBillInfo.getBillAmountSum());
            claimInfoDto.setReasonableAmount(medicalBillInfo.getReasonableAmountSum());
            if(medicalBillInfo.getTherapyType().split(";").length >1) {
                claimInfoDto.setTherapyType(EstimateConstValues.MEN_ZHEN_ZHU_YUAN);
            } else {
                claimInfoDto.setTherapyType(medicalBillInfo.getTherapyType());
            }
        }
        log.info("claimInfoDto："+claimInfoDto);
        mqDto.setClaimInfo(claimInfoDto);
    }

    /**
     * 结案所需数据
     * @param dto 同步案件状态入参
     * @param mqDto 消息体
     */
    private void initEndcase(SyncCaseStatusDto dto, SyncCaseStatusMqDto mqDto) {
        // 结案信息
        EndCaseInfoDto endCaseInfoDto = new EndCaseInfoDto();
        endCaseInfoDto.setAdvancedPament("2"); // 暂时写死

        WholeCaseBaseEntity wholeCase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(dto.getReportNo(), dto.getCaseTimes());
        if (Objects.nonNull(wholeCase)) {
            endCaseInfoDto.setIndemnityConclusion(wholeCase.getIndemnityConclusion());
            endCaseInfoDto.setIndemnityModel(wholeCase.getIndemnityModel());

            // 匹配微保的paymentType
            if (ConfigConstValues.INDEMNITYCONCLUSION_PAY.equals(endCaseInfoDto.getIndemnityConclusion())) {
                if (ConfigConstValues.INDEMNITYMODEL_PROTOCOL.equals(endCaseInfoDto.getIndemnityModel())) {
                    endCaseInfoDto.setPaymentType("4");
                } else if (ConfigConstValues.INDEMNITYMODEL_ACCOMMODATION.equals(endCaseInfoDto.getIndemnityModel())) {
                    endCaseInfoDto.setPaymentType("5");
                } else {
                    endCaseInfoDto.setPaymentType("2");
                }
            } else if (ConfigConstValues.INDEMNITYCONCLUSION_REFUSE.equals(endCaseInfoDto.getIndemnityConclusion())) {
                endCaseInfoDto.setPaymentType("3");
            } else {
                endCaseInfoDto.setPaymentType("9");
            }
        }

        List<VerifyDTO> verifyList = verifyMapper.getVerifyList(dto.getReportNo(), dto.getCaseTimes());
        if (CollectionUtils.isNotEmpty(verifyList)) {
            endCaseInfoDto.setAdjustmentCompletionTime(verifyList.get(0).getSettleDate());
        }
        List<PolicyPayDTO> policyPayList = mqDto.getReportInfoData().getPolicyPayList();
        if (CollectionUtils.isNotEmpty(policyPayList)) {
            BigDecimal payAmount = BigDecimal.ZERO;
            List<EndCaseDutyPayDto> dutyPayList = Lists.newArrayList();
            for (PolicyPayDTO policyPayDTO : policyPayList) {
                if (Objects.nonNull(policyPayDTO.getPolicySumPay())) {
                    payAmount = payAmount.add(policyPayDTO.getPolicyPay());
                }

                for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
                    for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                        EndCaseDutyPayDto endCaseDutyPayDto = new EndCaseDutyPayDto();
                        endCaseDutyPayDto.setDutyCode(dutyPayDTO.getDutyCode());
                        endCaseDutyPayDto.setDutyName(dutyPayDTO.getDutyName());
                        endCaseDutyPayDto.setDutyDesc(dutyPayDTO.getDutyDesc());
                        endCaseDutyPayDto.setDutyPayAmount(dutyPayDTO.getSettleAmount());
                        dutyPayList.add(endCaseDutyPayDto);
                    }
                }
            }
            endCaseInfoDto.setPayAmount(payAmount);
            endCaseInfoDto.setDutyPayList(dutyPayList);
        }
        mqDto.setEndCaseInfo(endCaseInfoDto);

        // 领款信息
        mqDto.setPaymentList(mqDto.getReportInfoData().getPaymentList());

        // 当零结和拒赔时，修改保单赔付信息
        if (ConfigConstValues.INDEMNITYCONCLUSION_ZERO_CLOSED.equals(dto.getIndemnityConclusion()) || ConfigConstValues.INDEMNITYCONCLUSION_REFUSE.equals(dto.getIndemnityConclusion())) {
            List<PolicyVO> policyVOList = mqDto.getReportInfoData().getPolicyInfoDTOList();
            if (CollectionUtils.isNotEmpty(policyVOList)) {
                List<PolicyPayDTO> list = Lists.newArrayList();
                for (PolicyVO policyVO : policyVOList) {
                    PolicyPayDTO policyPayDTO = new PolicyPayDTO();
                    policyPayDTO.setPolicyNo(policyVO.getPolicyNo());
                    policyPayDTO.setIndemnityConclusion(dto.getIndemnityConclusion());
                    list.add(policyPayDTO);
                }
                mqDto.getReportInfoData().setPolicyPayList(list);
            }
        }

        this.initRegister(dto,mqDto);
    }
}
