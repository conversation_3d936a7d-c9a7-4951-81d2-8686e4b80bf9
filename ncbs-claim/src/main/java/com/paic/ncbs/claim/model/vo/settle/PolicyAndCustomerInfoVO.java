package com.paic.ncbs.claim.model.vo.settle;


import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

public class PolicyAndCustomerInfoVO {
	private String policyNo;
	private String policyCerNo;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date insuranceBeginTime;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date insuranceEndTime;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date insuredEndTime;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date insuredBeginTime;
	private String clientNo;
	private String clientName;
	private String clientPersonnelType;
	private String clientIdNo;
	private String departmentCode;
	private String departmentName;
	private String insuranceApplicantName;
	private String applicantPhone;
	private String applicantPersonnelType;
	private String applicantIdNo;
	private String applicantEmail;
	private String policyStatusName ;
	private String productCode;
	private String productName;
	private String productPackageType;
	private String riskGroupName;
	private BigDecimal totalInsuredAmount;

	public BigDecimal getTotalInsuredAmount() {
		return totalInsuredAmount;
	}

	public void setTotalInsuredAmount(BigDecimal totalInsuredAmount) {
		this.totalInsuredAmount = totalInsuredAmount;
	}

	public String getPolicyCerNo() {
		return policyCerNo;
	}

	public void setPolicyCerNo(String policyCerNo) {
		this.policyCerNo = policyCerNo;
	}

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}


	public String getInsuranceApplicantName() {
		return insuranceApplicantName;
	}

	public void setInsuranceApplicantName(String insuranceApplicantName) {
		this.insuranceApplicantName = insuranceApplicantName;
	}

	public String getClientNo() {
		return clientNo;
	}

	public void setClientNo(String clientNo) {
		this.clientNo = clientNo;
	}

	public String getClientName() {
		return clientName;
	}

	public void setClientName(String clientName) {
		this.clientName = clientName;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	public String getApplicantPhone() {
		return applicantPhone;
	}

	public void setApplicantPhone(String applicantPhone) {
		this.applicantPhone = applicantPhone;
	}

	public String getApplicantPersonnelType() {
		return applicantPersonnelType;
	}

	public void setApplicantPersonnelType(String applicantPersonnelType) {
		this.applicantPersonnelType = applicantPersonnelType;
	}

	public String getApplicantIdNo() {
		return applicantIdNo;
	}

	public void setApplicantIdNo(String applicantIdNo) {
		this.applicantIdNo = applicantIdNo;
	}

	public String getApplicantEmail() {
		return applicantEmail;
	}

	public void setApplicantEmail(String applicantEmail) {
		this.applicantEmail = applicantEmail;
	}

    public String getPolicyStatusName() {
        return policyStatusName;
    }

    public void setPolicyStatusName(String policyStatusName) {
        this.policyStatusName = policyStatusName;
    }

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Date getInsuranceBeginTime() {
		return insuranceBeginTime;
	}

	public void setInsuranceBeginTime(Date insuranceBeginTime) {
		this.insuranceBeginTime = insuranceBeginTime;
	}

	public Date getInsuranceEndTime() {
		return insuranceEndTime;
	}

	public void setInsuranceEndTime(Date insuranceEndTime) {
		this.insuranceEndTime = insuranceEndTime;
	}

	public Date getInsuredEndTime() {
		return insuredEndTime;
	}

	public void setInsuredEndTime(Date insuredEndTime) {
		this.insuredEndTime = insuredEndTime;
	}

	public Date getInsuredBeginTime() {
		return insuredBeginTime;
	}

	public void setInsuredBeginTime(Date insuredBeginTime) {
		this.insuredBeginTime = insuredBeginTime;
	}

	public String getProductPackageType() {
		return productPackageType;
	}

	public void setProductPackageType(String productPackageType) {
		this.productPackageType = productPackageType;
	}

	public String getRiskGroupName() {
		return riskGroupName;
	}

	public void setRiskGroupName(String riskGroupName) {
		this.riskGroupName = riskGroupName;
	}

	public String getClientPersonnelType() {
		return clientPersonnelType;
	}

	public void setClientPersonnelType(String clientPersonnelType) {
		this.clientPersonnelType = clientPersonnelType;
	}

	public String getClientIdNo() {
		return clientIdNo;
	}

	public void setClientIdNo(String clientIdNo) {
		this.clientIdNo = clientIdNo;
	}
}
