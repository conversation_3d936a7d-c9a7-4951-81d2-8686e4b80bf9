package com.paic.ncbs.claim.service.lawsuit.impl;

import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitAudit;
import com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitCase;
import com.paic.ncbs.claim.dao.mapper.lawsuit.ClmsLawsuitAuditMapper;
import com.paic.ncbs.claim.dao.mapper.lawsuit.ClmsLawsuitCaseMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.lawsuit.ClmsLawsuitCaseService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 诉讼案件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Service
public class ClmsLawsuitCaseServiceImpl implements ClmsLawsuitCaseService {

    @Autowired
    private ClmsLawsuitCaseMapper clmsLawsuitCaseMapper;

    @Autowired
    private ClmsLawsuitAuditMapper clmsLawsuitAuditMapper;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private TaskPoolService taskPoolService;

    @Autowired
    private UserInfoService userInfoService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveclmsLawsuitCase(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO) {
        if (clmsLawsuitAuditCaseVO == null){
            throw new GlobalBusinessException("诉讼登记参数为空，请检查！");
        }
        String reportNo = clmsLawsuitAuditCaseVO.getReportNo();
        Integer caseTimes = clmsLawsuitAuditCaseVO.getCaseTimes();
        String approver = clmsLawsuitAuditCaseVO.getApprover();
        String userId = WebServletContext.getUserId();
        String departmentCode = WebServletContext.getDepartmentCode();
        String uuid = UuidUtil.getUUID();
        String lawsuitNo = null;

        if (approver.equals(userId)){
            throw new GlobalBusinessException("不能把本人提交的审批案件派工给本人");
        }

        ClmsLawsuitCase lawsuitCase = clmsLawsuitCaseMapper.getClmsLawsuitCase(reportNo, caseTimes);
        if (lawsuitCase != null) {
            //如果已存在，序号加1
            String currentSerial = lawsuitCase.getLawsuitNo().substring(lawsuitCase.getLawsuitNo().length() - 2);
            int nextSerial = Integer.parseInt(currentSerial) + 1;
            lawsuitNo = "SS" + reportNo + String.format("%02d", nextSerial);
        } else {
            //如果不存在，从01开始
            lawsuitNo = "SS" + reportNo + "01";
        }

        //保存诉讼案件信息
        ClmsLawsuitCase clmsLawsuitCase = new ClmsLawsuitCase();
        BeanUtils.copyProperties(clmsLawsuitAuditCaseVO, clmsLawsuitCase);
        clmsLawsuitCase.setId(uuid);
        clmsLawsuitCase.setLawsuitNo(lawsuitNo);
        clmsLawsuitCase.setStatus(BaseConstant.STRING_1);
        clmsLawsuitCase.setApproverName(userInfoService.getUserNameById(approver));
        clmsLawsuitCase.setCreatedBy(userId);
        clmsLawsuitCase.setUpdatedBy(userId);
        clmsLawsuitCaseMapper.saveClmsLawsuitCase(clmsLawsuitCase);

        //保存案件诉讼批复信息
        ClmsLawsuitAudit clmsLawsuitAudit = new ClmsLawsuitAudit();
        BeanUtils.copyProperties(clmsLawsuitAuditCaseVO, clmsLawsuitAudit);
        clmsLawsuitAudit.setLawsuitCaseId(uuid);
        clmsLawsuitAudit.setLawsuitNo(lawsuitNo);
        clmsLawsuitAudit.setStatus(BaseConstant.STRING_1);
        clmsLawsuitAudit.setSubmitter(userId);
        clmsLawsuitAudit.setSubmitterName(userInfoService.getUserNameById(userId));
        clmsLawsuitAudit.setAuditCode(approver);
        clmsLawsuitAudit.setAuditName(userInfoService.getUserNameById(approver));
        clmsLawsuitAudit.setCreatedBy(userId);
        clmsLawsuitAudit.setUpdatedBy(userId);
        clmsLawsuitAuditMapper.saveClmsLawsuitAudit(clmsLawsuitAudit);

        // 数据提交，开始创建任务
        bpmService.startProcessOc(reportNo, caseTimes, BpmConstants.OC_LITIGATION_APPROVAL, uuid, approver, departmentCode);
        // 记录操作记录
        operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_LITIGATION_APPROVAL, "发起", null);
        LogUtil.audit("#诉讼登记开始提交至工作流#:reportNo=%s,caseTimes=%s", reportNo, caseTimes);

    }

    @Override
    public ClmsLawsuitAuditCaseVO getLawsuitCaseById(String id) {
        return clmsLawsuitCaseMapper.getLawsuitCaseById(id);
    }

    @Override
    public List<Object> getALawyerForYourCase(String reportNo, String caseTimes) {
        return clmsLawsuitCaseMapper.getALawyerForYourCase(reportNo, caseTimes);
    }

    @Override
    public List<ClmsLawsuitAuditCaseVO> getClmsLawsuit(String reportNo, Integer caseTimes) {
        return clmsLawsuitCaseMapper.getClmsLawsuit(reportNo, caseTimes);
    }

    @Override
    public Integer getClmsLawsuitCount(String reportNo, Integer caseTimes) {
        return clmsLawsuitCaseMapper.getClmsLawsuitCount(reportNo, caseTimes);
    }

    @Override
    public List<Object> getDepartmentList() {
        String departmentCode = BaseConstant.STRING_1;
        String taskDefinitionBpmKey = BpmConstants.OC_LITIGATION_WAS_CONCLUDED;
        String currentUserId = WebServletContext.getUserId();
        ArrayList<DepartmentInfo> departmentInfos = new ArrayList<>();
        try {
            List<UserInfoDTO> userInfoDTOS = taskPoolService.searchTaskDealUser(departmentCode, taskDefinitionBpmKey);
            if (ListUtils.isNotEmpty(userInfoDTOS)){
                userInfoDTOS.forEach(userInfoDTO -> {
                    // 如果是当前登录用户，则跳过
                    if (currentUserId.equals(userInfoDTO.getUserCode())) {
                        return;
                    }
                    DepartmentInfo deptInfo = new DepartmentInfo();
                    deptInfo.setCode(userInfoDTO.getComCode());
                    deptInfo.setName(userInfoDTO.getComName());
                    if (!departmentInfos.contains(deptInfo)) {
                        departmentInfos.add(deptInfo);
                    }
                });
            }
        } catch (NcbsException e) {
            throw new RuntimeException(e);
        }
        return Collections.singletonList(departmentInfos);
    }

    private static class DepartmentInfo {
        private String code;
        private String name;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null || getClass() != obj.getClass()) {
                return false;
            }
            DepartmentInfo that = (DepartmentInfo) obj;
            return code != null ? code.equals(that.code) : that.code == null;
        }

        @Override
        public int hashCode() {
            return code != null ? code.hashCode() : 0;
        }
    }



}
