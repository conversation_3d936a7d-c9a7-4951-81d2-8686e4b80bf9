package com.paic.ncbs.claim.dao.entity.lawsuit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 诉讼案件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Getter
@Setter
@TableName("clms_lawsuit_case")
public class ClmsLawsuitCase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 保单号
     */
    @TableField("policy_no")
    private String policyNo;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 诉讼案编号
     */
    @TableField("lawsuit_no")
    private String lawsuitNo;

    /**
     * 被保险人
     */
    @TableField("insured_name")
    private String insuredName;

    /**
     * 案件状态
     */
    @TableField("case_status")
    private String caseStatus;

    /**
     * 庭审类型 1-一审,2-二审,3-再审
     */
    @TableField("hearing_type")
    private String hearingType;

    /**
     * 诉讼案由 1-人身保险合同纠纷,2-财产保险合同纠纷,3-责任保险合同纠纷,4-健康险纠纷,5-提供劳务受害责任纠纷,6-其他
     */
    @TableField("lawsuit_reason")
    private String lawsuitReason;

    /**
     * 原告人
     */
    @TableField("plaintiff")
    private String plaintiff;

    /**
     * 原告人电话
     */
    @TableField("p_phone")
    private String pPhone;

    /**
     * 被告人
     */
    @TableField("defendant")
    private String defendant;

    /**
     * 被告人电话
     */
    @TableField("d_phone")
    private String dPhone;

    /**
     * 承办律师
     */
    @TableField("lawyer")
    private String lawyer;

    /**
     * 承办律师电话
     */
    @TableField("l_phone")
    private String lPhone;

    /**
     * 受理法院
     */
    @TableField("court")
    private String court;

    /**
     * 案号
     */
    @TableField("court_case_no")
    private String courtCaseNo;

    /**
     * 开庭日期
     */
    @TableField("hearing_date")
    private LocalDate hearingDate;

    /**
     * 收到诉讼材料日期
     */
    @TableField("material_date")
    private LocalDate materialDate;

    /**
     * 接收判决书日期
     */
    @TableField("judgment_date")
    private LocalDate judgmentDate;

    /**
     * 诉讼/仲裁请求金额
     */
    @TableField("apply_amount")
    private BigDecimal applyAmount;

    /**
     * 更新诉讼/仲裁请求金额
     */
    @TableField("update_amount")
    private BigDecimal updateAmount;

    /**
     * 最终调解/判决金额
     */
    @TableField("final_amount")
    private BigDecimal finalAmount;

    /**
     * 对方索赔法律费用
     */
    @TableField("opponent_fee")
    private BigDecimal opponentFee;

    /**
     * 我方赔偿法律费用
     */
    @TableField("our_fee")
    private BigDecimal ourFee;

    /**
     * 庭审起始日期
     */
    @TableField("start_date")
    private LocalDate startDate;

    /**
     * 庭审终止日期
     */
    @TableField("end_date")
    private LocalDate endDate;

    /**
     * 书记员
     */
    @TableField("court_clerk")
    private String courtClerk;

    /**
     * 庭审法官
     */
    @TableField("judge_name")
    private String judgeName;

    /**
     * 联系电话
     */
    @TableField("judge_phone")
    private String judgePhone;

    /**
     * 庭审结果
     */
    @TableField("hearing_result")
    private String hearingResult;

    /**
     * 是否庭审前和解(0-否,1-是)
     */
    @TableField("is_pre_settle")
    private String isPreSettle;

    /**
     * 是否上诉/是否申请再审(0-否,1-是)
     */
    @TableField("is_appeal")
    private String isAppeal;

    /**
     * 是否已经结束(0-否,1-是)
     */
    @TableField("is_finished")
    private String isFinished;

    /**
     * 是否胜诉(0-败诉,1-胜诉)
     */
    @TableField("is_won")
    private String isWon;

    /**
     * 诉讼减损金额
     */
    @TableField("loss_reduction")
    private BigDecimal lossReduction;

    /**
     * 胜诉/败诉原因
     */
    @TableField("win_lose_reason")
    private String winLoseReason;

    /**
     * 批复人
     */
    @TableField("approver")
    private String approver;

    /**
     * 批复人名称
     */
    @TableField("approver_name")
    private String approverName;

    /**
     * 状态 1-待批复,2-已批复待办结,3-办结
     */
    @TableField("status")
    private String status;

    /**
     * 创建人员
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;
}
