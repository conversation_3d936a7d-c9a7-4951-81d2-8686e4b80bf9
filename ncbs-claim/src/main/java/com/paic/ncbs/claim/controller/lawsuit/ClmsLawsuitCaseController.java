package com.paic.ncbs.claim.controller.lawsuit;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO;
import com.paic.ncbs.claim.service.lawsuit.ClmsLawsuitCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 诉讼案件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Api(tags = "诉讼案件信息")
@RestController
@RequestMapping("/lawsuit/clmsLawsuitCaseAction")
public class ClmsLawsuitCaseController extends BaseController {

    @Autowired
    private ClmsLawsuitCaseService clmsLawsuitCaseService;

    @ApiOperation("诉讼-诉讼登记")
    @PostMapping(value = "/saveclmsLawsuitCase")
    public ResponseResult<Object> saveclmsLawsuitCase(@RequestBody ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO) throws Exception {
        clmsLawsuitCaseService.saveclmsLawsuitCase(clmsLawsuitAuditCaseVO);
        return ResponseResult.success();
    }

    @ApiOperation("诉讼-根据id获取案件详情")
    @GetMapping(value = "/getLawsuitCaseById")
    public ResponseResult<ClmsLawsuitAuditCaseVO> getLawsuitCaseById(String id) {
        return ResponseResult.success(clmsLawsuitCaseService.getLawsuitCaseById(id));
    }

    @ApiOperation("诉讼-获取该案件下审批通过委托律师")
    @GetMapping(value = "/getALawyerForYourCase")
    public ResponseResult<List<Object>> getALawyerForYourCase(String reportNo, String caseTimes) {
        return ResponseResult.success(clmsLawsuitCaseService.getALawyerForYourCase(reportNo, caseTimes));
    }

    @ApiOperation("诉讼-查询案件下所有诉讼信息")
    @GetMapping(value = "/getClmsLawsuit")
    public ResponseResult<List<ClmsLawsuitAuditCaseVO>> getClmsLawsuit(String reportNo, Integer caseTimes) {
        return ResponseResult.success(clmsLawsuitCaseService.getClmsLawsuit(reportNo, caseTimes));
    }

    @ApiOperation("诉讼-查询案件下所有诉讼信息数量")
    @GetMapping(value = "/getClmsLawsuitCount")
    public ResponseResult<Object> getClmsLawsuitCount(String reportNo, Integer caseTimes) {
        return ResponseResult.success(clmsLawsuitCaseService.getClmsLawsuitCount(reportNo, caseTimes));
    }

    @ApiOperation("诉讼-获取管理岗和核赔岗用户机构")
    @GetMapping(value = "/getDepartmentList")
    public ResponseResult<Object> getDepartmentList() {
        return ResponseResult.success(clmsLawsuitCaseService.getDepartmentList());
    }



}
