package com.paic.ncbs.claim.dao.mapper.follow;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.follow.ClmsUserFollowCase;

import java.util.List;

/**
 * <p>
 * 关注表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
public interface ClmsUserFollowCaseMapper extends BaseMapper<ClmsUserFollowCase> {

    void saveClmsUserFollowCase(ClmsUserFollowCase clmsUserFollowCase);

    void delClmsUserFollowCase(ClmsUserFollowCase clmsUserFollowCase);

    List<ClmsUserFollowCase> getClmsUserFollowCase(String userCode);

    ClmsUserFollowCase getCaseFollowStatus(String reportNo, Integer caseTimes, String userCode);

}
