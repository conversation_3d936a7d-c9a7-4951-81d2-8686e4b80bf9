package com.paic.ncbs.claim.controller.lawsuit;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO;
import com.paic.ncbs.claim.service.lawsuit.ClmsLawsuitAuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 诉讼批复表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Api(tags = "诉讼批复信息")
@RestController
@RequestMapping("/lawsuit/clmsLawsuitAuditAction")
public class ClmsLawsuitAuditController extends BaseController {

    @Autowired
    private ClmsLawsuitAuditService clmsLawsuitAuditService;

    @ApiOperation("诉讼-诉讼批复")
    @PostMapping(value = "/litigationApproval")
    public ResponseResult<Object> litigationApproval(@RequestBody ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO) throws Exception {
        clmsLawsuitAuditService.litigationApproval(clmsLawsuitAuditCaseVO);
        return ResponseResult.success();
    }

    @ApiOperation("诉讼-诉讼办结")
    @PostMapping(value = "/lawsuitWasConcluded")
    public ResponseResult<Object> lawsuitWasConcluded(@RequestBody ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO) throws Exception {
        clmsLawsuitAuditService.lawsuitWasConcluded(clmsLawsuitAuditCaseVO);
        return ResponseResult.success();
    }

    @ApiOperation("诉讼-查询待诉讼批复信息")
    @PostMapping(value = "/getClmsLawsuitCase")
    public ResponseResult<List<ClmsLawsuitAuditCaseVO>> getClmsLawsuitCase(@RequestBody ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO) {
        return ResponseResult.success(
        clmsLawsuitAuditService.getClmsLawsuitCase(clmsLawsuitAuditCaseVO));
    }

    @ApiOperation("诉讼-查询待诉讼办结信息")
    @PostMapping(value = "/getClmsLawsuitCaseConcluded")
    public ResponseResult<List<ClmsLawsuitAuditCaseVO>> getClmsLawsuitCaseConcluded(@RequestBody ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO) {
        return ResponseResult.success(
                clmsLawsuitAuditService.getClmsLawsuitCaseConcluded(clmsLawsuitAuditCaseVO));
    }

}
