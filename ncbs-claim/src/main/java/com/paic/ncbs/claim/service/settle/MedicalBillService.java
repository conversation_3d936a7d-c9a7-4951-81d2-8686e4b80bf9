package com.paic.ncbs.claim.service.settle;

import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillSpecialDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalDTO;
import com.paic.ncbs.claim.model.dto.settle.SyncBillParamDTO;
import com.paic.ncbs.claim.model.vo.duty.DutySurveyVO;
import com.paic.ncbs.claim.model.vo.settle.CompareBillNoResultVO;
import com.paic.ncbs.claim.model.vo.settle.IdAhcsBillInfoListVO;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoPageVO;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface MedicalBillService {

	 CompareBillNoResultVO validAndAddMedicalBill(MedicalBillInfoVO medicalBillInfoVO, String userUM);

     @Transactional
     void clearBillAmonutForHuge(String reportNo, Integer caseTimes, String userId, String lossObjectNo) throws GlobalBusinessException;

     MedicalBillInfoPageVO getBillInfoByPage(MedicalBillInfoDTO medicalBillInfoDTO) throws GlobalBusinessException;

	 void removeMedicalBill(IdAhcsBillInfoListVO idAhcsBillInfoListVO, String userUM) throws Exception;

	 MedicalBillInfoDTO getMedicalBillAllAmount(String reportNo, Integer caseTimes, String lossObjectNo);

	 CompareBillNoResultVO getSameBillNoReportNo(MedicalBillInfoVO medicalBillInfoVO) throws GlobalBusinessException;

	 BigDecimal getDefaultInsuranceTotalPayment(MedicalBillInfoVO medicalBillInfoVO) throws GlobalBusinessException;

	 String checkAllBillRequired(String reportNo, Integer caseTimes);

     @Transactional
     CompareBillNoResultVO modifyMedicalBill(MedicalBillInfoVO medicalBillInfoVO, String userUM) throws Exception;

     MedicalBillSpecialDTO getBillSpecial(String idAhcsBillInfo);

	 MedicalBillInfoVO getMedicalBill(String idAhcsBillInfo);

	 BigDecimal getAllLossAmount(String reportNo, Integer caseTimes);


    List<MedicalBillInfoVO> getMedicalBillInfoForPrint(String reportNo, Integer caseTimes);

	/**
	 * 诊断信息查询
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	List<PersonDiagnoseDTO> getPersonDiagnoseList(String reportNo, Integer caseTimes);

	List<MedicalBillInfoVO> checkBills(String reportNo, Integer caseTimes);

	DutySurveyVO addOuterMedicalBill(MedicalDTO medicalDTO) throws Exception;

	void addTpaReceipt(MedicalDTO medicalDTO);

	void syncBillInfo(SyncBillParamDTO syncBillParamDTO);

	/**
	 * 根据保单号查询保单历史报案发票信息
	 * @return
	 */
	BigDecimal	getPolicyHistoryBillInfo(String policyNo);

	/**
	 * @param reportNo
	 * @param caseTimes
	 * @return
	 */
	Map<String,CompareBillNoResultVO> checkAllBillDuplicate(String reportNo, int caseTimes);

	String getNewSplit(String s);

	/**
	 * 同步TPA医疗单据不涉及任务流流转
	 *
	 * @param medicalDTO 包含医疗信息的DTO对象
	 * @throws Exception 如果处理过程中出现错误，则抛出异常
	 */
	void addOuterMedicalBillNoTaskInfo(MedicalDTO medicalDTO) throws Exception;
}
