package com.paic.ncbs.claim.service.other;

import com.paic.ncbs.claim.common.enums.MailTypeEnum;
import com.paic.ncbs.claim.model.dto.message.MailSendDTO;
import com.paic.ncbs.message.model.dto.SmsResult;

import java.util.Map;

public interface MailSendService {

    /**
     * 发送大案邮件
     * @param reportNo
     * @param receiver
     * @return
     */
    void sendMajorCaseMail(String reportNo, String receiver,String cc);

    /**
     * 发送普通案件邮件
     * @param reportNo
     * @param receiver
     */
    void sendCaseMail(String reportNo, String receiver);
    /**
     * 发送再保邮件
     */
    SmsResult sendReinsMail(MailSendDTO mailSendDTO);
    /**
     * 根据模板参数生成内容
     */
    String getContextAndTitle(MailTypeEnum mailTypeEnum, Map<String,String> params);
}
