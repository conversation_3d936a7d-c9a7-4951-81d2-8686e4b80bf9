package com.paic.ncbs.claim.model.vo.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.vo.duty.NoPeopleHurtVO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(description = "报案信息")
public class SaveReportInfoVO {

    private String reportFlowFlag;

    @ApiModelProperty(value = "分单号")
    private List<String> superPolicyNos;

    @ApiModelProperty(value = "合作伙伴编码")
    private String partnerCode;

    @ApiModelProperty(value = "")
    private String cid;

    @ApiModelProperty(value = "保单号列表")
    private List<String> policyNos;

    @ApiModelProperty(value = "被保人id")
    private List<String> idPlyRiskPersons;

    private String outBizNo;

    private String outReportNo;

    @ApiModelProperty(value = "险种代码")
    private String planCode;

    @ApiModelProperty(value = "责任编码")
    private String dutyCode;

    @ApiModelProperty(value = "责任明细编码")
    private String dutyDetailCode;
    /**
     *报案来源(参考clm_common_parameter.collection_code = REPORT_FROM_TYPE)
     */
    @ApiModelProperty(value = "报案来源")
    private String reportMode;

    @ApiModelProperty(value = "报案人电话")
    private String reportPersonConPhone;

    /**
     * 出险者现状 对应枚举 InsuredApplyStatusEnum
     */
    @ApiModelProperty(value = "出险者现状")
    private String insuredApplyStatus;

    /**
     * 出险类型 对应枚举 InsuredApplyTypeEnum
     */
    @ApiModelProperty(value = "出险类型")
    private String insuredApplyType;

    // 去除
    @ApiModelProperty(value = "治疗类型（THE_0301：门诊、THE_0302：住院）")
    private String[] therapyType;

    /**
     * 事故类型 对应枚举 AccidentTypeEnum
     */
    @ApiModelProperty(value = "事故类型（疾病、意外）")
    private String accidentType;

    @ApiModelProperty(value = "损伤部位类型")
    private String injuryReasonCode;

    // String visitReason
    @ApiModelProperty(value = "就诊原因")
    private String visitReason;

    @ApiModelProperty(value = "客户号")
    private String clientNo;

    @ApiModelProperty(value = "身份证号")
    private String certificateNo;

    @ApiModelProperty(value = "客户名")
    private String clientName;

    @ApiModelProperty(value = "出生日期")
    private String birthday;

    @ApiModelProperty(value = "证件类型")
    private String certificateType;

    @ApiModelProperty(value = "客户证件类型")
    private String clientCertificateType;

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "赔付次数")
    private String caseTimes;

    @ApiModelProperty(value = "案件类别 1.人伤 2.非人伤")
    private String caseClass;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty(value = "出险时间")
    private Date accidentDate;

    @ApiModelProperty(value = "是否单证齐全（Y/N）")
    private String isSuffice;

    @ApiModelProperty(value = "收单说明")
    private String remark;

    @ApiModelProperty(value = "优先原因")
    private String priorityReason;

    private String reportAcceptUm;

    @ApiModelProperty(value = "联系人信息")
    private List<LinkManEntity> linkManList = new ArrayList<>();

    @ApiModelProperty(value = "机构编码")
    private String departmentCode;

    @ApiModelProperty(value = " 发送短信Y:发送，N:不发送")
    private String sendMessage;

    @ApiModelProperty(value = "是否符合自助（Y/N）")
    private String isSelfHelp;

    @ApiModelProperty(value = "是否大灾（Y-是 N-否）")
    private String isHugeAccident;

    @ApiModelProperty(value = "是否大灾（Y-是 N-否）")
    private String isEasyCase;

    @ApiModelProperty(value = "重灾名称")
    private String hugeAccidentName;

    @ApiModelProperty(value = "重灾类型")
    private String hugeAccidentType;

    @ApiModelProperty(value = "大灾ID")
    private String hugeAccidentId;

    @ApiModelProperty(value = "案件类型")
    private String caseType;

    @ApiModelProperty(value = "费用预估")
    private String costEstimate;
    @ApiModelProperty(value = "出险原因大类")
    private String accidentCauseLevel1;

    @ApiModelProperty(value = "出险原因明细类")
    private String accidentCauseLevel2;
    @ApiModelProperty(value = "新建报案出险原因大类")
    private String accidentCauseLevel3;

    @ApiModelProperty(value = "新建报案出险原因明细类")
    private String accidentCauseLevel4;
    @ApiModelProperty(value = "出险经过")
    private String accidentDetail;

    @ApiModelProperty(value = "出险城市")
    private String accidentCity;

    @ApiModelProperty(value = "出险县")
    private String accidentCounty;

    @ApiModelProperty(value = "出险省份")
    private String accidentProvince;

    @ApiModelProperty(value = "出险区域")
    private String accidentArea;

    @ApiModelProperty(value = "出险国家")
    private String accidentNation;

    @ApiModelProperty(value = "出险区域")
    private String accidentPlace;

    @ApiModelProperty(value = "事故地点(0：境内/1：境外)")
    private String whetherOutSideAccident;

    @ApiModelProperty(value = "微信openId")
    private String openId;

    @ApiModelProperty(value = "是否重大灾难")
    private String isSpecialReport;

    @ApiModelProperty(value = "")
    private String isPolicyReport;

    @ApiModelProperty(value = "")
    private String isTransReport;

    @ApiModelProperty(value = "是否异常")
    private String isAbnormal;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @ApiModelProperty(value = "报案时间")
    private Date reportDate;

    @ApiModelProperty(value = "联系人电话")
    private String linkManTelephone;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "（非人伤）行李延误")
    private ReportAccidentBaggageVO reportAccidentBaggage;

    @ApiModelProperty(value = "（非人伤）考试不通过")
    private ReportAccidentExamVO reportAccidentExam;

    @ApiModelProperty(value = "（非人伤）航班延误")
    private ReportAccidentFlightVO reportAccidentFlight;

    @ApiModelProperty(value = "非人伤）财产损失")
    private ReportAccidentLossVO reportAccidentLoss;

    @ApiModelProperty(value = "（非人伤）其他非人伤")
    private ReportAccidentOtherVO reportAccidentOther;

    @ApiModelProperty(value = "（非人伤）其他交通工具延误")
    private ReportAccidentTrafficVO reportAccidentTraffic;

    @ApiModelProperty(value = "（非人伤）旅行变更")
    private ReportAccidentTravelVO reportAccidentTravel;

    @ApiModelProperty(value = "（非人伤）宠物")
    private ReportAccidentPetVO reportAccidentPet;

    private List<ReportAntFileInfoVO> reportAntFileInfoList;

    private String medicalType;

    private String diseases;

    private List<String> subCaseClass;

    @ApiModelProperty(value = "出险扩展信息")
    private String accidentExtendInfo;
    private String accidentPersonPermanentAddress;
    private String billAccountType;
    private String billAccountNo;
    private List<AntPolicyVO> antPolicys;

    @ApiModelProperty(value = "标的")
    private List<CaseRiskPropertyDTO> riskPropertyList;

    /**
     * 方案号，责任险用，目前责任险案件只能选一个保单，所以方案号也只有一个
     */
    private String riskGroupNo;
    private String riskGroupName;

    @ApiModelProperty("非人伤VO")
    private NoPeopleHurtVO noPeopleHurtVO; // 责任险对应标的，和报案跟踪非人伤对象保持一致，只使用人员伤亡信息ClmsPersonalInjuryDeathInfoDTO
    @ApiModelProperty("损失类型 3-人伤 4-健康 5-财产 6-其他")
    private String lossClass;

    @ApiModelProperty("领款人信息")
    private List<PaymentInfoVO> paymentInfos;
}
