package com.paic.ncbs.claim.dao.entity.common;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 医院别名表实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("clm_hospital_alias_info")
public class HospitalAliasInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建人员
     */
    private String createdBy;

    /**
     * 修改人员
     */
    private String updatedBy;

    /**
     * 删除标记
     */
    @TableLogic
    private Boolean deleteFlag;

    /**
     * 医院表id
     */
    private String hospitalId;

    /**
     * 医院别名
     */
    private String hospitalAlias;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date sysCtime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date sysUtime;
}