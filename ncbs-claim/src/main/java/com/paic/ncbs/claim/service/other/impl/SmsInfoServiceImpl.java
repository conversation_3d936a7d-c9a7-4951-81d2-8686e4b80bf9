package com.paic.ncbs.claim.service.other.impl;

import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.enums.SmsTypeEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity;
import com.paic.ncbs.claim.dao.entity.other.TextTemplateEntity;
import com.paic.ncbs.claim.dao.entity.report.LinkManEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.casezero.CaseZeroCancelMapper;
import com.paic.ncbs.claim.dao.mapper.doc.DocumentTypeMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.ClaimRejectionApprovalRecordEntityMapper;
import com.paic.ncbs.claim.dao.mapper.other.SmsInfoMapper;
import com.paic.ncbs.claim.dao.mapper.other.TextTemplateMapper;
import com.paic.ncbs.claim.dao.mapper.report.LinkManMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.doc.SupplementsMaterialDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO;
import com.paic.ncbs.claim.model.dto.message.SmsInfoDTO;
import com.paic.ncbs.claim.model.dto.other.AccidentReasonDTO;
import com.paic.ncbs.claim.model.dto.other.AccidentReasonTypeInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO;
import com.paic.ncbs.claim.model.vo.record.SmsRecordVO;
import com.paic.ncbs.claim.model.vo.settle.PolicyAndCustomerInfoVO;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.other.SmsInfoService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.supplements.SupplementsMaterialService;
import com.paic.ncbs.message.compent.SmsService;
import com.paic.ncbs.message.model.dto.SmsResult;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class SmsInfoServiceImpl implements SmsInfoService {
    @Autowired
    private SmsService smsService;
    @Autowired
    private SmsInfoMapper smsInfoMapper;
    @Autowired
    private TextTemplateMapper textTemplateMapper;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private SupplementsMaterialService supplementsMaterialService;
    @Autowired
    private DocumentTypeMapper documentTypeMapper;
    @Autowired
    private CaseZeroCancelMapper caseZeroCancelMapper;
    @Autowired
    private ClaimRejectionApprovalRecordEntityMapper claimRejectionApprovalRecordEntityMapper ;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private LinkManMapper linkManMapper;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private CommonParameterService commonParameterService;
    @Autowired
    private CommonParameterService commonService;
    @Autowired
    private ReportInfoMapper reportInfoMapper;

    @Value("${switch.sendSms}")
    private Boolean sendSms;

    /**
     * 异步发送 : 有引用先不改这个方法
     * @param smsDTO
     */
    @Override
    @Async("asyncPool")
    @Deprecated
    public void sendSmsByAsync(SmsInfoDTO smsDTO) {
        LogUtil.audit("发送短信");
      //  sendSmsBySync(smsDTO);
    }

    /**
     * 发送业务短信
     * 短信发送：（reportNo + smsTypeEnum + businessId）应该是唯一条件 不可以多次发送 businessId可以为空
     *
     * @param smsTypeEnum
     * @param reportNo
     * @param caseTimes
     * @param businessId  用于关联子任务
     * @param requestId
     */
    @Override
    @Async("asyncPool")
    public void sendBusinessSmsAsync(SmsTypeEnum smsTypeEnum, String reportNo,Integer caseTimes, String businessId, String requestId) {
        if (StringUtils.isNotEmpty(requestId)) {
            MDC.put(BaseConstant.REQUEST_ID, requestId);
        }
        this.sendBusinessSms(smsTypeEnum, reportNo, caseTimes,businessId);
    }

    /**
     * 发送业务短信
     *
     * @param smsTypeEnum
     * @param reportNo
     * @param caseTimes
     * @param businessId
     */
    @Override
    public void sendBusinessSms(SmsTypeEnum smsTypeEnum, String reportNo,Integer caseTimes, String businessId) {
        Boolean flag = checkIsSend(reportNo);
        String reportSubMode = queryReportSubMode(reportNo);
        if(!flag){
            LogUtil.info("该报案号是黑名单产品不发送短信，报案号：{}", reportNo);
            return;
        } else if(ReportConstant.REPORSUBTMODE_INTERNAL_01.equals(reportSubMode)){
            //其他来源（美团、msh等一步结案、柜面报案、批量导入）不发，其他已经处理，本次只有柜面
            LogUtil.info("该报案号的报案来源是柜面不发送短信，报案号：{}", reportNo);
            return ;
        }
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        if(reportInfo== null){
            LogUtil.info("案件{}基本信息不存在！",reportNo);
            return ;
        }
        try {
            LogUtil.info("案件：{}，发送短信：{},开始:",reportNo,smsTypeEnum.getType());
            // 报案来电号码
            String phoneNo = reportInfo.getReporterCallNo();
            // 发送连接，用于和报案号联合去重作用
            String sendLink = StringUtils.isEmpty(businessId)?smsTypeEnum.getType():smsTypeEnum.getType()+":"+businessId;
            Map<String, String> params = getSmsParams(reportNo,caseTimes,smsTypeEnum.getType(),businessId);
            // 发送短信内容
            String content = getSmsContent(smsTypeEnum, reportNo,params);
            LogUtil.info("发送短信内容：{}",content);
            // 先不加重复发送校验了

            SmsInfoDTO smsInfoDTO = new SmsInfoDTO(reportNo, phoneNo, content, sendLink);
            if(com.paic.ncbs.claim.common.util.StringUtils.isNotEmpty(phoneNo) && ReUtil.isMatch("1[0-9]{10}", phoneNo)) {
                this.sendSmsBySync(smsInfoDTO);
            }
            LogUtil.info("案件：{}，发送短信：{},结束！",reportNo,smsTypeEnum.getType());
        } catch (GlobalBusinessException ge){
            // 短信发送失败也不用抛异常，不打印堆信息
            LogUtil.info("发送短信失败:{}",ge.getMessage());
        }catch (Exception e){
            // 短信发送失败也不用抛异常
            LogUtil.error("发送短信失败运行异常：",e);

        }

    }

    @Override
    public void sendSmsBySync(SmsInfoDTO smsDTO) {
        smsInfoMapper.addSmsInfo(smsDTO.init());
        SmsResult result = null;
        try {
           result = this.sendMessage(smsDTO.getMobileNo(), smsDTO.getSmsContent());
        }catch (Exception e){
            LogUtil.error("发送短信失败",e);
            result = new SmsResult();
            result.setFailCode("error");
            result.setFailReason(e.getMessage());
        }
        result = Optional.ofNullable(result).orElse(new SmsResult());
        if( Constants.SMS_SEND_SUCCESS.equals(result.getIsSuccess())){
            smsDTO.setSmsStatus(Constants.SMS_STATUS_SUCCESS);
        }else{
            smsDTO.setSmsStatus(Constants.SMS_STATUS_FAIL);
            smsDTO.setBaseDesc(result.getFailCode()+":"+result.getFailReason());
            if(smsDTO.getBaseDesc().length()>255){
                smsDTO.setBaseDesc(smsDTO.getBaseDesc().substring(0,255));
            }
        }
        smsDTO.setSendSeriesId(result.getSerialNo());
        smsInfoMapper.updateSmsInfo(smsDTO);
        // 放弃补偿: 因为URL 加签后的真正内容我们没有拿到。
        // if (!Objects.equals(result.getFailCode(), "0")) {
        //     AsynchronousCompensationJobDTO jobDTO = getAsynchronousCompensationJobDTO(smsDTO,result);
        //     paymentItemMapper.insertJobInfo(jobDTO);
        // }
    }




    @Override
    public SmsResult querySms(String id) {
        return null;
    }

    @Override
    public SmsResult sendMessage(String mobile, String smsContent) {
        LogUtil.info("短信服务入参：mobile:{},smsContent:{}", mobile,smsContent);
        SmsResult smsResult = smsService.sendTextSms(mobile, smsContent);
        LogUtil.info("短信服务返回值：{}", JSON.toJSON(smsResult));
        return smsResult;
    }

    @Override
    public Boolean checkIsSend(String reportNo) {
        LogUtil.info("发送短信前校验入参：reportNo:{}", reportNo);
        if(sendSms){
            //查询保单信息，productCode、businessSourceCode
            List<PolicyInfoDTO> policyInfoDTOList = policyInfoMapper.getPolicyInfoListByReportNo(reportNo);
            String productCode = policyInfoDTOList.get(0).getProductCode();
            //查询是否为短信黑名单产品
            List<TextTemplateEntity> textTemplateEntities =
                    textTemplateMapper.selectBlackInfo(SmsTypeEnum.BLACK_LIST_PRODUCT.getType(), productCode);
            if (textTemplateEntities.isEmpty()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String queryReportSubMode(String reportNo) {
        LogUtil.info("查询报案来源：reportNo:{}", reportNo);
        String reportSubMode = null;
        if(sendSms){
            ReportInfoEntity reportInfoEntity = reportInfoMapper.getReportInfo(reportNo);
            if(reportInfoEntity != null){
                if(!reportInfoEntity.getReportSubMode().isEmpty()){
                    reportSubMode = reportInfoEntity.getReportSubMode();
                }
            }
        }
        LogUtil.info("查询报案来源：reportNo:{}，报案来源：{}", reportNo, reportSubMode);
        return reportSubMode;
    }

    /**
     * 获取SMS内容
     * ${reportDate}报案时间 ${accidentDate} 出险时间 ${packageName} 方案名称  ${insuredName} 被保险人名字
     * ${supplementsDesc} 补材说明内容 ${finalAuditRemark} 最终审批说明内容
     * <p>
     * String msg1 =
     * "您好，先生/女士，您在${reportDate}申请的${packageName}，${insuredName}的理赔申请，经审核，您提交的材料还需补充：${supplementsDesc}
     * ，请通过微保小程序上传补充材料（路径：打开微保小程序→我的→理赔服务→申请记录→选择理赔记录上传材料），感谢配合。如有疑问可致电三星官方电话4009333000*4咨询。";
     * String msg2 =
     * "您好，先生/女士，再次提醒！您在${reportDate}申请的${packageName}，${insuredName}的理赔申请，需要您提交的材料还需补充：${supplementsDesc}
     * ，请尽快通过微保小程序上传补充材料（路径：打开微保小程序→我的→理赔服务→申请记录→选择理赔记录上传材料），感谢配合。因目前理赔材料不齐全导致审核程序受阻可能影响理赔！如有疑问可致电三星官方电话4009333000*4
     * 咨询。";
     * String msg3 = "您好，先生/女士，您在${reportDate}申请的${packageName}，${insuredName}的理赔申请，${finalAuditRemark}
     * ，如有疑问可通过微保小程序咨询平台在线客服。";
     *
     * @param smsTypeEnum
     * @param reportNo
     * @param params
     * @return
     */
    private String getSmsContent(SmsTypeEnum smsTypeEnum, String reportNo, Map<String, String> params) {
        //报案来源小类
        String reportSubMode = null;
        if (SmsTypeEnum.CUSTOMER_SUPPLEMENTS_FISRT == smsTypeEnum || SmsTypeEnum.CUSTOMER_SUPPLEMENTS_AGAIN == smsTypeEnum ||
                SmsTypeEnum.ZERO_CANCEL_REVIEW == smsTypeEnum || SmsTypeEnum.REJECT_REVIEW == smsTypeEnum ||
                SmsTypeEnum.VERIFY_REVIEW == smsTypeEnum) {
            ReportInfoEntity reportInfoEntity = reportInfoMapper.getReportInfo(reportNo);
            if(reportInfoEntity != null){
                if(!reportInfoEntity.getReportSubMode().isEmpty()){
                    reportSubMode = reportInfoEntity.getReportSubMode();
                }
            }
        }
        List<TextTemplateEntity> textTemplateEntities = null;
        //1、非黑名单 报案来源=微保 发微保模板
        if(ReportConstant.REPORTSUBMODE_ONLINE_02.equals(reportSubMode)){
            textTemplateEntities = textTemplateMapper.selectByTemplateCode(smsTypeEnum.getType(),
                    null);
        } else if (ReportConstant.REPORTSUBMODE_SMALL_PROGRAM_01.equals(reportSubMode)) {
            //2、非黑名单 报案来源=小程序 发小程序标准模板
            TextTemplateEntity textTemplateEntity = new TextTemplateEntity();
            textTemplateEntity.setTemplateCode(smsTypeEnum.getType());
            textTemplateEntities = textTemplateMapper.getStandardTemplate(textTemplateEntity);
            
        } else {
            //3、其他 发兜底模板
            TextTemplateEntity textTemplateEntity = new TextTemplateEntity();
            textTemplateEntity.setTemplateCode(smsTypeEnum.getType());
            textTemplateEntities = textTemplateMapper.getBackStopTemplate(textTemplateEntity);
        }
        if (CollectionUtils.isEmpty(textTemplateEntities)) {
            LogUtil.info("短信模板:{}不存在!", smsTypeEnum.getType());
            throw new GlobalBusinessException(MessageFormat.format("短信模板{0}不存在！", smsTypeEnum.getType()));
        }
        String content = textTemplateEntities.get(0).getTemplateContent();
        if (content == null || content.isEmpty()) {
            LogUtil.info("短信模板:{}为空!报案来源:{}", smsTypeEnum.getType(),reportSubMode);
            throw new GlobalBusinessException(MessageFormat.format("短信模板{0}为空！", smsTypeEnum.getType()));
        }
        StrSubstitutor strSubstitutor = new StrSubstitutor(params);
        return strSubstitutor.replace(content);
    }

    /**
     * 获取短信内容参数
     * @param reportNo
     * @param caseTimes
     * @param smsType
     * @param businessId
     * @return
     */
    private Map<String, String> getSmsParams(String reportNo, Integer caseTimes, String smsType, String businessId) {
        ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
        if (reportInfo == null) {
            LogUtil.info("案件{}基本信息不存在！", reportNo);
            throw new GlobalBusinessException(MessageFormat.format("案件{0}基本信息不存在！", reportNo));
        }

        List<PolicyAndCustomerInfoVO> policyAndCustomerInfoList =
                policyInfoMapper.getPolicyAndCustomerInfoList(reportNo);
        if (CollectionUtils.isEmpty(policyAndCustomerInfoList)) {
            throw new GlobalBusinessException(MessageFormat.format("案件{0}抄单信息不存在！", reportNo));
        }
        PolicyAndCustomerInfoVO policyAndCustomerInfoVO = policyAndCustomerInfoList.get(0);

        Map<String, String> params = new HashMap<>();
        params.put("reportNo", reportNo);
        params.put("caseTimes", String.valueOf(caseTimes));
        params.put("reportDate", DateUtils.getChineseDate(reportInfo.getReportDate()));
        params.put("productName", StringUtils.stripToEmpty(policyAndCustomerInfoVO.getProductName()));
        params.put("packageName", StringUtils.stripToEmpty(policyAndCustomerInfoVO.getRiskGroupName()));
        params.put("insuredName", StringUtils.stripToEmpty(policyAndCustomerInfoVO.getClientName()));

        if (StringUtils.equalsAny(smsType, SmsTypeEnum.CUSTOMER_SUPPLEMENTS_FISRT.getType(),
                SmsTypeEnum.CUSTOMER_SUPPLEMENTS_AGAIN.getType())) {
            String supplementsContentName = null;
            String supplementsDesc = null;
            SupplementsMaterialDTO supplementsMaterialDTO = supplementsMaterialService.selectByPrimaryKey(businessId);
            // 添加补材类型，补材内容
            if (null != supplementsMaterialDTO) {
                supplementsDesc = StringUtils.stripToEmpty(supplementsMaterialDTO.getSupplementsDesc())
                        .replaceAll("[。.]+", "。");
                String supplementsContent = supplementsMaterialDTO.getSupplementsContent();
                String[] contentCodeArr = StringUtils.split(supplementsContent, Constants.SEPARATOR);
                List<String> smallTypeNameList = new ArrayList<>();
                for (String contentCode :
                        Optional.ofNullable(contentCodeArr).orElse(ArrayUtils.EMPTY_STRING_ARRAY)) {
                    String smallName = documentTypeMapper.getSmallNameBySmallCode(contentCode);
                    if(StringUtils.isNotEmpty(smallName)){
                        smallTypeNameList.add(smallName);
                    }
                }
                supplementsContentName = StringUtils.join(smallTypeNameList, Constants.SEPARATOR);
            }

            params.put("supplementsContentName", supplementsContentName);
            params.put("supplementsDesc", supplementsDesc);
        } else if (StringUtils.equalsAny(smsType, SmsTypeEnum.ZERO_CANCEL_REVIEW.getType())) {
            //  获取零批核撤信息
            String finalAuditRemark = null;
            List<CaseZeroCancelDTO> zeroCancelInfoList = caseZeroCancelMapper.getZeroCancelInfoList(reportNo,
                    caseTimes);
            if (CollectionUtils.isNotEmpty(zeroCancelInfoList)) {
                CaseZeroCancelDTO caseZeroCancelDTO = zeroCancelInfoList.get(0);
                finalAuditRemark =  StringUtils.stripToEmpty(caseZeroCancelDTO.getVerifyRemark())
                        .replaceAll("[。.]+", "。");
            }
            params.put("finalAuditRemark", finalAuditRemark);

        } else if (StringUtils.equalsAny(smsType, SmsTypeEnum.REJECT_REVIEW.getType())) {
            // 获取拒批信息
            String finalAuditRemark = null;
            ClaimRejectionApprovalRecordEntity claimRejectionApprovalRecordEntity = claimRejectionApprovalRecordEntityMapper.selectAgreedRecord(reportNo, caseTimes);
            if (null != claimRejectionApprovalRecordEntity) {
                finalAuditRemark = StringUtils.stripToEmpty(claimRejectionApprovalRecordEntity.getAuditRemark())
                        .replaceAll("[。.]+", "。");
            }
            params.put("finalAuditRemark", finalAuditRemark);
        } else if (StringUtils.equalsAny(smsType, SmsTypeEnum.CREATE_REPORT.getType())) {
            params.put("insuranceApplicantName", StringUtils.stripToEmpty(policyAndCustomerInfoVO.getInsuranceApplicantName()));
            params.put("clientPersonnelType", StringUtils.stripToEmpty(commonParameterService.getValueChineseName(policyAndCustomerInfoVO.getClientPersonnelType(),"CERTIFICATE_TYPE")));
            params.put("clientIdNo", StringUtils.stripToEmpty(policyAndCustomerInfoVO.getClientIdNo()));
            //联系人信息
            List<LinkManEntity> linkManEntityList = linkManMapper.getLinkMans(reportNo,caseTimes);
            if (CollectionUtils.isEmpty(linkManEntityList)) {
                throw new GlobalBusinessException(MessageFormat.format("案件{0}联系人信息不存在！", reportNo));
            }
            params.put("linkManName", StringUtils.stripToEmpty(linkManEntityList.get(0).getLinkManName()));
            params.put("linkManTelephone", StringUtils.stripToEmpty(linkManEntityList.get(0).getLinkManTelephone()));
            params.put("linkManRelation", StringUtils.stripToEmpty(commonParameterService.getValueChineseName(linkManEntityList.get(0).getLinkManRelation(),"AHCS_PERSON_RELATION")));
            ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
            if (reportAccident == null) {
                LogUtil.info("案件{}出险信息不存在！", reportNo);
                throw new GlobalBusinessException(MessageFormat.format("案件{0}出险信息不存在！", reportNo));
            }
            Date accidentDate = reportAccident.getAccidentDate();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            params.put("accidentDate", StringUtils.stripToEmpty(formatter.format(accidentDate)));
            List<AccidentReasonTypeInfoDTO>  accidentReasonDTOList = commonService.getAccidentReason();
            String accidentDetail = "";
            if (ListUtils.isNotEmpty(accidentReasonDTOList)) {
                for (AccidentReasonTypeInfoDTO accidentReasonTypeInfoDTO : accidentReasonDTOList) {
                    if (StringUtils.isNotBlank(reportAccident.getAccidentCauseLevel1()) && reportAccident.getAccidentCauseLevel1().equals(accidentReasonTypeInfoDTO.getAccidentReasonTypeCode())) {
                        accidentDetail = accidentReasonTypeInfoDTO.getAccidentReasonTypeName();
                        if (ListUtils.isNotEmpty(accidentReasonTypeInfoDTO.getAccidentReasonDTOList())) {
                            for (AccidentReasonDTO accidentReasonDTO : accidentReasonTypeInfoDTO.getAccidentReasonDTOList()) {
                                if (StringUtils.isNotBlank(reportAccident.getAccidentCauseLevel2()) && reportAccident.getAccidentCauseLevel2().equals(accidentReasonDTO.getAccidentReasonCode())) {
                                    accidentDetail = accidentDetail + "-" + accidentReasonDTO.getAccidentReasonName();
                                    break;
                                }
                            }
                        }
                        break;
                    }
                }
            }
            params.put("accidentDetail", accidentDetail);
        }
        return params;
    }


    /**
     * 放弃JOB补偿
     * @param smsDTO
     * @param result
     * @return
     */
    /*
    private AsynchronousCompensationJobDTO getAsynchronousCompensationJobDTO(SmsInfoDTO smsDTO, SmsResult result) {
        String requestParam = "{\"mobile\":\"" + smsDTO.getMobileNo() + "\",\"smsContent\":\"" + smsDTO.getSmsContent() + "\"}";
        AsynchronousCompensationJobDTO jobDTO = new AsynchronousCompensationJobDTO();
        jobDTO.setId(UuidUtil.getUUID());
        jobDTO.setJobType(Constants.COMPENSATION_JOB_TYPE_7);
        jobDTO.setRetryTimes(0);
        jobDTO.setRequestParam(requestParam);
        jobDTO.setStatus("1");
        jobDTO.setUrl("thirdServiceConfig.getRepayCalUrl()");
        jobDTO.setBusinessType(Constants.COMPENSATION_BUSINESS_TYPE_72);
        jobDTO.setBusinessNo(smsDTO.getReportNo());
        jobDTO.setResponseParam("responseParam");
        jobDTO.setCreatedBy(ConstValues.SYSTEM_UM);
        jobDTO.setCreatedDate(new Date());
        jobDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
        jobDTO.setUpdatedDate(new Date());
        return jobDTO;
    }*/

    /**
     * 新建报案完成给业务员发送短信
     * @param smsTypeEnum
     * @param reportNo
     * @param caseTimes
     * @param businessUeserCode
     */
    public void sendBusinessSmsOnCreateReport(SmsTypeEnum smsTypeEnum, String reportNo,Integer caseTimes, String businessUeserCode) {
        try {
            if(StringUtils.isBlank(businessUeserCode)){
                LogUtil.info("案件{}没有处理人！",reportNo);
                return ;
            }
            UserInfoDTO userInfoDTO = Optional.ofNullable(cacheService.queryUserInfo(businessUeserCode)).orElse(new UserInfoDTO());
            if (null != userInfoDTO && StringUtils.isNotBlank(userInfoDTO.getMobile())) {
                LogUtil.info("案件：{}，发送短信：{},开始:",reportNo,smsTypeEnum.getType());
                // 报案来电号码
                String phoneNo = userInfoDTO.getMobile();
                // 发送连接，用于和报案号联合去重作用
                String sendLink = StringUtils.isEmpty(reportNo)?smsTypeEnum.getType():smsTypeEnum.getType()+":"+reportNo;
                Map<String, String> params = getSmsParams(reportNo,caseTimes,smsTypeEnum.getType(),null);
                // 发送短信内容
                String content = getSmsContent(smsTypeEnum, reportNo,params);
                // 先不加重复发送校验了
                SmsInfoDTO smsInfoDTO = new SmsInfoDTO(reportNo, phoneNo, content, sendLink);
                this.sendSmsBySync(smsInfoDTO);
                LogUtil.info("案件：{}，发送短信：{},结束！",reportNo,smsTypeEnum.getType());
            } else {
                LogUtil.info("案件{},处理人：{}在WOF未查询到信息或手机号为空！",reportNo,businessUeserCode);
            }
        } catch (GlobalBusinessException ge){
            // 短信发送失败也不用抛异常，不打印堆信息
            LogUtil.info("发送短信失败:{}",ge.getMessage());
        }catch (Exception e){
            // 短信发送失败也不用抛异常
            LogUtil.error("发送短信失败运行异常：",e);
        }
    }

    @Override
    public List<SmsRecordVO> querySmsRecordByReportNo(String reportNo) {
        if(com.paic.ncbs.claim.common.util.StringUtils.isEmptyStr(reportNo)){
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("参数不能为空"));
        }
        SmsInfoDTO smsDTO = new SmsInfoDTO();
        smsDTO.setReportNo(reportNo);
        List<SmsRecordVO> smsRecordVOS = smsInfoMapper.querySmsRecordByReportNo(smsDTO);
        return smsRecordVOS;
    }
}
