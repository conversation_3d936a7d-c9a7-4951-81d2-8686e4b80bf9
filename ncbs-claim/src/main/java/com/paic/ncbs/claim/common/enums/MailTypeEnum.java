package com.paic.ncbs.claim.common.enums;

import cn.hutool.core.util.StrUtil;

public enum MailTypeEnum {
    REINS_ACTUAL("REINS_ACTUAL", "再保邮件内容实赔模板"),
    REINS_ESTIMATED("REINS_ESTIMATED", "再保邮件内容预估模板"),
    REINS_ACTUAL_TITLE("REINS_ACTUAL_TITLE", "再保邮件标题实赔模板"),
    REINS_ESTIMATED_TITLE("REINS_ESTIMATED_TITLE", "再保邮件标题预估模板");

    private final String type;
    private final String name;

    MailTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static String getName(String type) {
        if (StrUtil.isEmpty(type)) {
            return null;
        }
        for (SmsTypeEnum caseClass : SmsTypeEnum.values()) {
            if (type.equals(caseClass.getType())) {
                return caseClass.getName();
            }
        }
        return null;
    }
}
