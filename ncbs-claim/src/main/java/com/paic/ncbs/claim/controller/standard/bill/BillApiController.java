package com.paic.ncbs.claim.controller.standard.bill;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.dto.api.StandardRequestDTO;
import com.paic.ncbs.claim.model.dto.settle.MedicalDTO;
import com.paic.ncbs.claim.model.dto.settle.ReceiptResponseDTO;
import com.paic.ncbs.claim.model.vo.duty.DutySurveyVO;
import com.paic.ncbs.claim.service.autoclaimsettle.AutoClaimSettleService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "对外收单相关接口")
@RestController
@Validated
@RequestMapping("/public/bill")
@Slf4j
public class BillApiController {
    @Autowired
    MedicalBillService medicalBillService;
    @Autowired
    private AutoClaimSettleService autoClaimSettleService;

    /**
     * 同步TPA医疗单据
     * */
    @PostMapping("/addOuterMedicalBill")
    public ResponseResult<Object> addOuterMedicalBill(@RequestBody StandardRequestDTO standardRequestDTO) throws Exception {
        String jsonString = JSONObject.toJSONString(standardRequestDTO.getRequestData());
        MedicalDTO medicalDTO = JSONObject.parseObject(jsonString, MedicalDTO.class);
        LogUtil.audit("对接外部-新增账单addOuterMedicalBill报案号{}，请求入参{}",medicalDTO.getReportNo(), JSON.toJSONString(medicalDTO));
        DutySurveyVO dutyVO = medicalBillService.addOuterMedicalBill(medicalDTO);
        //非全流程案件调用自动理算提交
        try {
            if(!"1".equals(medicalDTO.getClaimDealWay())) {
                autoClaimSettleService.autoSettleSubmit(dutyVO);
            }
        } catch (Exception e) {
            log.error("对接外部-新增账单addOuterMedicalBill报案号{}，自动理算提交异常{}",medicalDTO.getReportNo(), e.getMessage(), e);
        }
        MedicalDTO result=new MedicalDTO();
        result.setReportNo(medicalDTO.getReportNo());
        return ResponseResult.success(result);
    }
    /**
     * 案件发票明细补充接口
     * */
    @PostMapping("/receiveTpaReceipt")
    public ResponseResult<Object> receiveTpaReceipt(@RequestBody StandardRequestDTO standardRequestDTO) {
        String jsonString = JSONObject.toJSONString(standardRequestDTO.getRequestData());
        MedicalDTO medicalDTO = JSONObject.parseObject(jsonString, MedicalDTO.class);
        LogUtil.audit("案件发票明细补充接口receiveTpaReceipt报案号{}，请求入参{}",medicalDTO.getReportNo(), JSON.toJSONString(medicalDTO));
        medicalBillService.addTpaReceipt(medicalDTO);
        ReceiptResponseDTO result = new ReceiptResponseDTO();
        result.setReportNo(medicalDTO.getReportNo());
        return ResponseResult.success(result);
    }

    /**
     * 同步TPA医疗单据不涉及任务流流转
     * */
    @PostMapping("/addOuterMedicalBillNoTaskInfo")
    public ResponseResult<Object> addOuterMedicalBillNoTaskInfo(@RequestBody StandardRequestDTO standardRequestDTO) throws Exception {
        String jsonString = JSONObject.toJSONString(standardRequestDTO.getRequestData());
        MedicalDTO medicalDTO = JSONObject.parseObject(jsonString, MedicalDTO.class);
        LogUtil.audit("对接外部-新增账单addOuterMedicalBillNoTaskInfo报案号{}，请求入参{}",medicalDTO.getReportNo(), JSON.toJSONString(medicalDTO));
        medicalBillService.addOuterMedicalBillNoTaskInfo(medicalDTO);
        MedicalDTO result=new MedicalDTO();
        result.setReportNo(medicalDTO.getReportNo());
        return ResponseResult.success(result);
    }
}
