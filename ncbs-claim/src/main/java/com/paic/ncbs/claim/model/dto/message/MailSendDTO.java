package com.paic.ncbs.claim.model.dto.message;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * clms_mail_send实体类
 * <AUTHOR>
 * @since 2025-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("clms_mail_send")
public class MailSendDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private String id;
    /**
     * 业务号
     */
    @TableField(value = "business_no")
    private String businessNo;

    /**
     * 邮件类型
     */
    @TableField(value = "mail_type")
    private String mailType;
    /**
     * 邮件处理人
     */
    @TableField(value = "handler")
    private String handler;
    /**
     * 发送人
     */
    @TableField(value = "send_from")
    private String sendFrom;

    /**
     * 发送时间
     */
    @TableField(value = "send_time")
    private Date sendTime;

    /**
     * 发送状态0-未发送，1-成功，2-失败
     */
    @TableField(value = "status")
    private String status;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;
    /**
     * 内容
     */
    @TableField(value = "content")
    private String content;
    /**
     * 邮件格式html、text
     */
    @TableField(value = "format")
    private String format;
    /**
     * 收件人 多收件人；相隔
     */
    @TableField(value = "send_to")
    private String sendTo;
    /**
     * 抄送人 多抄送人；相隔
     */
    @TableField(value = "mail_cc")
    private String mailCc;
    /**
     * 密送人 多密送人；相隔
     */
    @TableField(value = "mail_bcc")
    private String mailBcc;
    /**
     *内嵌资源 文件名:文件地址，多内嵌；相隔
     */
    @TableField(value = "resources")
    private String resources;
    /**
     *  附件 文件名:文件地址 多附件；相隔
     */
    @TableField(value = "attachments")
    private String attachments;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "sys_utime")
    private Date sysUtime;
}
