package com.paic.ncbs.claim.model.vo.follow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("关注VO")
@Data
public class ClmsUserFollowCaseVO {
    @ApiModelProperty("主键id")
    private String id;

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    @ApiModelProperty("关注人")
    private String userCode;

    @ApiModelProperty("是否关注")
    private String isFollow;

    @ApiModelProperty("案件状态")
    private String caseStatus;

}
