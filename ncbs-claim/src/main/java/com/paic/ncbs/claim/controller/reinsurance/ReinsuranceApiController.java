package com.paic.ncbs.claim.controller.reinsurance;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.enums.ReinsuranceClaimTypeEnum;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.reinsurance.ReinsBillDTO;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.mapper.doc.DocumentMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateChangeMapper;
import com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper;
import com.paic.ncbs.claim.dao.mapper.other.MailSendMapper;
import com.paic.ncbs.claim.dao.mapper.reinsurance.ReinsBillMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO;
import com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO;
import com.paic.ncbs.claim.model.dto.message.MailSendDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.ReinsuranceRateDTO;
import com.paic.ncbs.claim.model.vo.reinsurance.BillSendVO;
import com.paic.ncbs.claim.model.vo.reinsurance.CodeNameVO;
import com.paic.ncbs.claim.model.vo.reinsurance.ReinsApiVO;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import io.swagger.annotations.Api;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Api(tags = "再保")
@Slf4j
@RestController
@RequestMapping("/reinsurance")
public class ReinsuranceApiController {
    @Autowired
    private ReinsuranceService reinsuranceService;
    @Autowired
    private RegisterCaseService registerCaseService;
    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private EstimateChangeMapper estimateChangeMapper;
    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private ReinsBillMapper reinsBillMapper;
    @Autowired
    private MailSendMapper mailSendMapper;
    @ApiOperation("获取最新的再保数据")
    @GetMapping("/getLastReinsurance")
    public ResponseResult getLastReinsurance(@RequestParam String reportNo, @RequestParam Integer caseTimes, @RequestParam String isDetailPage) {
        ReinsApiVO reinApiReqVO = new ReinsApiVO();
        //判断是否立案
        if (!registerCaseService.isExistRegisterRecord(reportNo, caseTimes)) {
            return ResponseResult.success();
        }
        List<CaseBaseDTO> caseBaseList = caseBaseService.getCaseBaseDTOList(reportNo, caseTimes);
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
        if (caseBaseList==null || caseBaseList.size() == 0) {
            throw new GlobalBusinessException("未找到对应案件");
        }
        ReinsuranceClaimTypeEnum claimType = ReinsuranceClaimTypeEnum.REGISTER;
        if ("1".equals(isDetailPage)) {
            WholeCaseBaseEntity wholeCase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
            if ("0".equals(wholeCase.getWholeCaseStatus())) {
                claimType = ReinsuranceClaimTypeEnum.ENDCASE;
            } else {
                //存在未决，获取未决，否则获取立案
                Integer estimateCount = estimateChangeMapper.selectCount(reportNo, caseTimes, caseBaseList.get(0).getPolicyNo());
                if(caseTimes==1){
                    if (estimateCount > 1) {
                        claimType = ReinsuranceClaimTypeEnum.ESTIMATE_LOSS;
                    }
                }else{
                    if (estimateCount == 1){
                        claimType = ReinsuranceClaimTypeEnum.CASE_REOPEN;
                    }else{
                        claimType = ReinsuranceClaimTypeEnum.ESTIMATE_LOSS;
                    }
                }
            }
        } else {
            //存在未决，获取未决，否则获取立案
            Integer estimateCount = estimateChangeMapper.selectCount(reportNo, caseTimes, caseBaseList.get(0).getPolicyNo());
            if(caseTimes==1){
                if (estimateCount > 1) {
                    claimType = ReinsuranceClaimTypeEnum.ESTIMATE_LOSS;
                }
            }else{
                if (estimateCount == 1){
                    claimType = ReinsuranceClaimTypeEnum.CASE_REOPEN;
                }else{
                    claimType = ReinsuranceClaimTypeEnum.ESTIMATE_LOSS;
                }
            }
        }
        //获取再保数据
        List<ReinsuranceRateDTO> reinsuranceRateList = reinsuranceService.getReinsuranceInfo1(reportNo, caseTimes, claimType);
        reinApiReqVO.setReinsuranceRateDTOList(reinsuranceRateList);
        //获取跳转地址
        String reinsURL = reinsuranceService.getReinsURL(caseBaseList.get(0).getPolicyNo(), reportAccident.getAccidentDate());
        reinApiReqVO.setReinsURL(reinsURL);
        return ResponseResult.success(reinApiReqVO);
    }

    @ApiOperation("根据报案号获取再保账单发送记录")
    @GetMapping("/getBillHistory")
    public ResponseResult getBillHistory(@RequestParam String reportNo) {
        ReinsApiVO reinApiReqVO = new ReinsApiVO();
        List<BillSendVO> billSendVOList = new ArrayList<>();
        ReinsBillDTO reinsBillDTO = new ReinsBillDTO();
        reinsBillDTO.setReportNo(reportNo);
        reinsBillDTO.setStatus("1");
        List<ReinsBillDTO> reinsBillList = reinsBillMapper.getBillByEntity(reinsBillDTO);
        for(ReinsBillDTO reinsBillDTO1: reinsBillList){
            List<MailSendDTO> mailSendList = mailSendMapper.getMailSendByBusinessNo(reinsBillDTO1.getId());
            for (MailSendDTO mailSendDTO: mailSendList){
                BillSendVO billSendVO = new BillSendVO();
                billSendVO.setBillType(reinsBillDTO1.getBillType());
                billSendVO.setBillNo(reinsBillDTO1.getBillNo());
                billSendVO.setReinsName(reinsBillDTO1.getReinsName());
                billSendVO.setHandler(mailSendDTO.getHandler());
                billSendVO.setSendTime(mailSendDTO.getSendTime());
                billSendVO.setStatus(mailSendDTO.getStatus());
                billSendVOList.add(billSendVO);
            }
        }
        reinApiReqVO.setBillSendVOList(billSendVOList);
        //获取账单类型
        List<CodeNameVO> codeNameVOs = new ArrayList<>();
        List<String> billTypeList = reinsBillMapper.getBillTypeByReportNo(reportNo);
        for (String billType : billTypeList){
            CodeNameVO codeNameVO = new CodeNameVO();
            codeNameVO.setCodeCode(billType);
            codeNameVO.setCodeName(Constants.BILL_TYPE_MAP.get(billType));
            codeNameVOs.add(codeNameVO);
        }
        reinApiReqVO.setCodeNameVOs(codeNameVOs);
        return ResponseResult.success(reinApiReqVO);
    }
    /**
     * 获取单证数据
     * @return
     */
    @ApiOperation("获取再保下拉列表")
    @PostMapping("/getDocumentInfo")
    public ResponseResult getDocumentInfo(@RequestBody ReinsApiVO reinsApiVO) {
        if (reinsApiVO.getReportNo() == null || "".equals(reinsApiVO.getReportNo())) {
            throw new GlobalBusinessException("参数错误");
        }
        List<CodeNameVO> codeNameVOs = new ArrayList<>();
        if ("2".equals(reinsApiVO.getCodeType())) {
            if(reinsApiVO.getDocumentOrder()==null
                    ||reinsApiVO.getDocumentType()==null||"".equals(reinsApiVO.getDocumentType())){
                throw new GlobalBusinessException("参数不全");
            }
            //取再保人数据
            List<ReinsBillDTO> reinsList = reinsBillMapper.getReinsByReportNo(reinsApiVO.getReportNo(), reinsApiVO.getDocumentType(), Integer.valueOf(reinsApiVO.getDocumentOrder()));
            for(ReinsBillDTO reins : reinsList){
                CodeNameVO codeNameVO = new CodeNameVO();
                codeNameVO.setCodeCode(reins.getReinsCode());
                codeNameVO.setCodeName(reins.getReinsName());
                codeNameVOs.add(codeNameVO);
            }
        } else if ("1".equals(reinsApiVO.getCodeType())) {
            if(reinsApiVO.getDocumentType()==null||"".equals(reinsApiVO.getDocumentType())){
                throw new GlobalBusinessException("参数不全");
            }
            //取估损编号
            List<Integer> billNoList = reinsBillMapper.getBillNoByReportNo(reinsApiVO.getReportNo(),reinsApiVO.getDocumentType());
            for (Integer billNo:billNoList){
                CodeNameVO codeNameVO = new CodeNameVO();
                codeNameVO.setCodeCode(String.valueOf(billNo));
                codeNameVO.setCodeName(String.valueOf(billNo));
                codeNameVOs.add(codeNameVO);
            }
        }
        return ResponseResult.success(codeNameVOs);

    }
    /**
     * 获取单证和收件人信息
     * @return
     */
    @ApiOperation("获取单证和收件人信息")
    @PostMapping("/getDocAndRecipient")
    public ResponseResult getDocAndRecipient(@RequestBody ReinsApiVO reinsApiVO) {
        if(reinsApiVO.getReportNo()==null || "".equals(reinsApiVO.getReportNo())){
            throw new GlobalBusinessException("报案号不能为空");
        }
        if(reinsApiVO.getReinsCode()==null || "".equals(reinsApiVO.getReinsCode())){
            throw new GlobalBusinessException("再保人不能为空");
        }
        ReinsBillDTO reinsBillDTO = new ReinsBillDTO();
        reinsBillDTO.setBillType(reinsApiVO.getDocumentType());
        reinsBillDTO.setReportNo(reinsApiVO.getReportNo());
        reinsBillDTO.setBillNo(Integer.valueOf(reinsApiVO.getDocumentOrder()));
        reinsBillDTO.setReinsCode(reinsApiVO.getReinsCode());
        reinsBillDTO.setStatus("1");
        List<ReinsBillDTO> reinsBillList = reinsBillMapper.getBillByEntity(reinsBillDTO);
        FileDocumentDTO fileDocument = fileUploadService.queryDocumentListById(reinsBillList.get(0).getDocumentGroupItemsId());
        List<FileDocumentDTO> fileDocumentList = new ArrayList<>();
        fileDocumentList.add(fileDocument);
        List<FileDocumentDTO> list =fileUploadService.queryDocumentList(reinsApiVO.getReportNo());
        if(list!=null && list.size()>0){
            list = list.stream()
                    .filter(fileDocumentDTO -> !"014007".equals(fileDocumentDTO.getDocumentType())
                            && !"014008".equals(fileDocumentDTO.getDocumentType()))
                    .collect(Collectors.toList());
        }
        if(list!=null && list.size()>0){
            fileDocumentList.addAll(list);
        }
        reinsApiVO.setDocumentList(fileDocumentList);
        //获取收件人信息
        reinsApiVO.setRecipientList(reinsuranceService.getReinsuranceContact(reinsApiVO.getReinsCode()));
        return ResponseResult.success(reinsApiVO);
    }
    @ApiOperation("邮件发送")
    @PostMapping("/sendReinsEmail")
    public ResponseResult sendReinsEmail(@RequestBody ReinsApiVO reinsApiVO) {
        if(reinsApiVO.getReportNo()==null|| "".equals(reinsApiVO.getReportNo())){
            throw new GlobalBusinessException("参数错误");
        }
        String msg = reinsuranceService.sendReinsEmail(reinsApiVO);
        return ResponseResult.success(msg);
    }
}
