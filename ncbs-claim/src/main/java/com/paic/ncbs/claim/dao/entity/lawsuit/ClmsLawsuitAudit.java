package com.paic.ncbs.claim.dao.entity.lawsuit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 诉讼批复表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Getter
@Setter
@TableName("clms_lawsuit_audit")
public class ClmsLawsuitAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 关联诉讼案件ID
     */
    @TableField("lawsuit_case_id")
    private String lawsuitCaseId;

    /**
     * 保单号
     */
    @TableField("policy_no")
    private String policyNo;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 诉讼案编号
     */
    @TableField("lawsuit_no")
    private String lawsuitNo;

    /**
     * 提交人
     */
    @TableField("submitter")
    private String submitter;

    /**
     * 提交人名称
     */
    @TableField("submitter_name")
    private String submitterName;

    /**
     * 提交时间
     */
    @TableField("submit_time")
    private LocalDateTime submitTime;

    /**
     * 批复意见 1-批复,2-移交批复
     */
    @TableField("audit_opinion")
    private String auditOpinion;

    /**
     * 批复机构
     */
    @TableField("audit_department")
    private String auditDepartment;

    /**
     * 批复人
     */
    @TableField("audit_code")
    private String auditCode;

    /**
     * 批复人
     */
    @TableField("audit_name")
    private String auditName;

    /**
     * 状态 1-待批复,2-已批复待办结,3-办结
     */
    @TableField("status")
    private String status;

    /**
     * 意见说明
     */
    @TableField("opinion_desc")
    private String opinionDesc;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;
}
