package com.paic.ncbs.claim.model.dto.lawsuit;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 诉讼批复表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Getter
@Setter
@TableName("clms_lawsuit_audit")
public class ClmsLawsuitAuditDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 关联诉讼案件ID
     */
    private String lawsuitCaseId;

    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Short caseTimes;

    /**
     * 诉讼案编号
     */
    private String lawsuitNo;

    /**
     * 序号
     */
    private String serialNo;

    /**
     * 提交人
     */
    private String submitter;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 批复意见 1-批复,2-移交批复
     */
    private String auditOpinion;

    /**
     * 批复机构
     */
    private String auditDepartment;

    /**
     * 批复人
     */
    private String auditCode;

    /**
     * 批复人
     */
    private String auditName;

    /**
     * 状态 1-待批复,2-已批复待办结,3-办结
     */
    private String status;

    /**
     * 意见说明
     */
    private String opinionDesc;
}
