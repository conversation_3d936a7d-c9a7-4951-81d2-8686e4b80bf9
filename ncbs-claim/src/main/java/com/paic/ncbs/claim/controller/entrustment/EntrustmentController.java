package com.paic.ncbs.claim.controller.entrustment;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Api(tags = "第三方委托")
@RestController
@RequestMapping("/who/app/entrustmentAction")
public class EntrustmentController extends BaseController {

    @Autowired
    private EntrustmentService entrustmentService;


    @ApiOperation("保存第三方委托")
    @PostMapping("/saveEntrustment")
    public ResponseResult<Object> saveEntrustment(@RequestBody EntrustmentApiVo entrustmentApiVo) {
        return entrustmentService.saveEntrustment(entrustmentApiVo);
    }

    @ApiOperation("查询委托数据")
    @PostMapping("/getEntrustData")
    public ResponseResult<EntrustmentApiVo> getEntrustData(@RequestBody EntrustmentApiVo entrustmentApiVo) {
        return entrustmentService.getEntrustData(entrustmentApiVo);
    }



    @ApiOperation("提交委托审批")
    @PostMapping("/submitEntrustAudit")
    public ResponseResult<Object> submitEntrustAudit(@RequestBody EntrustAuditDTO entrustAuditDTO) throws GlobalBusinessException {
        LogUtil.audit("#委托· 完成委托审批#入参#entrustmentAudit=" + entrustAuditDTO);
        entrustmentService.submitentrustAudit(entrustAuditDTO);
        return ResponseResult.success();
    }

    @ApiOperation("查询事故场景")
    @GetMapping(value = "/getAccidentSceneData/{collectionCode}")
    public ResponseResult<List<AccidentSceneDto>> getAccidentSceneData(@PathVariable("collectionCode") String collectionCode) {
        return ResponseResult.success(entrustmentService.getAccidentSceneData(collectionCode));
    }

    @ApiOperation("获取打印委托列表")
    @GetMapping("/getEntrustmentForPrint")
    public ResponseResult<Object> getEntrustmentForPrint(@RequestParam("reportNo") String reportNo) {
        return entrustmentService.getEntrustmentForPrint(reportNo);
    }

    @ApiOperation("获取审批用户列表")
    @GetMapping("/getApprovalUsers")
    public ResponseResult<List<UserInfoDTO>> getApprovalUsers(
            @RequestParam("reportNo") String reportNo,
            @RequestParam("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        List<UserInfoDTO> approvalUsers = entrustmentService.getApprovalUsers(reportNo,caseTimes);
        return ResponseResult.success(approvalUsers);
    }

    @ApiOperation("委托次数")
    @GetMapping(value = "/getEntrustmentCount/{reportNo}/{caseTimes}")
    public ResponseResult<Integer> getEntrustmentCount(@PathVariable("reportNo") String reportNo, @PathVariable("caseTimes") Integer caseTimes) {
        return ResponseResult.success(entrustmentService.getEntrustmentCount(reportNo, caseTimes));
    }
}