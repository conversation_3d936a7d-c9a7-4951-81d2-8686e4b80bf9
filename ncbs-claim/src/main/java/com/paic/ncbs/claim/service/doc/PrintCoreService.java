package com.paic.ncbs.claim.service.doc;

import com.paic.ncbs.claim.common.enums.ReinsuranceClaimTypeEnum;
import com.paic.ncbs.claim.dao.entity.reinsurance.ReinsBillDTO;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.model.vo.doc.PrintDutyPayVO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-04-26 17:04
 */
public interface PrintCoreService {
    /**
     * 异步请求
     */
    void saveFileAsync(String docNo, String xslFileName, String before, PrintDutyPayVO printDutyPayVO);

    /**
     * 异步请求
     */
    void saveCommissionFileAsync(String docNo, String xslFileName, String before, PrintEntrustDTO printEntrustDTO);

    void saveFile(String docNo, String xslFileName, String before,PrintDutyPayVO printDutyPayVO);

    void saveCommissionFile(String docNo, String xslFileName, String before,PrintEntrustDTO printEntrustDTO);
    void saveReinsuranceFileAsync(String reportNo, Integer caseTimes, ReinsuranceClaimTypeEnum claimType,String userId);
    /**
     * 生成再保账单
     * @param reinsBillDTOList
     */
    void buildReinsBill(List<ReinsBillDTO> reinsBillDTOList);

    void saveCoinsFileAsync(String reportNo, Integer caseTimes, String claimType, UserInfoDTO userInfoDTO,Integer subTimes);
}
