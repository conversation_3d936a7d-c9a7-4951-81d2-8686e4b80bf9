package com.paic.ncbs.claim.model.dto.message;

import com.paic.ncbs.message.model.dto.MailSendAttachmentParam;
import com.paic.ncbs.message.model.dto.MailSendParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
public class MailSendParamNew {
    private String from;
    private String to;
    private String cc;
    private String bcc;
    private String subject;
    private String format;
    private String body;
    private List<MailSendAttachmentParam> attachments;
    private List<MailSendAttachmentParam> resources;
}
