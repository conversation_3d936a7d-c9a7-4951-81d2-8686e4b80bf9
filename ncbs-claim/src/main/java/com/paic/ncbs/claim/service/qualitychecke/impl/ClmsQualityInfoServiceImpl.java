package com.paic.ncbs.claim.service.qualitychecke.impl;

import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.EstimateConstValues;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.qualitychecke.*;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.entity.user.DepartmentDefineEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.ClmsEstimateRecordMapper;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.qualitychecke.ClmsQualityDetailMapper;
import com.paic.ncbs.claim.dao.mapper.qualitychecke.ClmsQualityInfoMapper;
import com.paic.ncbs.claim.dao.mapper.qualitychecke.ClmsQualityRecordMapper;
import com.paic.ncbs.claim.dao.mapper.qualitychecke.ImportQualityRecordMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyPayMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.dao.mapper.verify.VerifyMapper;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.quality.QualityrequstVO;
import com.paic.ncbs.claim.model.vo.report.DepartmentVO;
import com.paic.ncbs.claim.model.vo.taskdeal.ClaimQuanlityCaseVO;
import com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.qualitychecke.ClmsQualityDetailService;
import com.paic.ncbs.claim.service.qualitychecke.ClmsQualityInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.paic.ncbs.claim.service.qualitychecke.ClmsQualityRecordService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static com.paic.ncbs.claim.common.constant.BpmConstants.INVESTIGATE_PLATFORM_NAME;

/**
 * <p>
 * 案件质检信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-14
 */
@Slf4j
@Service
public class ClmsQualityInfoServiceImpl extends ServiceImpl<ClmsQualityInfoMapper, ClmsQualityInfo> implements ClmsQualityInfoService {
    @Autowired
    private TaskInfoMapper taskInfoMapper ;
    @Autowired
    private CommonParameterMapper commonParameterMapper;
    @Autowired
    private ClmsQualityInfoMapper clmsQualityInfoMapper;
    @Autowired
    private ClmsQualityDetailMapper clmsQualityDetailMapper;
    @Autowired
    private ClmsQualityDetailService clmsQualityDetailService;
    @Autowired
    private TaskListService taskListService;
    @Autowired
    private CaseBaseMapper caseBaseMapper;
    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private FeePayMapper feePayMapper;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private CacheService cacheService ;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    TaskPoolService taskPoolService;
    @Autowired
    private InvestigateTaskMapper investigateTaskDao;
    @Autowired
    private ClmsQualityRecordService  clmsQualityRecordService;
    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private VerifyMapper verifyMapper;
    @Autowired
    private PolicyPayMapper policyPayDao;
    @Autowired
    private ClmsEstimateRecordMapper estimateRecordMapper;
    @Override
    public List<WorkBenchTaskVO> getWorkBenchTaskList(ClaimQuanlityCaseVO workBenchTaskQueryVO) {
        List<WorkBenchTaskVO> workBenchTaskList;
        String DepartmentCode = WebServletContext.getDepartmentCode();
        if(StringUtils.isNotEmpty(workBenchTaskQueryVO.getHandleCom())){
            DepartmentCode=workBenchTaskQueryVO.getHandleCom();
        }
        List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(DepartmentCode);
        workBenchTaskQueryVO.setDepartmentCodes(departmentCodes);
        workBenchTaskQueryVO.setIsIncludeSubordinates("Y");
        workBenchTaskList = taskInfoMapper.getQualityCaseList(workBenchTaskQueryVO);
        // 根据proportion或sum字段进行随机抽取
        if (workBenchTaskList != null && !workBenchTaskList.isEmpty()) {
            // 处理抽取比例
            if (StringUtils.isNotEmpty(workBenchTaskQueryVO.getProportion())) {
                try {
                    double proportion = Double.parseDouble(workBenchTaskQueryVO.getProportion());
                    if (proportion > 0 && proportion < 100) {
                        int sampleSize = (int) Math.round(workBenchTaskList.size() * proportion / 100.0);
                        if (sampleSize > 0 && sampleSize < workBenchTaskList.size()) {
                            workBenchTaskList = getRandomSample(workBenchTaskList, sampleSize);
                        }
                    }
                } catch (NumberFormatException e) {
                    // 比例格式错误，不进行抽取
                    log.warn("抽取比例格式错误: {}", workBenchTaskQueryVO.getProportion());
                }
            }
            // 处理抽取件数
            else if (StringUtils.isNotEmpty(workBenchTaskQueryVO.getSum())) {
                try {
                    int sum = Integer.parseInt(workBenchTaskQueryVO.getSum());
                    if (sum > 0 && sum < workBenchTaskList.size()) {
                        workBenchTaskList = getRandomSample(workBenchTaskList, sum);
                    }
                    // 如果sum >= workBenchTaskList.size()，则不需要抽取，返回全部数据
                } catch (NumberFormatException e) {
                    // 件数格式错误，不进行抽取
                    log.warn("抽取件数格式错误: {}", workBenchTaskQueryVO.getSum());
                }
            }
        }
       /* // 批量获取所有支付项信息，避免N+1查询问题
        if (!CollectionUtils.isEmpty(workBenchTaskList)) {
            // 提取所有taskId并准备批量查询
            List<String> taskIds = workBenchTaskList.stream()
                    .map(vo -> vo.getTaskId().split("_")[0])
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询所有相关的支付项
            if (!CollectionUtils.isEmpty(taskIds)) {
                List<PaymentItemDTO> allPaymentItems = paymentItemService.getPaymentItemsByIds(taskIds);

                // 按taskId分组支付项
                Map<String, List<PaymentItemDTO>> paymentItemsMap = allPaymentItems.stream()
                        .collect(Collectors.groupingBy(PaymentItemDTO::getIdClmPaymentItem));

                // 填充每条记录的支付信息
                workBenchTaskList.forEach(vo -> {
                    String taskId = vo.getTaskId().split("_")[0];
                    List<PaymentItemDTO> paymentItemList = paymentItemsMap.get(taskId);
                    if (CollectionUtils.isNotEmpty(paymentItemList) && paymentItemList.size() == 1) {
                        PaymentItemDTO paymentItem = paymentItemList.get(0);
                        vo.setClmPaymentTypeName(paymentItem.getPaymentTypeName());
                        vo.setPaymentAmount(BaseConstant.STRING_1.equals(paymentItem.getIsFullPay())
                                ? paymentItem.getCoinsuranceActualAmount()
                                : paymentItem.getPaymentAmount());
                    }
                });
            }
        }*/
        return workBenchTaskList;
    }
    /**
     * 从列表中随机抽取指定数量的元素
     * @param list 原始列表
     * @param sampleSize 抽取数量
     * @return 随机抽取的元素列表
     */
    private <T> List<T> getRandomSample(List<T> list, int sampleSize) {
        if (list == null || list.size() <= sampleSize) {
            return list;
        }

        List<T> copy = new ArrayList<>(list);
        List<T> result = new ArrayList<>(sampleSize);
        Random random = new Random();

        while (result.size() < sampleSize) {
            int index = random.nextInt(copy.size());
            result.add(copy.get(index));
            copy.remove(index);
        }

        return result;
    }
    @Override
    public ClmsQualityInfo selectQualityInfoById(String id) {
        ClmsQualityInfo clmsQualityInfo = clmsQualityInfoMapper.selectQualityInfoById(id);
        return clmsQualityInfo;
    }

    @Override
    public List<ClmsQualityInfo> selectAllQualityInfo(String qinspector) {
        List<ClmsQualityInfo> clmsQualityInfoList = clmsQualityInfoMapper.selectAllQualityInfo(qinspector);
        return clmsQualityInfoList;
    }

    @Override
    public List<ClmsQualityInfo> selectQualityInfoByCondition(ClmsQualityInfo condition) {
        List<ClmsQualityInfo> clmsQualityInfoList = clmsQualityInfoMapper.selectQualityInfoByCondition(condition);
        for (ClmsQualityInfo info : clmsQualityInfoList) {
            info.setQinitiator(getUserName(info.getQinitiator()));
        }
        return clmsQualityInfoList;
    }

    @Override
    @Transactional
    public boolean updateQualityInfo(QualityInfoDetailVO clmsQualityInfo) {
        ClmsQualityInfo clmsQualityInfos = clmsQualityInfo.getQualityInfo();
        clmsQualityInfos.setProcessTime(LocalDateTime.now());
        List<ClmsQualityDetail> clmsQualityDetails = clmsQualityInfo.getQualityDetails();
        clmsQualityInfos.setTaskStatus("1");
        clmsQualityInfos.setUpdatedBy(WebServletContext.getUserId());
        if("update".equals(clmsQualityInfo.getSubmitflag())){
            if(clmsQualityDetails != null && clmsQualityDetails.size() > 0){
                clmsQualityDetailMapper.deleteQualityDetailsBySerialNo(clmsQualityInfos.getId());
                for (ClmsQualityDetail detail : clmsQualityDetails) {
                    if(StringUtils.isEmptyStr(detail.getCreatedBy())){
                        detail.setCreatedBy(WebServletContext.getUserId());
                    }
                    if (StringUtils.isEmptyStr(detail.getUpdatedBy())){
                        detail.setUpdatedBy(WebServletContext.getUserId());
                    }
                    int result = clmsQualityDetailService.insertQualityDetail(detail);
                    if (result <= 0) {
                        LogUtil.error("插入质检详情失败，质检信息ID: " + clmsQualityInfos.getId() + ", 序列号: " + clmsQualityInfos.getSerialNo() + ", 详情ID: " + detail.getId());
                        throw new RuntimeException("插入质检详情失败，详情ID: " + detail.getId());
                    }
                }
            }
            if (clmsQualityInfoMapper.updateQualityInfo(clmsQualityInfos) > 0) {
                return true;
            }
        }else if("submit".equals(clmsQualityInfo.getSubmitflag())){
            if(clmsQualityDetails != null && clmsQualityDetails.size() > 0){
                clmsQualityDetailMapper.deleteQualityDetailsBySerialNo(clmsQualityInfos.getId());
                for (ClmsQualityDetail detail : clmsQualityDetails) {
                    if(StringUtils.isEmptyStr(detail.getCreatedBy())){
                        detail.setCreatedBy(WebServletContext.getUserId());
                    }
                    if (StringUtils.isEmptyStr(detail.getUpdatedBy())){
                        detail.setUpdatedBy(WebServletContext.getUserId());
                    }
                    int result = clmsQualityDetailService.insertQualityDetail(detail);
                    if (result <= 0) {
                        LogUtil.error("插入质检详情失败，质检信息ID: " + clmsQualityInfos.getId() + ", 序列号: " + clmsQualityInfos.getSerialNo() + ", 详情ID: " + detail.getId());
                        throw new RuntimeException("插入质检详情失败，详情ID: " + detail.getId());
                    }
                }
            }
            clmsQualityInfos.setHandler(WebServletContext.getUserId());
            clmsQualityInfos.setNode("0");
            clmsQualityRecordService.insertrecord(clmsQualityInfos);
            bpmService.completeTask_oc(clmsQualityInfos.getReportNo(), Integer.valueOf(clmsQualityInfos.getCaseTimes()), BpmConstants.OC_QUALITY_REVIEW);
            if("1".equals(clmsQualityInfos.getIsglobal())){
                clmsQualityInfos.setIsEnd("1");
                clmsQualityInfos.setTaskStatus("2");
                //clmsQualityInfos.setNode("2");
            }else{
                clmsQualityInfos.setNode("1");
                bpmService.startProcessOc(clmsQualityInfos.getReportNo(), Integer.valueOf(clmsQualityInfos.getCaseTimes()), BpmConstants.OC_QUALITY_CHECK_REVIEW, clmsQualityInfos.getId(), null, WebServletContext.getDepartmentCode());
            }
            if (clmsQualityInfoMapper.updateQualityInfo(clmsQualityInfos) > 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean deleteQualityInfoById(String id) {
        ClmsQualityInfo clmsQualityInfo = clmsQualityInfoMapper.selectQualityInfoById(id);
        if(clmsQualityInfo!=null){
            String taskStatus = clmsQualityInfo.getTaskStatus();
            if(StringUtils.isNotEmpty(taskStatus)&&"0".equals(taskStatus)){
                if (clmsQualityInfoMapper.deleteQualityInfoById(id) > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    @Transactional
    public QualityrequstVO insertQualityInfo(QualityrequstVO qualityrequstVO) {
        String uuid = UuidUtil.getUUID();
        String usercode = WebServletContext.getUserId();
        ClmsQualityInfo clmsQualityInfo = new ClmsQualityInfo();
        clmsQualityInfo.setId(uuid);
        clmsQualityInfo.setHandleCom(qualityrequstVO.getCompany());
        String isglobal = qualityrequstVO.getIsglobal();
        if(StringUtils.isNotEmpty(isglobal) && "true".equals(isglobal)){
            clmsQualityInfo.setIsglobal("1");
        }else{
            clmsQualityInfo.setIsglobal("0");
        }
        clmsQualityInfo.setIsEnd("0");
        clmsQualityInfo.setTaskStatus("0");
        clmsQualityInfo.setNode("0");
        if(StringUtils.isEmptyStr(qualityrequstVO.getPersonno())){
            return null;
        }
        // 设置基本字段
        clmsQualityInfo.setReportNo(qualityrequstVO.getReportNo());
        clmsQualityInfo.setStartTime(LocalDateTime.now());
        clmsQualityInfo.setQinitiator(usercode);
        clmsQualityInfo.setHandler(qualityrequstVO.getPersonno());
        if(StringUtils.isNotEmpty(qualityrequstVO.getPersonno())&&StringUtils.isNotEmpty(qualityrequstVO.getPerson())){
            clmsQualityInfo.setQinspector(qualityrequstVO.getPerson());
        }else{
            clmsQualityInfo.setQinspector(getUserName(qualityrequstVO.getPersonno()));
        }
        if(StringUtils.isNotEmpty(qualityrequstVO.getBatchNo())){
            clmsQualityInfo.setBatchNo(qualityrequstVO.getBatchNo());
            clmsQualityInfo.setImportTime(LocalDateTime.now());
        }
        if(!StringUtils.isEmptyStr(qualityrequstVO.getPersonno())){
            usercode=qualityrequstVO.getPersonno();
        }
        clmsQualityInfo.setCreatedBy(WebServletContext.getUserId());
        clmsQualityInfo.setUpdatedBy(WebServletContext.getUserId());
        qualityrequstVO.setQualityno(clmsQualityInfo.getId());
        Short caseTimes = 1;
        if(qualityrequstVO.getCaseTimes() != null && qualityrequstVO.getCaseTimes() > 0){
            caseTimes = Short.parseShort(String.valueOf(qualityrequstVO.getCaseTimes()));
        }
        ClmsQualityInfo clmsQualityInfoList =clmsQualityInfoMapper.selectQualityInfoWithMaxQiserNo(qualityrequstVO.getReportNo(), caseTimes);
        if(clmsQualityInfoList!=null){
            if("0".equals(clmsQualityInfoList.getIsEnd())){
                return null;
            }
        }
        clmsQualityInfo.setCaseTimes(caseTimes);
        int serialNo = Integer.parseInt(clmsQualityInfoMapper.selectMaxSerialNo(qualityrequstVO.getReportNo(), caseTimes));
        clmsQualityInfo.setSerialNo(String.valueOf(serialNo+1));
        clmsQualityInfo.setQiserNo(String.valueOf(serialNo+1));
        // 查询案件基本信息
        List<CaseBaseEntity> caseBase = caseBaseMapper.getCaseBaseInfoByReportNoAndCasetimes(
                qualityrequstVO.getReportNo(),
                caseTimes.toString()
        );
        WholeCaseBaseDTO wholeCaseBase =  wholeCaseBaseMapper.getWholeCaseBase(qualityrequstVO.getReportNo(), caseTimes);
        if(wholeCaseBase!=null){
            if (wholeCaseBase.getEndCaseDate() != null) {
                clmsQualityInfo.setCloseDate(wholeCaseBase.getEndCaseDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            }
        }
        if (caseBase != null&&caseBase.size()>0) {
            // 设置保单号
            clmsQualityInfo.setPolicyNo(caseBase.get(0).getPolicyNo());
            clmsQualityInfo.setRegistNo(caseBase.get(0).getRegistNo());
            AhcsPolicyInfoEntity entitiy= ahcsPolicyInfoMapper.getPolicyProductCode(clmsQualityInfo.getReportNo(),clmsQualityInfo.getPolicyNo());
            if(entitiy!=null){
                clmsQualityInfo.setProductName(entitiy.getProductName());
            }
            // 设置承保机构和理赔机构
            DepartmentDefineEntity underwritingDept = departmentDefineMapper.getDepartmentInfo(caseBase.get(0).getDepartmentCode());
            if (underwritingDept != null) {
                clmsQualityInfo.setComCode(underwritingDept.getDepartmentAbbrName());
            }
            clmsQualityInfo.setClaimOrg(underwritingDept != null ? underwritingDept.getDepartmentAbbrName() : "");
        }else{
            if(!"true".equals(isglobal)){
                return null;
            }
        }

        // 查询出险人信息
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoMapper.getReportCustomerInfoByReportNo(qualityrequstVO.getReportNo());
        if (customerInfo != null) {
            clmsQualityInfo.setInsuredPerson(customerInfo.getName());
        }

        // 查询案件金额
        BigDecimal b = policyPayDao.getSumPayFee(qualityrequstVO.getReportNo(), qualityrequstVO.getCaseTimes());
        List<ClmsEstimateRecord> clmsEstimateRecords = estimateRecordMapper.selectByReportNoAndType(qualityrequstVO.getReportNo(), caseTimes.toString(), EstimateConstValues.ESTIMATE_TYPE_REGISTRATION);
        //String caseAmount = feePayMapper.getFeePayForSum(qualityrequstVO.getReportNo(), qualityrequstVO.getCaseTimes());
        if (b != null ) {
            clmsQualityInfo.setCaseAmount(b);
        }else{
            if(clmsEstimateRecords!=null&&clmsEstimateRecords.size()>0){
                clmsQualityInfo.setCaseAmount(clmsEstimateRecords.get(0).getEstimateAmount());
            }
        }

        // 查询处理人员信息
        // 立案人员 - OC_REPORT_TRACK
        if (wholeCaseBase != null) {
            clmsQualityInfo.setCaseCreator(getUserName(wholeCaseBase.getRegisterUm()));
        }

        // 调查跟进人 - OC_INVESTIGATE_APPROVAL
        String investigateTask = investigateTaskDao.getinvestigatorumname(
                qualityrequstVO.getReportNo(),
                qualityrequstVO.getCaseTimes());
        if (investigateTask != null) {
            clmsQualityInfo.setInvestigator(getUserlist(investigateTask));
        }

        // 理算人员 - OC_MANUAL_SETTLE
        String settleTask = verifyMapper.getSettleumPerson(
                qualityrequstVO.getReportNo(),
                qualityrequstVO.getCaseTimes()
        );
        if (settleTask != null) {
            clmsQualityInfo.setAdjuster(getUserName(settleTask));
        }

        // 核赔人员 - OC_SETTLE_REVIEW
        String AssigneeName = verifyMapper.getverifyunPerson(
                qualityrequstVO.getReportNo(),
                qualityrequstVO.getCaseTimes()
        );
        if (AssigneeName != null) {
            clmsQualityInfo.setClaimApprover(getUserlist(AssigneeName));
        }

        ClmsQualityDetail clmsQualityDetail = new ClmsQualityDetail();
        clmsQualityDetail.setReportNo(clmsQualityInfo.getReportNo());
        clmsQualityDetail.setPolicyNo(clmsQualityInfo.getPolicyNo());
        clmsQualityDetail.setSerialNo(clmsQualityInfo.getId());
        clmsQualityDetail.setCaseTimes(clmsQualityInfo.getCaseTimes());
        clmsQualityDetail.setCreatedBy(WebServletContext.getUserId());
        clmsQualityDetail.setUpdatedBy(WebServletContext.getUserId());
        boolean insertQualityInfo = clmsQualityInfoMapper.insertQualityInfo(clmsQualityInfo) > 0;
        boolean insertQualityDetail = clmsQualityDetailService.insertQualityDetail(clmsQualityDetail)>0;
        if (insertQualityInfo&&insertQualityDetail) {
            //clmsQualityRecordService.insertrecord(clmsQualityInfo);
            TaskInfoDTO saskInfoDTO = taskInfoMapper.getTaskAssignerNameNew(qualityrequstVO.getReportNo(), Integer.valueOf(caseTimes), BpmConstants.OC_QUALITY_REVIEW);
            if(saskInfoDTO==null){
                bpmService.startProcessOc(qualityrequstVO.getReportNo(), Integer.valueOf(caseTimes), BpmConstants.OC_QUALITY_REVIEW, clmsQualityInfo.getId(), usercode, WebServletContext.getDepartmentCode());
            }
            return qualityrequstVO;
        }
        return null;
    }

    public String getUserlist(String AssigneeName) {
        if (AssigneeName.contains(",")) {
            String[] userCodes = AssigneeName.split(",");
            List<String> userNames = new ArrayList<>();
            for (String userCode : userCodes) {
                String userName = getUserName(userCode.trim());
                if (StringUtils.isNotEmpty(userName)) {
                    userNames.add(userName);
                } else {
                    userNames.add(userCode.trim());
                }
            }
            return String.join(",", userNames);
        } else {
            return getUserName(AssigneeName);
        }
    }

    @Override
    public List<QualityInfoWithDetailVO> selectQualityInfoByConditions(QualityQueryVO queryVO) {
        List<QualityInfoWithDetailVO> clmsQualityInfoList = clmsQualityInfoMapper.selectQualityInfoByConditions(queryVO);
        // 遍历列表，根据规则转换inspnStage和inspnStandard字段
        for (QualityInfoWithDetailVO qualityInfo : clmsQualityInfoList) {
            String inspnStage = qualityInfo.getInspnStage();
            String inspnStandard = qualityInfo.getInspnStandard();
            String qualityType = qualityInfo.getQualityType();
            String qualityperson = getUserName(qualityInfo.getQinitiator());
            String handlercom = qualityInfo.getHandleCom();

            if (inspnStage != null && qualityType != null) {
                // 根据qualityType的值拼接前缀
                String prefix = "0".equals(qualityType) ? "q" : "S";
                String collectionCode = prefix + inspnStage;

                // 查询对应的CommonParameterTinyDTO
                CommonParameterTinyDTO paramDTO = commonParameterMapper.getCommonParameterByCollectionCodeAndValueCode(
                        collectionCode, inspnStandard);

                // 如果查询到结果，则更新字段值
                if (paramDTO != null) {
                    qualityInfo.setInspnStage(paramDTO.getCollectionName());
                    qualityInfo.setInspnStandard(paramDTO.getValueChineseName());
                }
            }
            if(StringUtils.isNotEmpty(qualityperson)){
                qualityInfo.setQinitiator(qualityperson);
            }
            if(StringUtils.isNotEmpty(handlercom)){
                DepartmentVO departmentVO = taskPoolService.getSelectDepartmentList4Query(handlercom);
                if (departmentVO != null) {
                    qualityInfo.setHandleCom(departmentVO.getDepartmentAbbrName());
                }
            }
        }
        return clmsQualityInfoList;
    }

    @Override
    public QualityInfoDetailVO selectQualityInfoDetailByCondition(String reportNo, Short caseTimes, String id) {
        List<ClmsQualityRecord> qualityRecords = clmsQualityRecordService.selectByReportNoAndCaseTimes(reportNo, caseTimes,id);
        QualityInfoDetailVO qualityInfoDetailVO = clmsQualityInfoMapper.selectQualityInfoDetailByCondition(reportNo, caseTimes, id);
        if(qualityRecords!=null&&qualityRecords.size()>0 ){
            qualityInfoDetailVO.setQualityRecords(qualityRecords);
        }
        return qualityInfoDetailVO;
    }

    @Override
    @Transactional
    public String updateQualityInfoByReportNoAndCaseTimes(ClmsQualityInfo clmsQualityInfo) {
        // 参数校验
        if (clmsQualityInfo == null) {
            return "请求参数不能为空";
        }

        if (clmsQualityInfo.getReportNo() == null) {
            return "报案号不能为空";
        }

        if (clmsQualityInfo.getCaseTimes() == null) {
            return "赔付次数不能为空";
        }
        clmsQualityInfo.setProcessTime(LocalDateTime.now());
        clmsQualityInfo.setUpdatedBy(WebServletContext.getUserId());
        clmsQualityInfo.setNode("1");
        clmsQualityRecordService.insertrecord(clmsQualityInfo);
        if("0".equals(clmsQualityInfo.getApprovalOpinion())){
            clmsQualityInfo.setEndTime(LocalDateTime.now());
            clmsQualityInfo.setIsEnd("1");
            clmsQualityInfo.setTaskStatus("2");
            bpmService.completeTask_oc(clmsQualityInfo.getReportNo(), Integer.valueOf(clmsQualityInfo.getCaseTimes()), BpmConstants.OC_QUALITY_CHECK_REVIEW);
        }else if("1".equals(clmsQualityInfo.getApprovalOpinion())){
            bpmService.completeTask_oc(clmsQualityInfo.getReportNo(), Integer.valueOf(clmsQualityInfo.getCaseTimes()), BpmConstants.OC_QUALITY_CHECK_REVIEW);
            TaskInfoDTO tdto =  taskInfoMapper.findLatestByReportNoAndBpmKey(clmsQualityInfo.getReportNo(), Integer.valueOf(clmsQualityInfo.getCaseTimes()), BpmConstants.OC_QUALITY_REVIEW);
            clmsQualityInfo.setNode("0");
            bpmService.startProcessOc(clmsQualityInfo.getReportNo(), Integer.valueOf(clmsQualityInfo.getCaseTimes()), BpmConstants.OC_QUALITY_REVIEW, clmsQualityInfo.getId(),tdto.getAssigner(), tdto.getDepartmentCode());
        }else if("2".equals(clmsQualityInfo.getApprovalOpinion())){
            if(WebServletContext.getUserId().equals(clmsQualityInfo.getAtransferPerson())||clmsQualityInfo.getQinitiator().equals(clmsQualityInfo.getAtransferPerson())){
                return "不能将任务移交给自己";
            }
            clmsQualityInfo.setHandler(clmsQualityInfo.getAtransferPerson());
            clmsQualityInfo.setQinspector(getUserName(clmsQualityInfo.getAtransferPerson()));
            taskInfoMapper.reAssign(clmsQualityInfo.getId(),clmsQualityInfo.getAtransferPerson(),getUserName(clmsQualityInfo.getAtransferPerson()),WebServletContext.getDepartmentCode());
        }
        int result = clmsQualityInfoMapper.updateQualityInfoByReportNoAndCaseTimes(clmsQualityInfo);
        if (result <= 0) {
            return "更新质检信息失败";
        }
        return null;
    }

    @Override
    public String getUserName(String userCode){
        if (BpmConstants.INVESTIGATE_PLATFORM.equals(userCode)) {
            return INVESTIGATE_PLATFORM_NAME;
        }

        if (StringUtils.isNotEmpty(userCode) ){
            try {
                UserInfoDTO userInfo = cacheService.queryUserInfo(userCode);
                if (userInfo != null){
                    return userInfo.getUserName();
                }
            }catch (NcbsException e){
                log.info("调用[根据用户编码查询用户信息]接口异常!");
            }
        }
        return "";
    }

    @Override
    public ClmsQualityInfo selectQualityInfoWithMaxQiserNo(String reportNo, Short caseTimes) {
        ClmsQualityInfo qualityInfo =clmsQualityInfoMapper.selectQualityInfoWithMaxQiserNo(reportNo, caseTimes);
        return  qualityInfo;
    }

    @Override
    public String selectMaxBatchNoByDate(String batchNoPrefix) {
        return clmsQualityInfoMapper.selectMaxBatchNoByDate(batchNoPrefix);
    }

    @Override
    @Transactional
    public String deleteQualityInfoAndDetailById(String id) {
        // 先查询质检信息
        ClmsQualityInfo qualityInfo = clmsQualityInfoMapper.selectQualityInfoById(id);
        if (qualityInfo == null) {
            return qualityInfo.getReportNo()+"未查询到该质检任务";
        }else{
            String taskStatus = qualityInfo.getTaskStatus();
            if(StringUtils.isNotEmpty(taskStatus)&&!"0".equals(taskStatus)){
                return qualityInfo.getReportNo()+"只能清除任务状态是未处理的质检任务";
            }
        }
        // 删除相关的质检详情（根据serialNo）
        clmsQualityDetailMapper.deleteQualityDetailsBySerialNo(id);
        TaskInfoDTO saskInfoDTO = new TaskInfoDTO();
        saskInfoDTO.setTaskId(id);
        saskInfoDTO.setStatus("0");
        saskInfoDTO.setTaskDefinitionBpmKey("OC_QUALITY_REVIEW");
        taskInfoMapper.deleteQualityTaskInfo(saskInfoDTO);
        // 删除质检信息
        if (clmsQualityInfoMapper.deleteQualityInfoById(id) > 0) {
            return "true";
        }
        return qualityInfo.getReportNo()+"失败";
    }
}
