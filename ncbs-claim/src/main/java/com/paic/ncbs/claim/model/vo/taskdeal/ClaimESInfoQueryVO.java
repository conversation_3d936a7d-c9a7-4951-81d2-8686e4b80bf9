package com.paic.ncbs.claim.model.vo.taskdeal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: justinwu
 * @create 2025/3/4 15:25
 */
@Data
public class ClaimESInfoQueryVO {

    @ApiModelProperty("保单号")
    private String policyNo;
    @ApiModelProperty("标的人客户号")
    private String objectClientCode;
    @ApiModelProperty("标的证件号")
    private String objectCertNo;

    @ApiModelProperty("页码")
    private Integer page;
    @ApiModelProperty("行数")
    private Integer rows;
    @ApiModelProperty("条件拼接方式")
    private String andOr;
}
