package com.paic.ncbs.claim.model.vo.lawsuit;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class ClmsLawsuitAuditCaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 保单号
     */
    private String policyNo;

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Integer caseTimes;

    /**
     * 诉讼案编号
     */
    private String lawsuitNo;

    /**
     * 被保险人
     */
    private String insuredName;

    /**
     * 案件状态
     */
    private String caseStatus;

    /**
     * 庭审类型 1-一审,2-二审,3-再审
     */
    private String hearingType;

    /**
     * 诉讼案由 1-人身保险合同纠纷,2-财产保险合同纠纷,3-责任保险合同纠纷,4-健康险纠纷,5-提供劳务受害责任纠纷,6-其他
     */
    private String lawsuitReason;

    /**
     * 原告人
     */
    private String plaintiff;

    /**
     * 原告人电话
     */
    private String pPhone;

    /**
     * 被告人
     */
    private String defendant;

    /**
     * 被告人电话
     */
    private String dPhone;

    /**
     * 承办律师
     */
    private String lawyer;

    /**
     * 承办律师电话
     */
    private String lPhone;

    /**
     * 受理法院
     */
    private String court;

    /**
     * 案号
     */
    private String courtCaseNo;

    /**
     * 开庭日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate hearingDate;

    /**
     * 收到诉讼材料日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate materialDate;

    /**
     * 接收判决书日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate judgmentDate;

    /**
     * 诉讼/仲裁请求金额
     */
    private BigDecimal applyAmount;

    /**
     * 更新诉讼/仲裁请求金额
     */
    private BigDecimal updateAmount;

    /**
     * 最终调解/判决金额
     */
    private BigDecimal finalAmount;

    /**
     * 对方索赔法律费用
     */
    private BigDecimal opponentFee;

    /**
     * 我方赔偿法律费用
     */
    private BigDecimal ourFee;

    /**
     * 庭审起始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 庭审终止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 书记员
     */
    private String courtClerk;

    /**
     * 庭审法官
     */
    private String judgeName;

    /**
     * 联系电话
     */
    private String judgePhone;

    /**
     * 庭审结果
     */
    private String hearingResult;

    /**
     * 是否庭审前和解
     */
    private String isPreSettle;

    /**
     * 是否上诉/是否申请再审
     */
    private String isAppeal;

    /**
     * 是否已经结束
     */
    private String isFinished;

    /**
     * 是否胜诉
     */
    private String isWon;

    /**
     * 诉讼减损金额
     */
    private BigDecimal lossReduction;

    /**
     * 胜诉/败诉原因
     */
    private String winLoseReason;

    /**
     * 批复人
     */
    private String approver;

    /**
     * 批复人名称
     */
    private String approverName;

    /**
     * 关联诉讼案件ID
     */
    private String lawsuitCaseId;

    /**
     * 提交人
     */
    private String submitter;

    /**
     * 提交人名称
     */
    private String submitterName;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    /**
     * 批复意见 1-批复,2-移交批复
     */
    private String auditOpinion;

    /**
     * 批复机构
     */
    private String auditDepartment;

    /**
     * 批复人代码
     */
    private String auditCode;

    /**
     * 批复人姓名
     */
    private String auditName;

    /**
     * 意见说明
     */
    private String opinionDesc;

    /**
     * 状态 1-待批复,2-已批复待办结,3-办结
     */
    private String status;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sysUtime;


    public String getpPhone() {
        return pPhone;
    }

    public void setpPhone(String pPhone) {
        this.pPhone = pPhone;
    }

    public String getdPhone() {
        return dPhone;
    }

    public void setdPhone(String dPhone) {
        this.dPhone = dPhone;
    }

    public String getlPhone() {
        return lPhone;
    }

    public void setlPhone(String lPhone) {
        this.lPhone = lPhone;
    }
}
