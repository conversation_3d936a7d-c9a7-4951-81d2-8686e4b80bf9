package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.model.dto.report.QueryAccidentVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ReportAccidentMapper extends BaseDao<ReportAccidentEntity> {

	ReportAccidentEntity getReportAccident(String reportNo);

	void insertList(@Param("list") List<ReportAccidentEntity> list);

	/**
	 * 查询已赔付报案次数
	 * @param vo
	 * @return
	 */
	List<String> getAccidentReportInfo(QueryAccidentVo vo);
	/**
	 * 更新出险原因
	 *
	 * @param reportNo
	 * @param accidentCauseLevel1
	 * @param accidentCauseLevel2
	 * @param loginUm
	 */
    void updateAccidentReasonByReportNo(@Param("reportNo") String reportNo, @Param("accidentCauseLevel3") String accidentCauseLevel3, @Param("accidentCauseLevel4") String accidentCauseLevel4, @Param("loginUm") String loginUm);
}