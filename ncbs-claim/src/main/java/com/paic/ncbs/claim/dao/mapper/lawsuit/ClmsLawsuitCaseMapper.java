package com.paic.ncbs.claim.dao.mapper.lawsuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitCase;
import com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO;

import java.util.List;

/**
 * <p>
 * 诉讼案件表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
public interface ClmsLawsuitCaseMapper extends BaseMapper<ClmsLawsuitCase> {
    /**
     * 新增案件详情
     * @param clmsLawsuitCase
     * @return
     */
    void saveClmsLawsuitCase(ClmsLawsuitCase clmsLawsuitCase);

    /**
     * 修改案件详情
     * @param clmsLawsuitCase
     * @return
     */
    void updateClmsLawsuitCase(ClmsLawsuitCase clmsLawsuitCase);

    /**
     * 根据id获取案件详情
     * @param id
     * @return
     */
    ClmsLawsuitAuditCaseVO getLawsuitCaseById(String id);

    /**
     * 根据id删除案件详情
     * @param id
     * @return
     */
    void deleteClmsLawsuitCase(String id);

    /**
     * 根据报案号和赔付次数获取案件信息
     * @param reportNo,caseTimes
     * @return
     */
    ClmsLawsuitCase getClmsLawsuitCase(String reportNo,Integer caseTimes);

    /**
     * 获取案件委托律师
     * @param reportNo,caseTimes
     * @return
     */
    List<Object> getALawyerForYourCase(String reportNo, String caseTimes);

    /**
     * 查询案件下所有诉讼信息
     * @param reportNo,caseTimes
     * @return
     */
    List<ClmsLawsuitAuditCaseVO> getClmsLawsuit(String reportNo,Integer caseTimes);

    /**
     * 获取案件下所有诉讼信息数量
     * @param reportNo,caseTimes
     * @return
     */
    Integer getClmsLawsuitCount(String reportNo,Integer caseTimes);
}
