package com.paic.ncbs.claim.service.lawsuit;

import com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO;

import java.util.List;

/**
 * <p>
 * 诉讼批复表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
public interface ClmsLawsuitAuditService {

    /**
     * 诉讼-诉讼批复
     * @param clmsLawsuitAuditCaseVO
     */
    void litigationApproval(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO);

    /**
     * 诉讼-诉讼结案
     * @param clmsLawsuitAuditCaseVO
     */
    void lawsuitWasConcluded(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO);

    /**
     * 诉讼-查询待诉讼批复信息
     * @param clmsLawsuitAuditCaseVO
     * @return
     */
    List<ClmsLawsuitAuditCaseVO> getClmsLawsuitCase(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO);

    /**
     * 诉讼-查询待诉讼办结信息
     * @param clmsLawsuitAuditCaseVO
     * @return
     */
    List<ClmsLawsuitAuditCaseVO> getClmsLawsuitCaseConcluded(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO);
}
