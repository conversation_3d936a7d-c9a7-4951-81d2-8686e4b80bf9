package com.paic.ncbs.claim.common.constant;

import java.util.*;


public class SettleConst {
	
	private SettleConst() {
		throw new IllegalStateException("Utility class");
	}

    //责任明细分类编码-身故
    public static final String DETAIL_TYPE_DEATH = "01";
    //责任明细分类编码-伤残
    public static final String DETAIL_TYPE_DISABILITY = "02";
	//责任明细分类编码-重疾
    public static final String DETAIL_TYPE_MAJOR_DISEASE = "03";
    //责任明细分类编码-医疗费用
    public static final String DETAIL_TYPE_MEDICAL = "04";
    //责任明细分类编码-津贴
    public static final String DETAIL_TYPE_ALLOWANCE = "05";
    //责任明细分类编码-服务
    public static final String DETAIL_TYPE_SERVICE = "06";
    //责任明细分类编码-一般定额
    public static final String DETAIL_TYPE_QUOTA = "07";
    //责任明细分类编码-其他
    public static final String DETAIL_TYPE_OTHERS = "99";
    //责任明细分类编码-损失
    public static final String DETAIL_TYPE_LOSS = "08";
    //责任明细分类编码-责任
    public static final String DETAIL_TYPE_DUTY = "12";

    public static final Map<String, String> DETAIL_TYPE_NAME = new HashMap<>();
    static {
        DETAIL_TYPE_NAME.put(DETAIL_TYPE_DEATH,"身故");
        DETAIL_TYPE_NAME.put(DETAIL_TYPE_DISABILITY,"残疾");
        DETAIL_TYPE_NAME.put(DETAIL_TYPE_MAJOR_DISEASE,"重大疾病");
        DETAIL_TYPE_NAME.put(DETAIL_TYPE_MEDICAL,"医疗费用");
        DETAIL_TYPE_NAME.put(DETAIL_TYPE_ALLOWANCE,"津贴");
        DETAIL_TYPE_NAME.put(DETAIL_TYPE_SERVICE,"服务");
        DETAIL_TYPE_NAME.put(DETAIL_TYPE_QUOTA,"一般定额");
        DETAIL_TYPE_NAME.put(DETAIL_TYPE_OTHERS,"其他");
        DETAIL_TYPE_NAME.put(DETAIL_TYPE_LOSS, "损失");
        DETAIL_TYPE_NAME.put(DETAIL_TYPE_DUTY, "责任");
    }

    public static final Map<String,String> SETTLE_REASON = new HashMap<>();
    static {
        SETTLE_REASON.put(DETAIL_TYPE_DEATH,"：责任明细保额");
        SETTLE_REASON.put(DETAIL_TYPE_DISABILITY,"：责任明细保额 * 伤残比例");
        SETTLE_REASON.put(DETAIL_TYPE_MAJOR_DISEASE,"：责任明细保额");
        SETTLE_REASON.put(DETAIL_TYPE_MEDICAL,"(合理费用-免赔额)* 赔付比例");
        SETTLE_REASON.put(DETAIL_TYPE_ALLOWANCE,"：（津贴天数-免赔天数）* 津贴日额         ");
        SETTLE_REASON.put(DETAIL_TYPE_SERVICE,"");
        SETTLE_REASON.put(DETAIL_TYPE_QUOTA,"：责任明细保额");
        SETTLE_REASON.put(DETAIL_TYPE_OTHERS,"");
        SETTLE_REASON.put(DETAIL_TYPE_LOSS, "：损失额");
        SETTLE_REASON.put(DETAIL_TYPE_DUTY, "：责任明细保额");
    }
    //责任明细分类->案件类别 映射
    public static final Map<String, List<String>> DETAIL_TYPE_CASE_CLASS_MAP = new HashMap<>();
    static {
        //身故-> 意外身故、疾病身故 01
        DETAIL_TYPE_CASE_CLASS_MAP.put(DETAIL_TYPE_DEATH,Arrays.asList("IAT_1_01","IAT_1_02"));
        //残疾-> 意外残疾、疾病残疾 02
        DETAIL_TYPE_CASE_CLASS_MAP.put(DETAIL_TYPE_DISABILITY,Arrays.asList("IAT_1_03","IAT_1_04"));
        //重疾-> 重大疾病 03
        DETAIL_TYPE_CASE_CLASS_MAP.put(DETAIL_TYPE_MAJOR_DISEASE, Collections.singletonList("IAT_1_08"));
        //医疗-> 意外医疗、疾病住院医疗、疾病门诊医疗、意外津贴、疾病津贴 04
        DETAIL_TYPE_CASE_CLASS_MAP.put(DETAIL_TYPE_MEDICAL,Arrays.asList("IAT_1_05","IAT_1_06","IAT_1_07","IAT_1_09","IAT_1_11"));
        //津贴-> 意外医疗、疾病住院医疗、疾病门诊医疗、意外津贴、疾病津贴 05
        DETAIL_TYPE_CASE_CLASS_MAP.put(DETAIL_TYPE_ALLOWANCE,Arrays.asList("IAT_1_05","IAT_1_06","IAT_1_07","IAT_1_09","IAT_1_11"));
        //其他-> 其他 99
        DETAIL_TYPE_CASE_CLASS_MAP.put(DETAIL_TYPE_OTHERS,Arrays.asList("IAT_1_10","24"));
        //一般定额-> 其他 07
        DETAIL_TYPE_CASE_CLASS_MAP.put(DETAIL_TYPE_QUOTA, Collections.singletonList("IAT_1_10"));
        // 损失 -> 人身伤亡、财产损失、法律责任及其他、物质损失、现金损失、给付型津贴、延误类、救援类 08
        DETAIL_TYPE_CASE_CLASS_MAP.put(DETAIL_TYPE_LOSS, Arrays.asList("IAT_2_01", "IAT_2_02", "IAT_2_03", "IAT_3_01", "IAT_3_02", "IAT_4_01", "IAT_4_02", "IAT_4_03","24"));
        // 责任 -> 一级分类“责任险”下的“人身伤亡”、“财产损失”、“法律责任及其他”任一二级分类 12
        DETAIL_TYPE_CASE_CLASS_MAP.put(DETAIL_TYPE_DUTY, Arrays.asList("IAT_2_01", "IAT_2_02", "IAT_2_03"));
    }



    //BENC_01	一般住院津贴
    public static final String BENEFIT_CODE_01 = "BENC_01";
    //BENC_03	隔离津贴
    public static final String BENEFIT_CODE_03 = "BENC_03";
    //BENC_04	意外伤害住院津贴
    public static final String BENEFIT_CODE_04 = "BENC_04";
    //BENC_05	癌症住院津贴
    public static final String BENEFIT_CODE_05 = "BENC_05";
    //BENC_06	误工津贴
    public static final String BENEFIT_CODE_06 = "BENC_06";
    //BENC_07	手术住院津贴
    public static final String BENEFIT_CODE_07 = "BENC_07";
    //BENC_08	其他
    public static final String BENEFIT_CODE_08 = "BENC_08";

    public static final Map<String,String> BENEFIT_MAPPING_MAP = new HashMap<>();
    static {
        BENEFIT_MAPPING_MAP.put(BENEFIT_CODE_01,"一般住院津贴");
        BENEFIT_MAPPING_MAP.put(BENEFIT_CODE_03,"隔离津贴");
        BENEFIT_MAPPING_MAP.put(BENEFIT_CODE_04,"意外伤害住院津贴");
        BENEFIT_MAPPING_MAP.put(BENEFIT_CODE_05,"癌症住院津贴");
        BENEFIT_MAPPING_MAP.put(BENEFIT_CODE_06,"误工津贴");
        BENEFIT_MAPPING_MAP.put(BENEFIT_CODE_07,"手术住院津贴");
        BENEFIT_MAPPING_MAP.put(BENEFIT_CODE_08,"其他");
    }

	
	 
	public static final String USER_ID = "userId";


	public static final String CLAIM_TYPE_PAY = "1";
	public static final String CLAIM_TYPE_PRE_PAY = "2";
    /**
     * 追偿
     */
    public static final String CLAIM_TYPE_REP_PAY = "4";
	public static final String PAYMENT_TYPE_DECREASE_FEE = "107";


    public static final String PAYMENT_TYPE_PAY = "13";
    /**
     * 直接理赔费用
     */
    public static final String PAYMENT_TYPE_FEE = "1J";
    public static final String PAYMENT_TYPE_REINSURE_RECEIVE_PAY = "C13";
    public static final String PAYMENT_TYPE_REINSURE_RECEIVE_FEE = "C1J";

    public static final String PAYMENT_TYPE_PREPAY = "11";
    public static final String PAYMENT_TYPE_FEE_PREPAY = "11J";
    public static final String PAYMENT_TYPE_REINSURE_PAY = "P13";
    public static final String PAYMENT_TYPE_REINSURE_FEE = "P1J";
    /**
     * 追偿
     */
    public static final String PAYMENT_TYPE_REPLEVY_PAY = "1H";
    public static final String PAYMENT_TYPE_REPLEVY_FEE = "1HJ";

	public static final String PAYMENT_TYPE_PREPAY_FEE_NAME="预赔费用";
	public static final String PAYMENT_TYPE_PREPAY_PAY_NAME="预赔赔款";

	 
	public static final String PAYMENT_TYPE_FEE_NAME = "结案费用";
	public static final String PAYMENT_TYPE_PAY_NAME = "结案赔款";
	
	 
	public static final Set<String> COINSURE_PAYMENT_TYPE_SET = new HashSet<>();
	static {
		COINSURE_PAYMENT_TYPE_SET.add(PAYMENT_TYPE_REINSURE_PAY);
		COINSURE_PAYMENT_TYPE_SET.add(PAYMENT_TYPE_REINSURE_FEE);
		COINSURE_PAYMENT_TYPE_SET.add(PAYMENT_TYPE_REINSURE_RECEIVE_PAY);
		COINSURE_PAYMENT_TYPE_SET.add(PAYMENT_TYPE_REINSURE_RECEIVE_FEE);
		
	}

	//正常赔付
	public static final String INDEMNITY_MODE_PAY = "1";
    //拒赔
	public static final String INDEMNITY_MODE_DENIED = "4";
    /**
     * 协议赔付
     */
	public static final String INDEMNITY_MODE_PROTOCOL = "15";
    /**
     * 通融赔付
     */
	public static final String INDEMNITY_MODE_ACCOMMODATE= "16";

	public static final String MIGRATE_FROM_NH = "na";


	 
	public static final String BATCH_SETTLE_TYPE_WHOLE_CASE = "01";

    /**
     * 理算状态 0-待处理，1-处理中，2-已处理，3-退回
     */
	public static final String SETTLE_STATUS_AWAIT = "0";
	public static final String SETTLE_STATUS_ON = "1";
	public static final String SETTLE_STATUS_DONE = "2";
	public static final String SETTLE_STATUS_ROLLBACK = "3";

	public static final String VERIFY_PASS = "1";
	
	 
	public static final String VERIFY_MODIFY_PASS = "2";
	
	 
	public static final String SETTLE_AGAIN = "3";
	

	public static final String NOT_MERGE = "01";
	
	public static final String PREPARE_MERGE = "02";
	
	 
	public static final String RMB = "01";
        public static final Map<String,String> CURRENCY_MAP = new HashMap<>();
    static {
        CURRENCY_MAP.put(RMB,"CNY");
    }
	 
	public static final String COLLECTION = "1";
	
	 
	public static final String COLLECT_SIGN = "0";
	
	 
	public static final String PAYMENTITEM_STATUS_BILL = "30";
	
	 
	public static final String COMPANY_COUNTER = "01";
	public static final String REAL_TIME_PAY = "02";
	public static final String BANK_TRANSFER = "03";
	public static final String APPROACH_NAME_COMPANY_COUNTER = "出纳柜面";

    public static final String ACCIDENT_OUTPATIENT = "4";
     
    public static final String ACCIDENT_HOSPITAL = "5";
     
    public static final String ACCIDENT_ALLOWANCE = "6";

    public static final String ACCIDENT_DEATH = "7";
     
    public static final String ACCIDENT_DISABILITY = "8";
     
    public static final String DISEASE_OUTPATIENT = "9";
     
    public static final String DISEASE_HOSPITAL = "10";
     
    public static final String DISEASE_ALLOWANCE = "11";
     
    public static final String DISEASE_DEATH = "12";
     
    public static final String DISEASE_DISABILITY = "13";
     
    public static final String BIG_DISEASE = "14";
     
    public static final String OTHER_PERSON = "15";
    
     
    public static final String FLIGHT_DELAY = "16";
    
     
    public static final String PROPERTY_LOSS = "17";
    
     
    public static final String TRAVLE_CHANGE = "18";
    
     
    public static final String BAGGAGE_DELAY = "19";
    
     
    public static final String TRAFIC_DELAY = "20";
    
     
    public static final String EXAM_NOT_PASS = "21";
    
     
    public static final String OTHER_NO_HURT = "22";




    public static final Set<String> MEDICAL_CASE_SET = new HashSet<>();
    static{
    	MEDICAL_CASE_SET.add(ACCIDENT_OUTPATIENT);
    	MEDICAL_CASE_SET.add(ACCIDENT_HOSPITAL);
    	MEDICAL_CASE_SET.add(DISEASE_OUTPATIENT);
    	MEDICAL_CASE_SET.add(DISEASE_HOSPITAL);
    }
     
    public static final Set<String> DISABLE_CASE_SET = new HashSet<>();
    static{
    	DISABLE_CASE_SET.add(ACCIDENT_DISABILITY);
    	DISABLE_CASE_SET.add(DISEASE_DISABILITY);
    }
     
    public static final Set<String> DIE_CASE_SET = new HashSet<>();
    static{
    	DIE_CASE_SET.add(ACCIDENT_DEATH);
    	DIE_CASE_SET.add(DISEASE_DEATH);
    }
    
     
    public static final Set<String> PROPERTY_LOSS_CASE_SET = new HashSet<>();
    static{
    	PROPERTY_LOSS_CASE_SET.add(PROPERTY_LOSS);
    }
	
	 
	public static final String SETTLE_FEE_TYPE_ARBITRAGE = "FEE_01"; //'仲裁费'
	 
	public static final String SETTLE_FEE_TYPE_LAWSUIT = "FEE_02"; //'诉讼费'
	 
	public static final String SETTLE_FEE_TYPE_COMMON_ESTIMATE = "FEE_03"; //'公估费'
	 
	public static final String SETTLE_FEE_TYPE_LAWYER = "FEE_04"; //'律师费'
	 
	public static final String SETTLE_FEE_TYPE_EXECUTE = "FEE_05"; //'执行费'
	 
	public static final String SETTLE_FEE_TYPE_VERIFY = "FEE_06";//'检验及鉴定费'

    // 去除奖励费
    // public static final String SETTLE_FEE_TYPE_AWARD = "FEE_07";

    public static final String SETTLE_INQUIRE = "FEE_08";//'调查费'

    public static final String SETTLE_OTHER = "FEE_09";//'其他'

    public static final String SETTLE_SPECIAL = "FEE_07";//'专项查勘费'

	 
	public static final String INSURED_CONFIG_MEDICAL = "IS_0101";
	 
	public static final String INSURED_CONFIG_FATAL_DISEASE = "IS_0102";
	 
	public static final String INSURED_CONFIG_DISABLED = "IS_0103";
	 
	public static final String INSURED_CONFIG_DIED = "IS_0104";
	 
	public static final String INSURED_CONFIG_ALLOWANCE = "IS_0105";
	 
	public static final String INSURED_CONFIG_OTHER_PERSONAL_DAMAGE = "IS_0106";
	 
	public static final String INSURED_CONFIG_RESCUE = "IS_0107";
	 
	public static final String INSURED_CONFIG_FLIGHT_DELAYED = "IS_0108";
	
	 
	public static final String INSURED_CONFIG_BAGGAGE_DELAY = "IS_0109";
	
	 
	public static final String INSURED_CONFIG_TRAVEL_CHANGE = "IS_0110";
	
	 
	public static final String INSURED_CONFIG_PROPERTY_LOSS = "IS_0111";
	
	 
	public static final String INSURED_CONFIG_EXAM_NOT_PASS = "IS_0112";
	
	 
	public static final String INSURED_CONFIG_OTHER_NO_HURT = "IS_0113";
	
	 
	public static final String INSURED_CONFIG_DIED_DISABLED = "IS_0114";
	
	 
	public static final String INSURED_CONFIG_DIED_MEDICAL = "IS_0115";
	
	 
	public static final String INSURED_CONFIG_DIED_MEDICAL_LOSS = "IS_0116";
	
	 
	public static final String INSURED_CONFIG_TRAFIC_DELAY = "IS_0117";
	
	 
	public static final String INSURED_CONFIG_PORT_CHANGE = "IS_0118";
	
	 
	public static final String INSURED_CONFIG_NULL = "IS_NULL";

	 
	public static final String INSURED_CONFIG_PET_MEDICAL = "IS_0119";
     
    public static final Set<String> DIE_DETAIL_ELEMENT1_SET = new HashSet<>();
    static{
    	DIE_DETAIL_ELEMENT1_SET.add(INSURED_CONFIG_DIED_DISABLED);
    	DIE_DETAIL_ELEMENT1_SET.add(INSURED_CONFIG_DIED_MEDICAL);
    	DIE_DETAIL_ELEMENT1_SET.add(INSURED_CONFIG_DIED_MEDICAL_LOSS);
    	DIE_DETAIL_ELEMENT1_SET.add(INSURED_CONFIG_DIED);
    }

	public static final String MEDICAL_GROUP = "medicalGroup";

	public static final String DISEASE_GROUP = "diseaseGroup";

	public static final String DISABLE_GROUP = "DisableGroup";

	public static final String DIE_GROUP = "dieGroup";

	public static final String ALLOWANCE_GROUP = "allowanceGroup";

	public static final String PERSON_HURT_GROUP = "personhurtGroup";

	public static final String RESCUE_GROUP = "rescueGroup";
	 
	public static final String FLIGHT_DELAYED_GROUP = "flightDelayedGroup";
	

	public static final String BAGGAGE_DELAY_GROUP = "baggageDelayGroup";
	
	 
	public static final String TRAVEL_CHANGE_GROUP = "travelChangeGroup";
	
	 
	public static final String PROPERTY_LOSS_GROUP = "propertyLossGroup";
	
	 
	public static final String EXAM_NOT_PASS_GROUP = "examNotPassGroup";
	
	 
	public static final String OTHER_NO_HURT_GROUP = "otherNoHurtGroup";
	
	 
	public static final String TRAFIC_DELAY_GROUP = "traficDelayGroup";
	
	 
	public static final String PORT_CHANGE_GROUP = "portChangeGroup";
	
	 
	public static final String DIE_DISABLED_MEDICAL_GROUP = "dieDisabledMedicalGroup";
	
	 
	public static final String DIE_DISABLED_MEDICAL_PROPERTY_GROUP = "dieDisabledMedicalPropertyGroup";
	
	 
	public static final String CONFIG_NULL_GROUP = "configNullGroup";
	
	 
	public static final String INSURED_CONFIG_DIED_DISABLED_GROUP = "diedDisabledGroup";
	 
	public static final String PET_MEDICAL_GROUP = "petMedicalGroup";


	 
	public static final Map<String, String> DETAIL_GROUP_MAP = new HashMap<String, String>();
    static {
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_MEDICAL,MEDICAL_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_FATAL_DISEASE,DISEASE_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_DISABLED,DISABLE_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_DIED,DIE_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_ALLOWANCE,ALLOWANCE_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_OTHER_PERSONAL_DAMAGE,PERSON_HURT_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_RESCUE,RESCUE_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_FLIGHT_DELAYED,FLIGHT_DELAYED_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_BAGGAGE_DELAY,BAGGAGE_DELAY_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_TRAVEL_CHANGE,TRAVEL_CHANGE_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_PROPERTY_LOSS,PROPERTY_LOSS_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_EXAM_NOT_PASS,EXAM_NOT_PASS_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_OTHER_NO_HURT,OTHER_NO_HURT_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_TRAFIC_DELAY,TRAFIC_DELAY_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_PORT_CHANGE,PORT_CHANGE_GROUP);
       	DETAIL_GROUP_MAP.put(INSURED_CONFIG_DIED_MEDICAL,DIE_DISABLED_MEDICAL_GROUP);
       	DETAIL_GROUP_MAP.put(INSURED_CONFIG_DIED_MEDICAL_LOSS,DIE_DISABLED_MEDICAL_PROPERTY_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_NULL,CONFIG_NULL_GROUP);
    	DETAIL_GROUP_MAP.put(INSURED_CONFIG_DIED_DISABLED,INSURED_CONFIG_DIED_DISABLED_GROUP);
		DETAIL_GROUP_MAP.put(INSURED_CONFIG_PET_MEDICAL,PET_MEDICAL_GROUP);
    }
	
	 
    public static final Map<String, String> CALCULATE_SERVICE_NAME_MAP = new HashMap<String, String>();
    static {
    	CALCULATE_SERVICE_NAME_MAP.put(MEDICAL_GROUP, "medicalCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(DISEASE_GROUP, "diseaseCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(DISABLE_GROUP, "disabledCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(DIE_GROUP, "dieCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(ALLOWANCE_GROUP, "allowanceCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(PERSON_HURT_GROUP, "personHurtCaculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(RESCUE_GROUP, "rescueCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(FLIGHT_DELAYED_GROUP, "flightDelayCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(BAGGAGE_DELAY_GROUP, "baggageDelayCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(TRAVEL_CHANGE_GROUP, "travelChangeCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(PROPERTY_LOSS_GROUP, "propertyLossCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(EXAM_NOT_PASS_GROUP, "examNotPassCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(OTHER_NO_HURT_GROUP, "otherNoHurtCaculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(TRAFIC_DELAY_GROUP, "traficDelayCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(PORT_CHANGE_GROUP, "portChangeCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(DIE_DISABLED_MEDICAL_GROUP, "dieDisabledMedicalService");
    	CALCULATE_SERVICE_NAME_MAP.put(DIE_DISABLED_MEDICAL_PROPERTY_GROUP, "dieDisabledMedicalPropertyService");
    	CALCULATE_SERVICE_NAME_MAP.put(CONFIG_NULL_GROUP, "commonCalculateService");
    	CALCULATE_SERVICE_NAME_MAP.put(INSURED_CONFIG_DIED_DISABLED_GROUP, "dieDisabledService");
    	CALCULATE_SERVICE_NAME_MAP.put(PET_MEDICAL_GROUP, "petMedicalService");
    }
     
    public static final Set<String> SPECIAL_CASE_CONCLUSION_SET = new HashSet<String>();
    static {
    	SPECIAL_CASE_CONCLUSION_SET.add(INDEMNITY_MODE_DENIED);
    	SPECIAL_CASE_CONCLUSION_SET.add(INDEMNITY_MODE_PROTOCOL);
    	SPECIAL_CASE_CONCLUSION_SET.add(INDEMNITY_MODE_ACCOMMODATE);
    }
   
     
    public static final String PLAN_ELEMENT_009 = "APE_0001";
    
     
    public static final String ELEMENT1_058 = "element1_058";
    
     
    public static final String ELEMENT1_059 = "element1_059";
    
     
    public static final String ELEMENT1_060 = "element1_060";
    
     
    public static final String ELEMENT1_061 = "element1_061";
    
     
    public static final String ELEMENT1_062 = "element1_062";
    
     
    public static final String ELEMENT1_063 = "element1_063";
    
     
    public static final String ELEMENT1_000 = "element1_000";
    
     
    public static final String ELEMENT1_008 = "element1_008";
    
     
    public static final String ELEMENT1_010 = "element1_010";
    
     
    public static final String ELEMENT1_007 = "element1_007";
    
     
    public static final String ELEMENT1_009 = "element1_009";
    
     
    public static final String ELEMENT1_064 = "element1_064";
    
     
    public static final String ELEMENT1_065 = "element1_065";
     
    public static final String ELEMENT1_068 = "element1_068";
     
    public static final String ELEMENT1_069 = "element1_069";
     
    public static final String ELEMENT2_049 = "element2_049";
    
     
    public static final String ELEMENT2_051 = "element2_051";
    
     
    public static final String ELEMENT2_052 = "element2_052";
    
     
    public static final String ELEMENT2_053 = "element2_053";
    
     
    public static final String ELEMENT2_054 = "element2_054";
    
     
    public static final String ELEMENT2_024 = "element2_024";
    
     
    public static final String ELEMENT2_025 = "element2_025";
    
     
    public static final String ELEMENT2_026 = "element2_026";
    
     
    public static final String ELEMENT2_000 = "element2_000";
    
     
    public static final String ELEMENT2_019 = "element2_019";
    
     
    public static final String ELEMENT2_017 = "element2_017";
    
     
    public static final String ELEMENT2_021 = "element2_021";

     
    public static final String ELEMENT2_022 = "element2_022";
    
     
    public static final String ELEMENT2_023 = "element2_023";
    
     
    public static final String ELEMENT2_020 = "element2_020";
    
     
    public static final String ELEMENT2_018 = "element2_018";
    
     
    public static final String ELEMENT2_016 = "element2_016";
    
     
    public static final String ELEMENT2_015 = "element2_015";
    
     
    public static final String ELEMENT2_008 = "element2_008";
    
     
    public static final String ELEMENT2_001 = "element2_001";
    
     
    public static final String ELEMENT1_041 = "element1_041";
    
     
    public static final String ELEMENT2_006 = "element2_006";
    
     
    public static final String ELEMENT2_002 = "element2_002";
    
     
    public static final String ELEMENT2_003 = "element2_003";
    
     
    public static final String ELEMENT2_012 = "element2_012";
    
     
    public static final String ELEMENT2_004 = "element2_004";
    
     
    public static final String ELEMENT2_005 = "element2_005";
    
     
    public static final String ELEMENT2_007 = "element2_007";
    
     
    public static final String ELEMENT2_009 = "element2_009";
    
     
    public static final String ELEMENT2_010 = "element2_010";

     
    public static final String ELEMENT2_011 = "element2_011";
    
     
    public static final String ELEMENT2_050 = "element2_050";
    
     
    public static final String ELEMENT2_032 = "element2_032";
    
     
    public static final String ELEMENT2_034 = "element2_034";
    
     
    public static final String ELEMENT2_035 = "element2_035";
    
     
    public static final String ELEMENT2_033 = "element2_033";
    
     
    public static final String ELEMENT2_037 = "element2_037";
    
     
    public static final String ELEMENT2_038 = "element2_038";
    
     
    public static final String ELEMENT2_036 = "element2_036";
     
    public static final String ELEMENT2_061 = "element2_061";   
     
    public static final String ELEMENT2_062 = "element2_062"; 
     
    public static final String ELEMENT2_063 = "element2_063"; 
     
    public static final String ELEMENT2_064 = "element2_064";
    
    
    
    
     
    public static final String TR_ACC_TYPE_01 = "TR_ACC_TYPE_01";
    
     
    public static final String TR_ACC_TYPE_02 = "TR_ACC_TYPE_02";
    
     
    public static final String TR_ACC_TYPE_03 = "TR_ACC_TYPE_03";
    
     
    public static final String TR_ACC_TYPE_04 = "TR_ACC_TYPE_04";
    
     
    public static final String TR_ACC_TYPE_05 = "TR_ACC_TYPE_05";
    
     
    public static final String TR_ACC_TYPE_06 = "TR_ACC_TYPE_06";
    
     
    public static final String TR_ACC_TYPE_07 = "TR_ACC_TYPE_07";
    
     
    public static final String TR_ACC_TYPE_08 = "TR_ACC_TYPE_08";
     
    public static final String CHECKLOSS_OTHER2_TRAVELCHANGE ="TR_ACC_TYPE_09";

	 
	public static final String TR_ACC_TYPE_10 = "TR_ACC_TYPE_10";

     
    public static final String PR_ACC_TYPE_01 = "PR_ACC_TYPE_01";
    
     
    public static final String PR_ACC_TYPE_02 = "PR_ACC_TYPE_02";
    
     
    public static final String PR_ACC_TYPE_03 = "PR_ACC_TYPE_03";
    
     
    public static final String PR_ACC_TYPE_04 = "PR_ACC_TYPE_04";
    
     
    public static final String PR_ACC_TYPE_05 = "PR_ACC_TYPE_05";
    
     
    public static final String PR_ACC_TYPE_06 = "PR_ACC_TYPE_06";

     
    public static final String PR_ACC_TYPE_07 = "PR_ACC_TYPE_07";
    
     
    public static final String PR_ACC_TYPE_08 = "PR_ACC_TYPE_08";
    
     
    public static final String PR_ACC_TYPE_09 = "PR_ACC_TYPE_09";
    
     
    public static final String PR_ACC_TYPE_10 = "PR_ACC_TYPE_10";
    
     
    public static final String PR_ACC_TYPE_11 = "PR_ACC_TYPE_11";
    
     
    public static final String PR_ACC_TYPE_12 = "PR_ACC_TYPE_12";
    
     
    public static final String PR_ACC_TYPE_13 = "PR_ACC_TYPE_13";
     
    public static final String PR_ACC_TYPE_14 = "PR_ACC_TYPE_14";
     
    public static final String PR_ACC_TYPE_15 = "PR_ACC_TYPE_15";
     
    public static final String PR_ACC_TYPE_16 = "PR_ACC_TYPE_16";
     
    public static final String PR_ACC_TYPE_17 = "PR_ACC_TYPE_17";

     
    public static final String ONCT_01 = "ONCT_01";
    
     
    public static final String ONCT_02 = "ONCT_02";
    
     
    public static final String ONCT_03 = "ONCT_03";
    
     
    public static final String ONCT_04 = "ONCT_04";
    
     
    public static final String ONCT_05 = "ONCT_05";
    
     
    public static final String PREPAID_TYPE_2701 = "PT_2701";
    
     
    public static final String PREPAID_TYPE_2702 = "PT_2702";
    
     
    public static final String PREPAID_TYPE_2703 = "PT_2703";
    
     
    public static final String PREPAID_TYPE_2704 = "PT_2704";
    
     
    public static final String PREPAID_TYPE_2705 = "PT_2705";
    
     
    public static final String PREPAID_TYPE_2706 = "PT_2706";
    
     
    public static final String EXAMIN_01 = "EXAMIN_01";

     
    public static final String EXAMIN_02 = "EXAMIN_02";


	 
	public static final String EXAMIN_03 = "EXAMIN_03";

	 
	public static final String EXAMIN_04 = "EXAMIN_04";


    
     
    public static final Set<String> TAVERN_CANCLE_SET = new HashSet<>();
    static {
    	TAVERN_CANCLE_SET.add(ELEMENT2_019);
    }
    
     
    public static final Set<String> TTAVEL_CANCLE_SET = new HashSet<>();
    static {
    	TTAVEL_CANCLE_SET.add(ELEMENT2_017);
    }
    
     
    public static final Set<String> TTAVEL_CUT_SET = new HashSet<>();
    static {
    	TTAVEL_CUT_SET.add(ELEMENT2_021);
    	TTAVEL_CUT_SET.add(ELEMENT2_022);
    	TTAVEL_CUT_SET.add(ELEMENT2_023);
    }
    
     
    public static final Set<String> TTAVEL_SUSPEND_SET = new HashSet<>();
    static {
    	TTAVEL_SUSPEND_SET.add(ELEMENT2_022);
    	TTAVEL_SUSPEND_SET.add(ELEMENT2_023);
    }
    
     
    public static final Set<String> TTAVEL_DELAY_SET = new HashSet<>();
    static {
    	TTAVEL_DELAY_SET.add(ELEMENT2_023);
    	TTAVEL_DELAY_SET.add(ELEMENT2_020);
    }
    
     
    public static final Set<String> VISA_REFUSE_SET = new HashSet<>();
    static {
    	VISA_REFUSE_SET.add(ELEMENT2_018);
    }
    
     
    public static final Set<String> FLIGHT_CANCLE_SET = new HashSet<>();
    static {
    	FLIGHT_CANCLE_SET.add(ELEMENT2_016);
    }
    
     
    public static final Set<String> TRAIN_REFUND_SET = new HashSet<>();
    static {
    	TRAIN_REFUND_SET.add(ELEMENT2_015);
    }
    
    public static final Set<String> OTHER2_SET = new HashSet<>();
    static {
    	OTHER2_SET.add(ELEMENT2_000);
    }
    
     
    public static final Map<String,Set<String>> TRAVLE_MAPPING_MAP = new HashMap<>();
    static {
    	TRAVLE_MAPPING_MAP.put(TR_ACC_TYPE_01, TAVERN_CANCLE_SET);
    	TRAVLE_MAPPING_MAP.put(TR_ACC_TYPE_02, TTAVEL_CANCLE_SET);
    	TRAVLE_MAPPING_MAP.put(TR_ACC_TYPE_03, TTAVEL_CUT_SET);
    	TRAVLE_MAPPING_MAP.put(TR_ACC_TYPE_04, TTAVEL_SUSPEND_SET);
    	TRAVLE_MAPPING_MAP.put(TR_ACC_TYPE_05, TTAVEL_DELAY_SET);
    	TRAVLE_MAPPING_MAP.put(TR_ACC_TYPE_06, VISA_REFUSE_SET);
    	TRAVLE_MAPPING_MAP.put(TR_ACC_TYPE_07, FLIGHT_CANCLE_SET);
    	TRAVLE_MAPPING_MAP.put(TR_ACC_TYPE_08, TRAIN_REFUND_SET);
    	TRAVLE_MAPPING_MAP.put(CHECKLOSS_OTHER2_TRAVELCHANGE, OTHER2_SET);
    }
    
     
    public static final Set<String> INDOOR_PROPERTY_SET = new HashSet<>();
    static {
    	INDOOR_PROPERTY_SET.add(ELEMENT2_008);
    }
    
     
    public static final Set<String> AUTOMOTIVE_SET = new HashSet<>();
    static {
    	AUTOMOTIVE_SET.add(ELEMENT2_001);
    	AUTOMOTIVE_SET.add(ELEMENT1_041);
    }
    
     
    public static final Set<String> NO_AUTOMOTIVE_SET = new HashSet<>();
    static {
    	NO_AUTOMOTIVE_SET.add(ELEMENT2_006);
    }
    
     
    public static final Set<String> BAGGAGE_CONSIGN_SET = new HashSet<>();
    static {
    	BAGGAGE_CONSIGN_SET.add(ELEMENT2_002);
    }
    
     
    public static final Set<String> BAGGAGE_CARRY_SET = new HashSet<>();
    static {
    	BAGGAGE_CARRY_SET.add(ELEMENT2_003);
    	BAGGAGE_CARRY_SET.add(ELEMENT2_012);
    }
    
    
     
    public static final Set<String> TRAVLE_CERTIFICATE_SET = new HashSet<>();
    static {
    	TRAVLE_CERTIFICATE_SET.add(ELEMENT2_012);
    	TRAVLE_CERTIFICATE_SET.add(ELEMENT2_004);
    }
    
     
    public static final Set<String> TICKET_LOSS_SET = new HashSet<>();
    static {
    	TICKET_LOSS_SET.add(ELEMENT2_005);
    }
    
     
    public static final Set<String> TICKET_CANCLE_SET = new HashSet<>();
    static {
    	TICKET_CANCLE_SET.add(ELEMENT2_007);
    }
    
     
    public static final Set<String> HOUSE_DECORATION_SET = new HashSet<>();
    static {
    	HOUSE_DECORATION_SET.add(ELEMENT2_009);
    }
    
     
    public static final Set<String> HOUSE_BUILD_SET = new HashSet<>();
    static {
    	HOUSE_BUILD_SET.add(ELEMENT2_010);
    }
    
     
    public static final Set<String> CAR_CANCLE_SET = new HashSet<>();
    static {
    	CAR_CANCLE_SET.add(ELEMENT2_011);
    }
    
     
    public static final Set<String> THIRD_PROPERTY_LOSS_SET = new HashSet<>();
    static {
    	THIRD_PROPERTY_LOSS_SET.add(ELEMENT1_065);
    }
     
    public static final Set<String> CRACKED_SCREEN_SET = new HashSet<>();
    static {
    	CRACKED_SCREEN_SET.add(ELEMENT1_068);
    	CRACKED_SCREEN_SET.add(ELEMENT2_061);
    }
     
    public static final Set<String> CARD_PIRACY_SET = new HashSet<>();
    static {
    	CARD_PIRACY_SET.add(ELEMENT1_069);
    	CARD_PIRACY_SET.add(ELEMENT2_062);
    }
     
    public static final Set<String> ELECTRONIC_DEVICE_SET = new HashSet<>();
    static {
    	ELECTRONIC_DEVICE_SET.add(ELEMENT2_063);
    }
     
    public static final Set<String> CASH_JEWELRY_SET = new HashSet<>();
    static {
    	CASH_JEWELRY_SET.add(ELEMENT2_064);
    }
     
    public static final Set<String> OTHER_SET = new HashSet<>();
    static {
    	OTHER_SET.add(ELEMENT2_000);
    	OTHER_SET.add(ELEMENT1_000);
    }
    
     
    public static final Set<String> OTHER1_SET = new HashSet<>();
    static {
    	OTHER1_SET.add(ELEMENT1_000);
    }
    
     
    public static final Map<String,Set<String>> PROPERTY_LOSS_MAPPING_MAP = new HashMap<>();
    static {
    	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_01, INDOOR_PROPERTY_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_02, AUTOMOTIVE_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_03, NO_AUTOMOTIVE_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_04, BAGGAGE_CONSIGN_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_05, BAGGAGE_CARRY_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_06, TRAVLE_CERTIFICATE_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_07, TICKET_LOSS_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_08, TICKET_CANCLE_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_09, HOUSE_DECORATION_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_10, HOUSE_BUILD_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_11, CAR_CANCLE_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_12, OTHER_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_13, THIRD_PROPERTY_LOSS_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_14, CRACKED_SCREEN_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_15, CARD_PIRACY_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_16, ELECTRONIC_DEVICE_SET);
      	PROPERTY_LOSS_MAPPING_MAP.put(PR_ACC_TYPE_17, CASH_JEWELRY_SET);
    }
    
     
    public static final Set<String> AMBULANCE_FEE_SET = new HashSet<>();
    static {
    	AMBULANCE_FEE_SET.add(ELEMENT1_008);
    }
    
     
    public static final Set<String> SURGERY_COMPLICATION_SET = new HashSet<>();
    static {
    	SURGERY_COMPLICATION_SET.add(ELEMENT1_010);
    }
    
     
    public static final Set<String> GRAVIDA_CHECK_FEE_SET = new HashSet<>();
    static {
    	GRAVIDA_CHECK_FEE_SET.add(ELEMENT1_007);
    }
    
     
    public static final Set<String> SPORT_DISABILITY_SET = new HashSet<>();
    static {
    	SPORT_DISABILITY_SET.add(ELEMENT1_009);
    }
    
     
    public static final Map<String,Set<String>> PERSON_HURT_MAPPING_MAP = new HashMap<>();
    static {
    	PERSON_HURT_MAPPING_MAP.put(ONCT_01, AMBULANCE_FEE_SET);
    	PERSON_HURT_MAPPING_MAP.put(ONCT_02, SURGERY_COMPLICATION_SET);
    	PERSON_HURT_MAPPING_MAP.put(ONCT_03, GRAVIDA_CHECK_FEE_SET);
    	PERSON_HURT_MAPPING_MAP.put(ONCT_04, SPORT_DISABILITY_SET);
    	PERSON_HURT_MAPPING_MAP.put(ONCT_05, OTHER1_SET);
    }

    public static final Set<String> SPECIAL_DUTY_ELEMENT_SET = new HashSet<String>();
    static {
    	SPECIAL_DUTY_ELEMENT_SET.add(ELEMENT2_049);
    	SPECIAL_DUTY_ELEMENT_SET.add(ELEMENT1_058);
    	SPECIAL_DUTY_ELEMENT_SET.add(ELEMENT1_059);
    	SPECIAL_DUTY_ELEMENT_SET.add(ELEMENT1_060);
    	SPECIAL_DUTY_ELEMENT_SET.add(ELEMENT1_061);
    	SPECIAL_DUTY_ELEMENT_SET.add(ELEMENT1_062);
    	SPECIAL_DUTY_ELEMENT_SET.add(ELEMENT1_063);
    }
    
     
    public static final Set<String> SPECIAL_DUTY_ATTRIBUTE_SET = new HashSet<String>();
    static {
    	SPECIAL_DUTY_ATTRIBUTE_SET.add(INSURED_CONFIG_OTHER_PERSONAL_DAMAGE);
    	SPECIAL_DUTY_ATTRIBUTE_SET.add(INSURED_CONFIG_RESCUE);
    	SPECIAL_DUTY_ATTRIBUTE_SET.add(INSURED_CONFIG_FLIGHT_DELAYED);
    	SPECIAL_DUTY_ATTRIBUTE_SET.add(INSURED_CONFIG_TRAVEL_CHANGE);
    	SPECIAL_DUTY_ATTRIBUTE_SET.add(INSURED_CONFIG_PROPERTY_LOSS);
    	SPECIAL_DUTY_ATTRIBUTE_SET.add(INSURED_CONFIG_EXAM_NOT_PASS);
    	SPECIAL_DUTY_ATTRIBUTE_SET.add(INSURED_CONFIG_OTHER_NO_HURT);
    }
    

     
    public static final String CASE_CLASS_PERSON_OTHER_LOSS = "15";

     
    public static final String CASE_CLASS_TRAVEL_ALERT = "18";
    
     
    public static final String CASE_CLASS_PROPERTY_LOSS = "17";
    
     
    public static final String CASE_CLASS_OTHER_LOSS = "22";
    
    public static final Set<String> SPECIAL_CASE_CLASS = new HashSet<String>();
    static {
    	SPECIAL_CASE_CLASS.add(CASE_CLASS_PERSON_OTHER_LOSS);
    	SPECIAL_CASE_CLASS.add(CASE_CLASS_TRAVEL_ALERT);
    	SPECIAL_CASE_CLASS.add(CASE_CLASS_PROPERTY_LOSS);
    	SPECIAL_CASE_CLASS.add(CASE_CLASS_OTHER_LOSS);
    	SPECIAL_CASE_CLASS.add(BIG_DISEASE);
    }
    
    public static final Set<String> SPECIAL_INSURED_CONFIG = new HashSet<String>();
    static {
    	SPECIAL_INSURED_CONFIG.add(INSURED_CONFIG_FATAL_DISEASE);
    	SPECIAL_INSURED_CONFIG.add(INSURED_CONFIG_OTHER_PERSONAL_DAMAGE);
    	SPECIAL_INSURED_CONFIG.add(INSURED_CONFIG_RESCUE);
    	SPECIAL_INSURED_CONFIG.add(INSURED_CONFIG_PROPERTY_LOSS);
    	SPECIAL_INSURED_CONFIG.add(INSURED_CONFIG_TRAVEL_CHANGE);
    	SPECIAL_INSURED_CONFIG.add(INSURED_CONFIG_OTHER_NO_HURT);
    }
    
     
    public static final String CASE_CLASS_FLIGHT_DELAY = "16";
    
     
    public static final String CASE_CLASS_VEHICL_DELAY_OTHER = "20";
    
    public static final Set<String> DELAY_CASE_CLASS = new HashSet<String>();
    static {
    	DELAY_CASE_CLASS.add(CASE_CLASS_FLIGHT_DELAY);
    	DELAY_CASE_CLASS.add(CASE_CLASS_VEHICL_DELAY_OTHER);
    }
    
     
    public static final String CHECKLOSS_PAYPATTERN_03 = "PAYPATTERN_03";
    
    
     
    public static final String COINSURANCE_TYPE_OUTER = "0";
    
     
    public static final String COINSURANCE_TYPE_INNER = "1";
    
     
    public static final String IS_MAIN_INSURANCE= "1";
    
     
    public static final String NOT_MAIN_INSURANCE = "0";
    
     
    public static final String PINGAN_COMPANY_CODE = "3005";
    
     
    public static final String IS_COINSURE_N = "N";
    
     
    public static final String IS_COINSURE_Y = "Y";
    
     
    public static final String PRIORITY_LAWSUIT_CASE = "诉讼案件";
    
     
    public static final String IS_AUTO_SETTLE_N = "0";
    
     
    public static final String IS_AUTO_SETTLE_Y = "1";
    
     
    public static final String HAS_SPECIAL_PROMISE_N = "N";
    
     
    public static final String HAS_SPECIAL_PROMISE_Y = "Y";

    // 个单
    public static final String BUSINESS_TYPE_PERSON = "P";

    // 团单
    public static final String BUSINESS_TYPE_ORG = "G";
    
    public static final String PREPAID_TYPE_MUTI = "PT_2706";
    
     
    public static final String IS_ABSOLUTE_REMIT_Y = "Y";
    
     
    public static final String IS_ABSOLUTE_REMIT_N = "N";
    
     
    public static final String DEATH_PAPER_TYPE_01 = "01";
    
     
    public static final String DEATH_PAPER_TYPE_02 = "02";
    
     
    public static final String DETAIL_POLICY_NO= "9999999999999999999";
    
     
    public static final String AHCS_VEHIC_TYPE_01 ="VEHIC_TYPE_01";

     
    public static final String AHCS_VEHIC_TYPE_02 ="VEHIC_TYPE_02";

     
    public static final String AHCS_VEHIC_TYPE_03 ="VEHIC_TYPE_03";

     
    public static final String AHCS_VEHIC_TYPE_04 ="VEHIC_TYPE_04";
    
     
    public static final String AHCS_CRUISES_DELAY_01 ="AHCS_CRUISES_DELAY-01";
    
     
    public static final String AHCS_CRUISES_DELAY_02 ="AHCS_CRUISES_DELAY-02";


    public static final String THERAPY_TYPE_INPATIENT = "THE_0302";
    
     
    public static final String THERAPY_TYPE_OUTPATIENT = "THE_0301";

    public static final String IS_DISINSURANCE_Y = "1";
    
     
    public static final String IS_DISINSURANCE_N = "0";
    
    
     
    public static final Map<String,Map<String,Set<String>>> ACC_TYPE_MAP = new HashMap<>();
    
     
    public static final String AHCS_TRAVEL_ACC_TYPE = "AHCS_TRAVEL_ACC_TYPE";
    
     
    public static final Map<String, String> AHCS_TRAVEL_ACC_MAP = new HashMap<>();
    
     
    public static final String AHCS_PROP_ACC_TYPE = "AHCS_PROP_ACC_TYPE";
    
     
    public static final Map<String, String> AHCS_PROP_ACC_MAP = new HashMap<>();
        
    static{
    	ACC_TYPE_MAP.put(AHCS_TRAVEL_ACC_TYPE, TRAVLE_MAPPING_MAP);
    	ACC_TYPE_MAP.put(AHCS_PROP_ACC_TYPE, PROPERTY_LOSS_MAPPING_MAP);
    }   
    
     
    public static final String BILL_TYPE_1 = "1";
    
     
    public static final String BILL_TYPE_2 = "2";
    
     
    public static final String BILL_TYPE_3 = "3";
    
     
    public static final Map<String, String> REPORT_PROPERTY_LOSS_MAP = new HashMap<>();
    static{
    	
    	REPORT_PROPERTY_LOSS_MAP.put("1", PR_ACC_TYPE_04);
    	REPORT_PROPERTY_LOSS_MAP.put("2", PR_ACC_TYPE_02);
    	    	REPORT_PROPERTY_LOSS_MAP.put("4", PR_ACC_TYPE_09);
    	REPORT_PROPERTY_LOSS_MAP.put("5", PR_ACC_TYPE_05);
    	REPORT_PROPERTY_LOSS_MAP.put("6", PR_ACC_TYPE_03);
    	REPORT_PROPERTY_LOSS_MAP.put("7", PR_ACC_TYPE_08);
    	REPORT_PROPERTY_LOSS_MAP.put("8", PR_ACC_TYPE_10);
    	REPORT_PROPERTY_LOSS_MAP.put("9", PR_ACC_TYPE_06);
    	REPORT_PROPERTY_LOSS_MAP.put("10", PR_ACC_TYPE_11);
    	REPORT_PROPERTY_LOSS_MAP.put("11", PR_ACC_TYPE_07);
    	REPORT_PROPERTY_LOSS_MAP.put("12", PR_ACC_TYPE_01);
    	REPORT_PROPERTY_LOSS_MAP.put("13", PR_ACC_TYPE_12);
    	REPORT_PROPERTY_LOSS_MAP.put("14", PR_ACC_TYPE_07);
    	REPORT_PROPERTY_LOSS_MAP.put("15", PR_ACC_TYPE_07);
    	REPORT_PROPERTY_LOSS_MAP.put("16", PR_ACC_TYPE_14);
    	REPORT_PROPERTY_LOSS_MAP.put("17", PR_ACC_TYPE_15);
    	REPORT_PROPERTY_LOSS_MAP.put("18", PR_ACC_TYPE_16);
    	REPORT_PROPERTY_LOSS_MAP.put("19", PR_ACC_TYPE_17);
    }
    
     
    public static final Map<String, String> REPORT_PL_ACCIDENT_CAUSE_MAP = new HashMap<>();
    static{
    	
    	REPORT_PL_ACCIDENT_CAUSE_MAP.put("1", "AHCS_ACCIDENT_CAUSE_01");
    	REPORT_PL_ACCIDENT_CAUSE_MAP.put("0", "AHCS_ACCIDENT_CAUSE_99");
    	REPORT_PL_ACCIDENT_CAUSE_MAP.put("2", "AHCS_ACCIDENT_CAUSE_02");
    	REPORT_PL_ACCIDENT_CAUSE_MAP.put("3", "AHCS_ACCIDENT_CAUSE_03");
    	REPORT_PL_ACCIDENT_CAUSE_MAP.put("4", "AHCS_ACCIDENT_CAUSE_04");
    	REPORT_PL_ACCIDENT_CAUSE_MAP.put("5", "AHCS_ACCIDENT_CAUSE_05");
    	REPORT_PL_ACCIDENT_CAUSE_MAP.put("6", "AHCS_ACCIDENT_CAUSE_06");
   }


    public static final Map<String, String> REPORT_TRAVEL_ALERT_MAP = new HashMap<>();
    static{
    	
    	REPORT_TRAVEL_ALERT_MAP.put("1", CHECKLOSS_OTHER2_TRAVELCHANGE);
    	REPORT_TRAVEL_ALERT_MAP.put("2", TR_ACC_TYPE_02);
    	REPORT_TRAVEL_ALERT_MAP.put("3", TR_ACC_TYPE_06);
    	REPORT_TRAVEL_ALERT_MAP.put("4", TR_ACC_TYPE_05);
    	REPORT_TRAVEL_ALERT_MAP.put("5", TR_ACC_TYPE_08);
    	REPORT_TRAVEL_ALERT_MAP.put("6", TR_ACC_TYPE_03);
    }

    public static final String SUPPLY_MEDICAL = "1";

    public static final String DISTINCT_INSURANCE_N = "2";

    public static final String DISTINCT_INSURANCE_Y = "3";

    public static final String YES = "Y";

	public final static String PRODUCT_CLASS_HEALTH = "01";

	public final static String PRODUCT_CLASS_PERSONAL_WEALTH = "02";

    public final static List<String> NO_PERSONAL_CLASS = new ArrayList<>();
    static {
        NO_PERSONAL_CLASS.add("16");
        NO_PERSONAL_CLASS.add("17");
        NO_PERSONAL_CLASS.add("18");
        NO_PERSONAL_CLASS.add("19");
        NO_PERSONAL_CLASS.add("20");
        NO_PERSONAL_CLASS.add("21");
        NO_PERSONAL_CLASS.add("22");
    }

}
