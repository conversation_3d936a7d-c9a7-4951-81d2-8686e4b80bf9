package com.paic.ncbs.claim.model.vo.checkloss;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.common.page.Pager;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("医院信息出参")
public class HospitalInfoVO {
    @ApiModelProperty("主键")
    private String idHospitalInfo;
    @ApiModelProperty("医院编码")
    private String hospitalCode;
    @ApiModelProperty("医院名称")
    private String hospitalName;
    @ApiModelProperty("医院名称")
    private String hospitalFullName;
    @ApiModelProperty("机构编码")
    private String departmentCode;
    @ApiModelProperty("机构名称")
    private String departmentName;
    @ApiModelProperty("医院等级")
    private String grade;
    @ApiModelProperty("医院级别")
    private String hospitalLevel;
    @ApiModelProperty("医院性质描述")
    private String hospitalPropertyDes;
    @ApiModelProperty("是否合作")
    private String isCooperate;
    @ApiModelProperty("是否定点社保 Y-是社保 N-否")
    private String isSocialInsurance;
    @ApiModelProperty("是否定点医院 Y-是 N-否")
    private String isAppointedHospital;
    @ApiModelProperty("是否有效,Y-有效,N-无效")
    private String isValid;
    @ApiModelProperty("机构联系人")
    private String departmentLinkMan;
    @ApiModelProperty("维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date maintainTime;
    @ApiModelProperty("省级行政区（省/直辖市）代码")
    private String provinceCode;
    @ApiModelProperty("地级市代码")
    private String prefectureLevelCode;
    @ApiModelProperty("县级行政区（区/县）代码")
    private String districtCode;
    @ApiModelProperty("省的中文名给前端展示用")
    private String provinceName;
    @ApiModelProperty("市的中文名给前端展示用")
    private String prefectureLevelName;
    @ApiModelProperty("县的中闻名给前端展示用")
    private String districtName;
    @ApiModelProperty("详细地址")
    private String addressDesc;
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedDate;
    @ApiModelProperty("当前页")
    private int currentPage;
    @ApiModelProperty("每页数据显示数量")
    private int perPageSize;
    @ApiModelProperty("结束页码")
    private int pageNumEnd;
    @ApiModelProperty("起始页码")
    private int pageNumBegin;
    @ApiModelProperty("医院名字")
    private String query;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("创建人")
    private String createdBy;
    @ApiModelProperty("更新人")
    private String updatedBy;
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdDate;
    @ApiModelProperty("分页对象")
    private Pager pager;
    @ApiModelProperty("编码机构类型1-全国，2-北京，3-上海，监管上报需要")
    private String orgType;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("维护时间-开始的时间")
    private Date maintainStartDate;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("维护时间-当结束的时间")
    private Date maintainEndDate;
    @ApiModelProperty("医院编码集合")
    private List<String> hospitalCodeList;
}
