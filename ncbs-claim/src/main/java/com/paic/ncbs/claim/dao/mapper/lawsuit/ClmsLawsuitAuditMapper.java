package com.paic.ncbs.claim.dao.mapper.lawsuit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitAudit;
import com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO;

import java.util.List;

/**
 * <p>
 * 诉讼批复表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
public interface ClmsLawsuitAuditMapper extends BaseMapper<ClmsLawsuitAudit> {

    /**
     * 新增案件批复信息
     * @param clmsLawsuitAudit
     * @return
     */
    void saveClmsLawsuitAudit(ClmsLawsuitAudit clmsLawsuitAudit);

    /**
     * 修改案件批复信息
     * @param clmsLawsuitAudit
     * @return
     */
    void updateClmsLawsuitAudit(ClmsLawsuitAudit clmsLawsuitAudit);

    /**
     * 删除案件批复信息
     * @param id
     * @return
     */
    void deleteClmsLawsuitAudit(String id);

    /**
     * 根据id查询案件批复信息
     * @param id
     * @return
     */
    ClmsLawsuitAudit getLawsuitAuditById(String id);

    /**
     * 查询待诉讼批复信息
     * @param clmsLawsuitAuditCaseVO
     * @return
     */
    List<ClmsLawsuitAuditCaseVO> getClmsLawsuitAudit(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO);

    /**
     * 查询待诉讼办结信息
     * @param clmsLawsuitAuditCaseVO
     * @return
     */
    List<ClmsLawsuitAuditCaseVO> getClmsLawsuitAuditConcluded(ClmsLawsuitAuditCaseVO clmsLawsuitAuditCaseVO);


}
