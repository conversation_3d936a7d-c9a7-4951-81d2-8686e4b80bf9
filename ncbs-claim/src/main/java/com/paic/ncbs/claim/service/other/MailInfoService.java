package com.paic.ncbs.claim.service.other;

import com.paic.ncbs.claim.model.dto.message.MailSendParamNew;
import com.paic.ncbs.message.model.dto.MailSendParam;
import com.paic.ncbs.message.model.dto.SmsResult;

public interface MailInfoService {

    /**
     * 异步发送
     * @param reportNo
     * @param mailSendParam
     */
    void sendMailByAsync(String reportNo, MailSendParam mailSendParam);

    /**
     * 同步发送
     * @param reportNo
     * @param mailSendParam
     */
    void sendMailBySync(String reportNo,MailSendParam mailSendParam);
    /**
     * 发送邮件信息
     */
    SmsResult sendReinsMailBySync(MailSendParamNew mailSendParam);
}
