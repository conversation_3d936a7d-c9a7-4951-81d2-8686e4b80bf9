package com.paic.ncbs.claim.dao.mapper.reinsurance;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.reinsurance.ReinsBillDTO;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

public interface ReinsBillMapper extends BaseMapper<ReinsBillDTO> {
    /**
     * 查询账单类别
     */
    List<String> getBillTypeByReportNo(@Param("reportNo") String reportNo);
    /**
     * 获取账单编号
     */
    List<Integer> getBillNoByReportNo(@Param("reportNo") String reportNo,@Param("billType") String billType);
    /**
     * 获取再保人
     */
    List<ReinsBillDTO> getReinsByReportNo(@Param("reportNo") String reportNo,
                                          @Param("billType") String billType,
                                          @Param("billNo") Integer billNo);
    /**
     * 获取再保账单信息
     */
    List<ReinsBillDTO> getBillByEntity(ReinsBillDTO reinsBillDTO);
    /**
     * 根据id更新账单状态
     */
    int updateBillStatusById(@Param("id") String id,@Param("status") String status);
    /**
     * 获取失败的账单数据
     */
    List<ReinsBillDTO> getFailBill(ReinsBillDTO reinsBillDTO);

}
