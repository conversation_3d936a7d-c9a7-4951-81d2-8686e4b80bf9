package com.paic.ncbs.claim.sao;

import com.paic.ncbs.claim.model.dto.reinsurance.*;

import java.util.Date;
import java.util.List;

/**
 * 对接再保
 */
public interface ReinsuranceSAO {

    /**
     * 理赔数据送再保接口
     * @param dto
     * @return
     */
    ReinsuranceRespDTO repayCal(IntfClaimMainDTO dto);
    /**
     * 理赔数据查询再保接口
     * @return
     */
    List<ReinsuranceRateDTO> queryRepayCal(ReinsuranceReqDTO dto);
    /**
     * 查询再保联系人
     * @return
     */
    List<ReinsurerDTO> queryReinsurer(ReinsuranceReqDTO dto);

    /**
     * 获取分保单号
     * @param dto
     * @return
     */
    String queryRepolicyNo(ReinsuranceReqDTO dto);
}
