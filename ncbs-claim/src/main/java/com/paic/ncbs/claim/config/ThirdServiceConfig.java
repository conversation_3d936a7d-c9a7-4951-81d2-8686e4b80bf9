package com.paic.ncbs.claim.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@RefreshScope
@Component
@Getter
public class ThirdServiceConfig {

    /**
     * 发送再保url
     */
    @Value("${ncbs.reinsurance.repayCalUrl:http://reins-main.lb.ssprd.com:48001/reins/claim/genReinsDangerUnit}")
    private String repayCalUrl;
    /**
     * 查询再保url
     */
    @Value("${ncbs.reinsurance.queryRepayCalUrl:http://reins-main.lb.ssdev.com:48001/reins/claim/reinsShare}")
    private String queryRepayCalUrl;
    /**
     * 查询再保人url
     */
    @Value("${ncbs.reinsurance.queryReinsurerUrl:http://reins-main.lb.ssdev.com:48001/reins/claim/claimReinsCode}")
    private String queryReinsurerUrl;
    /**
     * 查询分保单号url
     */
    @Value("${ncbs.reinsurance.queryRepolicyNoUrl:http://reins-main.lb.ssdev.com:48001/reins/claim/getRepolicyNo}")
    private String queryRepolicyNoUrl;
    /**
     * 再保页面跳转地址URL
     */
    @Value("${ncbs.reinsurance.reinsURL:https://reins-dev.inspci.com/reins-web/#/facultative/reinsuranceCorrection/view/}")
    private String reinsURL;
}
