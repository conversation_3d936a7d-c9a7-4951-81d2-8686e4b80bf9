package com.paic.ncbs.claim.dao.entity.endcase;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;

/**
 * 整案扩展表
 */
public class WholeCaseBaseExEntity extends EntityDTO {

    private static final long serialVersionUID = -2339179937204496681L;

    private String idClmWholeCaseBaseEx;

    private String idClmWholeCaseBase;

    private String reportNo;

    private Short caseTimes;

    private String isInjuryDirectCase;

    private String oneButtonServiceType;

    private String trafficPoliceTeam;

    private String firstTrafficPolice;

    private String secondTrafficPolice;

    private String isSelfHelp;

    private String isLawsuit;

    private String courtCaseNo;

    public String getIsLawsuit() {
        return isLawsuit;
    }

    public void setIsLawsuit(String isLawsuit) {
        this.isLawsuit = isLawsuit;
    }

    public String getCourtCaseNo() {
        return courtCaseNo;
    }

    public void setCourtCaseNo(String courtCaseNo) {
        this.courtCaseNo = courtCaseNo;
    }

    public String getIdClmWholeCaseBaseEx() {
        return idClmWholeCaseBaseEx;
    }

    public void setIdClmWholeCaseBaseEx(String idClmWholeCaseBaseEx) {
        this.idClmWholeCaseBaseEx = idClmWholeCaseBaseEx == null ? null : idClmWholeCaseBaseEx.trim();
    }

    public String getIdClmWholeCaseBase() {
        return idClmWholeCaseBase;
    }

    public void setIdClmWholeCaseBase(String idClmWholeCaseBase) {
        this.idClmWholeCaseBase = idClmWholeCaseBase == null ? null : idClmWholeCaseBase.trim();
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    public Short getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Short caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getIsInjuryDirectCase() {
        return isInjuryDirectCase;
    }

    public void setIsInjuryDirectCase(String isInjuryDirectCase) {
        this.isInjuryDirectCase = isInjuryDirectCase == null ? null : isInjuryDirectCase.trim();
    }

    public String getOneButtonServiceType() {
        return oneButtonServiceType;
    }

    public void setOneButtonServiceType(String oneButtonServiceType) {
        this.oneButtonServiceType = oneButtonServiceType == null ? null : oneButtonServiceType.trim();
    }

    public String getTrafficPoliceTeam() {
        return trafficPoliceTeam;
    }

    public void setTrafficPoliceTeam(String trafficPoliceTeam) {
        this.trafficPoliceTeam = trafficPoliceTeam == null ? null : trafficPoliceTeam.trim();
    }

    public String getFirstTrafficPolice() {
        return firstTrafficPolice;
    }

    public void setFirstTrafficPolice(String firstTrafficPolice) {
        this.firstTrafficPolice = firstTrafficPolice == null ? null : firstTrafficPolice.trim();
    }

    public String getSecondTrafficPolice() {
        return secondTrafficPolice;
    }

    public void setSecondTrafficPolice(String secondTrafficPolice) {
        this.secondTrafficPolice = secondTrafficPolice == null ? null : secondTrafficPolice.trim();
    }

    public String getIsSelfHelp() {
        return isSelfHelp;
    }

    public void setIsSelfHelp(String isSelfHelp) {
        this.isSelfHelp = isSelfHelp == null ? null : isSelfHelp.trim();
    }
}