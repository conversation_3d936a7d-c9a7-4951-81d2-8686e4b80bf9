package com.paic.ncbs.claim.model.vo.duty;

import com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.PolicyGroupDTO;
import com.paic.ncbs.claim.model.vo.dynamic.DynamicFieldResultVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@ApiModel("DutySurveyVO-核责VO")
public class DutySurveyVO {

    @ApiModelProperty("报案号")
    private String reportNo;

    @ApiModelProperty("赔付次数")
    private int caseTimes;

    @ApiModelProperty("客户号")
    private String partyNo;

    @ApiModelProperty("状态。1：发送，0：暂存")
    private String status;

    @ApiModelProperty("案件修改备注")
    private String modifyCaseRemark;

    @ApiModelProperty("案件类别列表：1.人伤 2.非人伤")
    private List<String> caseClass;

    @ApiModelProperty("案件细类列表：如意外门诊,意外住院等")
    private List<String> caseSubClass;

    @ApiModelProperty("案件细类列表明细")
    private List<String> caseSubClassName;

    @ApiModelProperty("报案查勘说明VO")
    private SurveyVO surveyVO;

    @ApiModelProperty("预估保单表单dto")
    private EstimatePolicyFormDTO estimatePolicyFormDTO;

    @ApiModelProperty("人伤就医情况VO")
    private PeopleHurtVO peopleHurtVO;

    @ApiModelProperty("非人伤VO")
    private NoPeopleHurtVO noPeopleHurtVO;

    @ApiModelProperty("CheckPaymentVO-校验支付金额VO")
    private CheckPaymentVO checkPaymentVO;

    @ApiModelProperty("VerifyConclusionVO-核责结论VO")
    private VerifyConclusionVO verifyConclusionVO;

    @ApiModelProperty("跟踪记录表主键")
    private String idAhcsTrackInto;

    @ApiModelProperty("任务实例ID")
    private String taskId;

    @ApiModelProperty("差错代码列表")
    private List<String> mistakeCodeList;

    @ApiModelProperty("差错备注/描述")
    private String mistakeRemark;

    @ApiModelProperty("用户ID（UM账号）")
    private String userId;

    @ApiModelProperty(" 发现并登记差错的任务Id(发现并记录差错的环节id)")
    private String recordTaskId;

    @ApiModelProperty("救援信息VO")
    private PersonRescueVO personRescueVO;

    @ApiModelProperty("（核责发送，有风险信息提示时的）不提调说明")
    private String nonSurveyStatement;

    @ApiModelProperty("是否完成核责")
    private String isCompleteDuty;

    @ApiModelProperty("是否清空账单金额")
    private String isClearBillAmonut;

    @ApiModelProperty("是否重大事故")
    private String isHugeAccident;

    @ApiModelProperty("是否手动转换字段")
    private boolean manualTransToField;

    @ApiModelProperty("数据来源")
    private String migrateFrom;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("生日")
    private String birthDay;

    @ApiModelProperty("被保人证件号")
    private String certificateNo;

    @ApiModelProperty("被保人证件类型")
    private String certificateType;

    @ApiModelProperty("国外城市编码")
    private String accidentNation;

    @ApiModelProperty("拒赔申明")
    private List<ClaimRejectionApprovalRecordEntity> claimRejectionApprovalRecordEntity ;

    @ApiModelProperty("页面跳转来源 N：收单 Y：理算")
    private String isSettle;
    private boolean standardVersionSwitch;
    @ApiModelProperty("是否有风险标识：Y-是，N-否:TPA调用立案接口会传入")
    private String riskFlag;
    @ApiModelProperty("风险描述备注：是否有风险标识 未Y时必填 TPA调用立案接口时传入")
    private String riskRemark;

    private List<PolicyGroupDTO> policyRiskGroupList;

    @ApiModelProperty(value = "标的")
    private List<CaseRiskPropertyDTO> riskPropertyList;

    @ApiModelProperty(value = "动态字段")
    private List<DynamicFieldResultVO> dynamicFieldResult;
    @ApiModelProperty("损失类型 3-人伤 4-健康 5-财产 6-其他")
    private String lossClass;
    @ApiModelProperty(value = "出险原因大类")
    private String accidentCauseLevel1;
    @ApiModelProperty(value = "出险原因明细类")
    private String accidentCauseLevel2;
    @ApiModelProperty(value = "出险原因大类")
    private String accidentCauseLevel3;
    @ApiModelProperty(value = "出险原因明细类")
    private String accidentCauseLevel4;
    @ApiModelProperty(value="估损信息")
    private Map<String,List<LossEstimationVO>> lossEstimationVos;
}
