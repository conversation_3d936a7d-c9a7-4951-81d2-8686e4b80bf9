package com.paic.ncbs.claim.model.vo.reinsurance;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
@Data
public class BillSendVO {
    /**
     * 报案号
     */
    private String reportNo;
    /**
     * 编号
     */
    private Integer billNo;

    /**
     * 账单类型 1-估损,2-实赔
     */
    private String billType;

    /**
     * 再保人编码
     */
    private String reinsCode;

    /**
     * 再保人名称
     */
    private String reinsName;

    /**
     * 处理人
     */
    private String handler;
    /**
     * 发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;
    /**
     * 发送状态（1-成功，2-失败）
     */
    private String status;

}
