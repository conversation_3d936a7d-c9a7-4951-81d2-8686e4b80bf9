package com.paic.ncbs.claim.mq.producer.impl;

import cn.wesure.cmq.CmqTemplate;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.dto.problem.RequestData;
import com.paic.ncbs.claim.mq.producer.MqProducerProblemCaseService;
import com.paic.ncbs.claim.service.mqcompensation.MqCompensationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MqProducerProblemCaseServiceImpl implements MqProducerProblemCaseService {

    @Autowired
    private CmqTemplate cmqTemplate;
    @Value("${mq.tpa.problemCase.topic:T-NCBS-CLAIM-PROBLEMCASE-DEV}")
    private String topic;
    @Value("${mq.tpa.problemCase.delayTime:2*60*1000}")
    private Integer delayTime;
    @Autowired
    private MqCompensationService mqCompensationService;
    @Override
    public void requestProblemCase(RequestData data) {
        String msg = "";
        try {
            ObjectMapper mapper = new ObjectMapper();
            msg = mapper.writeValueAsString(data);
            Stopwatch stopwatch = Stopwatch.createStarted();
            LogUtil.audit("发送问题件答复开始：topic="+topic+"，msg="+msg);
            String mqResp = cmqTemplate.sendQueue(topic,msg, delayTime);
            LogUtil.audit("发送问题件答复结束：topic="+topic+"，msg="+msg+"，mqResp="+mqResp+"，useTime="+stopwatch.elapsed(TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            log.error("发送问题件答复异常！", e);
            mqCompensationService.addMqCompensation(topic, msg, BaseConstant.INT_0);
        }
    }
}
