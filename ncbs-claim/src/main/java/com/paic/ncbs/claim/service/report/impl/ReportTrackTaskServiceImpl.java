package com.paic.ncbs.claim.service.report.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.InsuredApplyTypeEnum;
import com.paic.ncbs.claim.common.response.MockJsonUtil;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonAccidentMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.model.dto.duty.SurveyDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.dto.endcase.SubCaseClassDefineDTO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO;
import com.paic.ncbs.claim.model.dto.report.ReportBaseInfoResData;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.duty.*;
import com.paic.ncbs.claim.model.vo.endcase.CaseClassDefineVO;
import com.paic.ncbs.claim.model.vo.trace.ClmsTraceRecordVO;
import com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo;
import com.paic.ncbs.claim.service.base.impl.SwitchServiceUtil;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.checkloss.ChannelProcessService;
import com.paic.ncbs.claim.service.common.ClaimSendTpaMqInfoService;
import com.paic.ncbs.claim.service.common.ClaimWorkFlowService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import com.paic.ncbs.claim.service.duty.LossEstimationService;
import com.paic.ncbs.claim.service.dynamic.IDynamicFieldResultService;
import com.paic.ncbs.claim.service.endcase.CaseBaseService;
import com.paic.ncbs.claim.service.endcase.CaseClassService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.report.*;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.CustomerService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.trace.PersonTraceService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service("reportTrackTaskService")
public class ReportTrackTaskServiceImpl implements ReportTrackTaskService {

    @Autowired
    private ChannelProcessService channelProcessService;
    @Autowired
    private DutySurveyService dutySurveyService;
    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;
    @Autowired
    private CaseProcessService caseProcessService;
    @Autowired
    private RegisterCaseService registerCaseService;
    @Autowired
    private EstimateService estimateService;
    @Autowired
    private TaskInfoService taskInfoService;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private CaseClassMapper caseClassDao;
    @Autowired
    private PersonAccidentMapper personAccidentDao;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private CommonParameterService commonService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private CaseClassService caseClassService;
    @Autowired
    private ClaimWorkFlowService claimWorkFlowService;

    @Autowired
    private ClaimSendTpaMqInfoService claimSendTpaMqInfoService;

    @Autowired
    private RiskPropertyService riskPropertyService;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    private IDynamicFieldResultService dynamicFieldResultService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Autowired
    private CaseBaseService caseBaseService;
    @Autowired
    private LossEstimationService lossEstimationService;
    @Autowired
    private ReportInfoExService reportInfoExService;
    @Autowired
    private ReportAccidentService reportAccidentService;
    @Autowired
    private EstimateChangeService estimateChangeService;
    @Autowired
    private PersonTraceService personTraceService;
    @Override
    @Transactional
    public void saveReportTrack(DutySurveyVO dutySurveyVO, String loginUm) throws Exception {

        String reportNo = dutySurveyVO.getReportNo();
        String status = dutySurveyVO.getStatus();
        int caseTimes =dutySurveyVO.getCaseTimes();
        dutySurveyVO.setUserId(loginUm);

        String taskId = dutySurveyVO.getTaskId();

        LogUtil.audit("#保存报案跟踪数据,reportNo=%s,taskId=%s,status=%s,入参={}", reportNo, taskId, status, JsonUtils.toJsonString(dutySurveyVO));

        TaskInfoDTO taskInfo = taskInfoService.findLatestByReportNoAndBpmKey(reportNo, caseTimes, BpmConstants.OC_REPORT_TRACK);
        /* delete by zjtang 取消旧校验逻辑
        if (Objects.isNull(taskInfo) || !BaseConstant.STRING_0.equals(taskInfo.getStatus())) {
            throw new GlobalBusinessException("任务不存在或挂起中，无法提交！");
        }
         */
        if (Objects.isNull(taskInfo)) {
            throw new GlobalBusinessException("任务不存在，无法提交！");
        }

        //status 1-发送 0-立案，暂存
        if (ChecklossConst.STATUS_TMP_SUBMIT.equals(status)) {
            //报案跟踪提交校验存在人伤跟踪时必须上传一条单证数据
            PersonTranceRequestVo personTranceRequestVo = new PersonTranceRequestVo();
            personTranceRequestVo.setReportNo(reportNo);
            personTranceRequestVo.setCaseTimes(caseTimes);
            personTranceRequestVo.setTaskDefinitionBpmKey(BpmConstants.OC_HUMAN_INJURY_TRACKING);
            personTranceRequestVo.setFlag("0");
            personTraceService.getChekPersonTrace(personTranceRequestVo);
            //校验当前流程是否有冲突
            bpmService.processCheck(reportNo,BpmConstants.OC_REPORT_TRACK,BpmConstants.OPERATION_SUBMIT);
            //校验案件是否立案，是否结案(待改造，看看能否和下面的校验isExistRegisterRecord合并成一个)
            this.checkReportTrack(dutySurveyVO);
            this.validReportTrack(dutySurveyVO);
        } else if (ChecklossConst.STATUS_TMP_SAVE.equals(status)) {
            estimateService.dealEstimateInfo(dutySurveyVO);
        }

        //将之前保存的案件类别先设置为“失效”状态
        List<String> caseSubClassList= caseClassService.saveClassData(dutySurveyVO);

        PeopleHurtVO peopleHurtVO = dutySurveyVO.getPeopleHurtVO();
        if (peopleHurtVO != null) {
            //人伤信息处理
            peopleHurtVO = dutySurveyService.dealPeopleHurtInfo(dutySurveyVO,caseSubClassList);
        }
        NoPeopleHurtVO noPeopleHurtVO = dutySurveyVO.getNoPeopleHurtVO();
        if (Objects.nonNull(noPeopleHurtVO)) {
            //非人伤 9月版本
            dutySurveyService.dealNoPeopleHurtInfo(dutySurveyVO,peopleHurtVO);
        }

        if (dutySurveyVO.getPersonRescueVO() != null) {
            dutySurveyService.savePersonRescue(dutySurveyVO.getPersonRescueVO(), reportNo, caseTimes, loginUm, taskId, status);
        }
        this.addReportTrack(dutySurveyVO, loginUm, status);

        //动态字段处理
        if(!CollectionUtils.isEmpty(dutySurveyVO.getDynamicFieldResult())) {
            dynamicFieldResultService.saveDynamicFieldResult(dutySurveyVO.getDynamicFieldResult());
        }

        try {
            if(ListUtils.isNotEmpty(dutySurveyVO.getRiskPropertyList())){
                for (CaseRiskPropertyDTO caseRiskPropertyDTO : dutySurveyVO.getRiskPropertyList()) {
                    caseRiskPropertyDTO.setReportNo(reportNo);
                    caseRiskPropertyDTO.setCaseTimes(caseTimes);
                    caseRiskPropertyDTO.setTaskId(BpmConstants.REPORT_TRACK);
                    caseRiskPropertyDTO.setCreatedBy(loginUm);
                    caseRiskPropertyDTO.setUpdatedBy(loginUm);
                    caseRiskPropertyDTO.setIdCaseRiskProperty(UuidUtil.getUUID());

                    if (!CollectionUtils.isEmpty(caseRiskPropertyDTO.getRiskPropertyMap())) {
                        caseRiskPropertyDTO.setRiskDetail(JSON.toJSONString(caseRiskPropertyDTO.getRiskPropertyMap()));
                        if(BaseConstant.TARGET_TYPE_EMPLOYER.equals(caseRiskPropertyDTO.getRiskGroupType())){
                            Map<String, Object> riskPropertyMap = caseRiskPropertyDTO.getRiskPropertyMap();
                            caseRiskPropertyDTO.setIdPlyRiskProperty((String) riskPropertyMap.get("idPlyRiskProperty"));
                            caseRiskPropertyDTO.setName((String) riskPropertyMap.get("name"));
                            caseRiskPropertyDTO.setCertificateType((String) riskPropertyMap.get("certificateType"));
                            caseRiskPropertyDTO.setCertificateNo((String) riskPropertyMap.get("certificateNo"));
                        }
                    }
                    if(Objects.isNull(caseRiskPropertyDTO.getIdPlyRiskGroup())){
                        caseRiskPropertyDTO.setIdPlyRiskGroup("");
                    }
                    if(StringUtils.isEmptyStr(caseRiskPropertyDTO.getPolicyNo())){
                        List<String> policyNoByReportNo = ahcsPolicyInfoMapper.getPolicyNoByReportNo(reportNo);
                        if(!CollectionUtils.isEmpty(policyNoByReportNo)){
                            caseRiskPropertyDTO.setPolicyNo(policyNoByReportNo.get(0));
                        }
                    }

                }
                CaseRiskPropertyDTO tempCaseRiskPropertyDTO = new CaseRiskPropertyDTO();
                tempCaseRiskPropertyDTO.setReportNo(reportNo);
                tempCaseRiskPropertyDTO.setCaseTimes(caseTimes);
                tempCaseRiskPropertyDTO.setUpdatedBy(loginUm);
                tempCaseRiskPropertyDTO.setUpdatedDate(new Date());
                riskPropertyService.removeCaseRiskProperty(tempCaseRiskPropertyDTO);
                riskPropertyService.saveCaseRiskPropertyList(dutySurveyVO.getRiskPropertyList());
            }
        }catch (Exception e){
            LogUtil.info("报案跟踪保存标的失败,不影响原有流程",e);
        }
        //更新CLMS_REPORT_INFO_EX表的估损字段
        reportInfoExService.updateCaseClassByReportNo(reportNo, dutySurveyVO.getLossClass(), loginUm);
        dutySurveyVO.setAccidentCauseLevel3(dutySurveyVO.getAccidentCauseLevel1());
        dutySurveyVO.setAccidentCauseLevel4(dutySurveyVO.getAccidentCauseLevel2());
        //更新CLM_REPORT_ACCIDENT表的出险原因字段
        reportAccidentService.updateAccidentReasonByReportNo(reportNo,dutySurveyVO.getAccidentCauseLevel3(),dutySurveyVO.getAccidentCauseLevel4(),loginUm);
        lossEstimationService.saveLossEstimationList(reportNo, caseTimes,BpmConstants.REPORT_TRACK,dutySurveyVO.getLossClass(),dutySurveyVO.getLossEstimationVos());

        if (ChecklossConst.STATUS_TMP_SUBMIT.equals(status)) {
            //操作记录
            operationRecordService.insertOperationRecordByLabour(reportNo, BpmConstants.OC_REPORT_TRACK, "提交", null);
            //完成报案跟踪工作流 原先的事件改成直接调用创建任务和完成任务
            //claimWorkFlowService.dealWorkFlowData(reportNo,caseTimes);
            //TPA报案的数据 核心业务人员操作时 流程状态变化后需要通知TPA,sendTpaMq里有判断只有TPA报案的数据才会推送MQ
            //claimSendTpaMqInfoService.sendTpaMq(reportNo,caseTimes,CaseProcessStatus.PENDING_ACCEPT.getCode());
            //更新ES同步时间
           // caseBaseService.updateEsUpdatedDate(reportNo,caseTimes);
        }

    }
    /**
     * 报案跟踪信息校验
     * @param dutySurveyVO
     */
    private void checkReportTrack(DutySurveyVO dutySurveyVO) {
        RapeCheckUtil.checkParamEmpty(dutySurveyVO.getLossClass(), "损失类别");
        RapeCheckUtil.checkParamEmpty(dutySurveyVO.getAccidentCauseLevel2(), "出险原因");
    }

    public String saveReportTrackTask(String reportNo, int caseTimes)  {
        if (!registerCaseService.isExistRegisterRecord(reportNo, caseTimes)) {
            if(!taskInfoService.hasNotFinishTaskByTaskKey(reportNo,caseTimes,BpmConstants.OC_REGISTER_REVIEW,null)){
                //不存在审核未完成的立案任务开启报案跟踪任务
                bpmService.newSuspendOrActiveTask_oc(reportNo,caseTimes,BpmConstants.OC_REPORT_TRACK,false);
                return "已报案待报案跟踪";
            }else{//存在未审核完成挂起报案跟踪任务
                bpmService.newSuspendOrActiveTask_oc(reportNo, caseTimes,BpmConstants.OC_REPORT_TRACK,true);
                caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.REGISTRATION_WAIT_APPROVING.getCode());
                return "已立案待审核";
            }

        }
        //更新taskId更新为未决历史信息表IdFlagHistoryChange与未决历史表关联
        List<EstimateChangeDTO> changeOldList = estimateChangeService.getLastEstimateChangeList(reportNo,caseTimes);
        if (!CollectionUtils.isEmpty(changeOldList)){
            String oldIdFlagHistoryChange = changeOldList.get(0).getIdFlagHistoryChange();
            lossEstimationService.updateLossEstimationTaskId(reportNo, caseTimes, oldIdFlagHistoryChange, BpmConstants.REPORT_TRACK);
        }
        //完成报案跟踪工作流 原先的事件改成直接调用创建任务和完成任务
        claimWorkFlowService.dealWorkFlowData(reportNo,caseTimes);
        //TPA报案的数据 核心业务人员操作时 流程状态变化后需要通知TPA,sendTpaMq里有判断只有TPA报案的数据才会推送MQ
        claimSendTpaMqInfoService.sendTpaMq(reportNo,caseTimes,CaseProcessStatus.PENDING_ACCEPT.getCode());
        //更新ES同步时间
        caseBaseService.updateEsUpdatedDate(reportNo,caseTimes);
        return "已报案跟踪待收单";
    }
    public void addReportTrack(DutySurveyVO dutySurveyVO, String loginUm, String status)  {
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();

        String taskId = dutySurveyVO.getTaskId();
        SurveyVO surveyVO = dutySurveyVO.getSurveyVO();
        SurveyDTO surveyDTO = new SurveyDTO();
        surveyVO.setReportNo(reportNo) ;
        surveyVO.setCaseTimes(caseTimes);
        if (surveyVO != null) {
            BeanUtils.copyProperties(surveyVO, surveyDTO);
        }
        surveyDTO.setTaskId(taskId);
        surveyDTO.setStatus(status);
        dutySurveyService.saveSurvey(reportNo, caseTimes, loginUm, surveyDTO);

    }

    @Override
    public DutySurveyVO getReportTrackInfo(String reportNo, int caseTimes) throws Exception {

        DutySurveyVO dutySurveyVO = new DutySurveyVO();
        dutySurveyVO.setStandardVersionSwitch(SwitchServiceUtil.getStandardVersionSwitch());
        dutySurveyVO.setReportNo(reportNo);
        dutySurveyVO.setCaseTimes(caseTimes);

        String taskId = caseClassDao.getReportTrackTaskCode(reportNo, caseTimes, null, TacheConstants.ADDITIONAL_SURVEY);//report1
        //案件大类别
        List<CaseClassDTO> caseClassList = caseClassDao.getBigCaseClassList(reportNo, caseTimes, "", taskId);
        if (ListUtils.isNotEmpty(caseClassList)){
            dutySurveyVO.setCaseClass(caseClassList.stream().map(CaseClassDTO::getCaseSubClass).collect(Collectors.toList()));
        }
        //案件子类别
        List<String> caseSubClassList = caseClassDao.getCaseClassList(reportNo, caseTimes, taskId);
        if (ListUtils.isNotEmpty(caseSubClassList) && StringUtils.isNotEmpty(caseSubClassList.get(0))) {
            dutySurveyVO.setCaseSubClass(caseSubClassList);
            List<String> caseSubClassName   =new ArrayList<>();
            caseSubClassList.forEach(e-> caseSubClassName.add(InsuredApplyTypeEnum.getName(e)));
            dutySurveyVO.setCaseSubClassName(caseSubClassName);
        }else {
            dutySurveyVO.setCaseSubClass(new ArrayList<>());
            dutySurveyVO.setCaseSubClassName(new ArrayList<>());
        }

        //案件大类别
        Set<String> bigClass = new HashSet<>();
        if(ListUtils.isNotEmpty(dutySurveyVO.getCaseClass())){
            bigClass.addAll(dutySurveyVO.getCaseClass());
        }
//        List<CaseClassDTO> caseClassList = caseClassDao.getBigCaseClassList(reportNo, caseTimes, "", taskId);
//        if (ListUtils.isNotEmpty(caseClassList)){
//            bigClass.addAll(caseClassList.stream().map(CaseClassDTO::getCaseSubClass).collect(Collectors.toList()));
//        }

        if(SwitchServiceUtil.getStandardVersionSwitch() && ListUtils.isNotEmpty(caseSubClassList)){
            List<CaseClassDefineVO> caseClassVOList = MockJsonUtil.getJavaListFromFile("case_class_define.json",CaseClassDefineVO.class);
            List<SubCaseClassDefineDTO> subCaseClassDefines = caseClassVOList.get(1).getSubCaseClassDefines();
            for (String s : caseSubClassList) {
                for (SubCaseClassDefineDTO dto : subCaseClassDefines) {
                    if(s.equals(dto.getClassCode())){
                        bigClass.add(ChecklossConst.CASECLASS_NO_PEOPLE_HURT);
                        break;
                    }
                }
            }
        }
        dutySurveyVO.setCaseClass(new ArrayList<>(bigClass));


        List<ChannelProcessDTO> channelProcessList = channelProcessService.getChanelProcessIdList(reportNo, caseTimes);
        if (channelProcessList != null && !channelProcessList.isEmpty()) {
            for (ChannelProcessDTO channelProcess : channelProcessList) {
                switch (channelProcess.getChannelType()) {
                    case ChecklossConst.CASECLASS_PEOPLE_HURT:
                        PeopleHurtVO peopleHurtVO = dutySurveyService.getPeopleHurtVO(reportNo, caseTimes, channelProcess.getIdAhcsChannelProcess(), taskId);
                        peopleHurtVO.setChannelType(ChecklossConst.CASECLASS_PEOPLE_HURT);
                        peopleHurtVO.setIdAhcsChannelProcess(channelProcess.getIdAhcsChannelProcess());
                        dutySurveyVO.setPeopleHurtVO(peopleHurtVO);
                        break;
                    case ChecklossConst.CASECLASS_NO_PEOPLE_HURT:
                        NoPeopleHurtVO noPeopleHurtVO = dutySurveyService.getNoPeopleHurtVO(reportNo, caseTimes, taskId, null);
                        noPeopleHurtVO.setChannelType(ChecklossConst.CASECLASS_NO_PEOPLE_HURT);
                        noPeopleHurtVO.setIdAhcsChannelProcess(channelProcess.getIdAhcsChannelProcess());
                        dutySurveyVO.setNoPeopleHurtVO(noPeopleHurtVO);
                        // 非人伤查询标的
                        CaseRiskPropertyDTO caseRiskPropertyDTO = new CaseRiskPropertyDTO();
                        caseRiskPropertyDTO.setReportNo(reportNo);
                        caseRiskPropertyDTO.setCaseTimes(caseTimes);
                        dutySurveyVO.setRiskPropertyList(riskPropertyService.getLastTaskIdCaseRiskPropertyList(caseRiskPropertyDTO));
                        break;
                    default:
                        break;
                }
            }
        }
        dutySurveyVO.setSurveyVO(dutySurveyService.getSurveyVOByCaseType(reportNo, caseTimes,0));
        if(dutySurveyVO.getSurveyVO().getPhoneSurveyDetail() == null){
            dutySurveyVO.getSurveyVO().setPhoneSurveyDetail(dutySurveyVO.getPeopleHurtVO().getAccidentVO().getAccidentDetail());
        }
        VerifyConclusionVO verifyConclusionVO = new VerifyConclusionVO();
        WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseService.getWholeCaseBase2(reportNo, caseTimes);
        if (wholeCaseBaseDTO != null) {
            verifyConclusionVO.setIndemnityConclusion(wholeCaseBaseDTO.getIndemnityConclusion());
            verifyConclusionVO.setIndemnityModel(wholeCaseBaseDTO.getIndemnityModel());
            dutySurveyVO.setMigrateFrom(wholeCaseBaseDTO.getMigrateFrom());//后续看看可否去掉
        }
        dutySurveyVO.setVerifyConclusionVO(verifyConclusionVO);

        String taskIdHis = caseClassDao.getReportTrackTaskCode(reportNo, caseTimes, ChecklossConst.STATUS_TMP_SUBMIT, TacheConstants.ADDITIONAL_SURVEY);
        PersonAccidentDTO personAccidentDTO = personAccidentDao.getPersonAccidentInfo(reportNo, String.valueOf(caseTimes), taskIdHis, ChecklossConst.STATUS_TMP_SUBMIT);
        if (personAccidentDTO != null && StringUtils.isNotEmpty(personAccidentDTO.getIsHugeAccident())) {
            dutySurveyVO.setIsHugeAccident(personAccidentDTO.getIsHugeAccident());
        }

        ReportBaseInfoResData reportBaseInfoResData = reportInfoService.requestReportBaseInfo(reportNo);
        if (Objects.nonNull(reportBaseInfoResData)) {
            dutySurveyVO.setName(reportBaseInfoResData.getName());
            dutySurveyVO.setBirthDay(reportBaseInfoResData.getBirthday());
            dutySurveyVO.setCertificateType(reportBaseInfoResData.getCertificateType());
            dutySurveyVO.setCertificateNo(reportBaseInfoResData.getCertificateNo());
            String lossClass = reportBaseInfoResData.getLossClass();
            if(lossClass!=null){//将人伤映射为人伤，非人上映射为其他
                lossClass = lossClass.replace('1', '3');
                lossClass = lossClass.replace('2', '6');
            }
            dutySurveyVO.setLossClass(lossClass);
            dutySurveyVO.setAccidentCauseLevel1(reportBaseInfoResData.getAccidentCauseLevel3Code());
            dutySurveyVO.setAccidentCauseLevel2(reportBaseInfoResData.getAccidentCauseLevel4Code());
            LogUtil.audit("完整dutySurveyVO参数：{}", JSON.toJSONString(dutySurveyVO));
        }
        AdressSearchDto adressSearchDto  = new AdressSearchDto();
        PersonAccidentVO personAccidentVO = dutySurveyVO.getPeopleHurtVO().getAccidentVO();
        if (dutySurveyVO.getPeopleHurtVO().getAccidentVO().getOverseasOccur() != null){
            if (("0").equals(dutySurveyVO.getPeopleHurtVO().getAccidentVO().getOverseasOccur())){
                adressSearchDto.setOverseasOccur(personAccidentVO.getOverseasOccur()).setAccidentProvinceCode(personAccidentVO.getAccidentProvince())
                        .setAccidentCountyCode(personAccidentVO.getAccidentCounty()).setAccidentCityCode(personAccidentVO.getAccidentCity());
            }else {
                adressSearchDto.setOverseasOccur(personAccidentVO.getOverseasOccur()).setAccidentAreaCode(personAccidentVO.getAccidentArea())
                        .setAccidentNationCode(personAccidentVO.getAccidentNation());
            }
            AdressSearchDto detailAdressFormCode = commonService.getDetailAdressFormCode(adressSearchDto);
            personAccidentVO.setAccidentAreaName(detailAdressFormCode.getAccidentAreaName());
            personAccidentVO.setAccidentNationName(detailAdressFormCode.getAccidentNationName());
            personAccidentVO.setAccidentProvinceName(detailAdressFormCode.getAccidentProvinceName());
            personAccidentVO.setAccidentCityName(detailAdressFormCode.getAccidentCityName());
            personAccidentVO.setAccidentCountyName(detailAdressFormCode.getAccidentCountyName());
        }
        dutySurveyVO.setLossEstimationVos(lossEstimationService.getLastLossEstimationVOs(reportNo, caseTimes));
        try {
            CaseRiskPropertyDTO caseRiskQuery = new CaseRiskPropertyDTO(reportNo,caseTimes,BpmConstants.REPORT_TRACK);
            caseRiskQuery.setMatchSelected(true);
            dutySurveyVO.setPolicyRiskGroupList(riskPropertyService.getCaseRiskProperty(caseRiskQuery));
        }catch (Exception e){
            LogUtil.info("报案跟踪查询标的失败,不影响原有流程",e);
        }
        dutySurveyVO.setDynamicFieldResult(dynamicFieldResultService.getDynamicFieldResultReport(reportNo, caseTimes, caseSubClassList));
        return dutySurveyVO;
    }

    private void validReportTrack(DutySurveyVO dutySurveyVO) throws GlobalBusinessException {
        String reportNo = dutySurveyVO.getReportNo();
        int caseTimes = dutySurveyVO.getCaseTimes();
       /* if (!registerCaseService.isExistRegisterRecord(reportNo, caseTimes)) {
            LogUtil.audit("#请先完成立案动作#reportNo=" + reportNo);
            throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG,"请先完成立案动作");
        }*/
        // 下面要改成新的流程状态
        // 流程状态（01-待立案,02-待收单,03-待审核,04-审核中,05-待结案,06-已结案,07-零结,08-注销,09-航延使用）
        String processStatus = caseProcessService.getCaseProcessStatus(reportNo, caseTimes);
        LogUtil.audit("#当前案件流程状态状态# reportNo=" + reportNo + ",caseTimes=" + caseTimes + ",processStatus=" + processStatus);
        boolean isEndCase = ConfigConstValues.PROCESS_STATUS_CASE_CLOSED.equals(processStatus)
                || ConfigConstValues.PROCESS_STATUS_ZORE_CANCEL.equals(processStatus)
                || ConfigConstValues.PROCESS_STATUS_CANCELLATION.equals(processStatus);
        if (isEndCase) {
            LogUtil.audit("该案件已结案! reportNo=" + reportNo + ",processStatus=" + processStatus);
            throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG);
        }

        //如果不记名校验是否已实名
        ReportCustomerInfoEntity customerInfo = customerService.getReportCustomerInfoByReportNo(reportNo);
        if("200".equals(customerInfo.getClientCluster()) || "020".equals(customerInfo.getClientCluster())){
            throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG,"请实名化被保人");
        }

    }

}
