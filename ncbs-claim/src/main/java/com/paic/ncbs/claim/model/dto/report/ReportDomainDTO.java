package com.paic.ncbs.claim.model.dto.report;

import com.paic.ncbs.claim.dao.entity.ahcs.*;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.model.dto.ahcs.AhcsPolicyDomainDTO;
import com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO;
import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import com.paic.ncbs.claim.model.dto.pet.PetInjureDTO;
import com.paic.ncbs.claim.model.dto.pet.PetInjureExDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO;
import com.paic.ncbs.claim.model.vo.pet.IChongReportVO;
import com.paic.ncbs.claim.model.vo.report.OnlineReportVO;
import com.paic.ncbs.claim.model.vo.report.TelReportVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportDomainDTO {
    private IChongReportVO iChongReportVO;
    private TelReportVO telVo;
    private OnlineReportVO onlineReportVO;
    private String reportNo;
    private Integer caseTimes = 1;
    private String acceptDepartmentCode;
    private String reportAcceptUm;
    private String clientNo;
    private Date reportDate = new Date();

    private ReportAccidentEntity reportAccident;
    private ReportAccidentExEntity reportAccidentEx;
    private ReportInfoEntity reportInfo;
    private ReportExcEntity reportExc;
    private ReportInfoExEntity reportInfoExs;
    private List<CaseBaseEntity> caseBases;
    private List<LinkManEntity> LinkMans;
    private WholeCaseBaseEntity wholeCaseBase;
    private WholeCaseBaseExEntity wholeCaseBaseEx;
    private ReportCustomerInfoEntity reportCustomerInfo;
    private List<AhcsPolicyDomainDTO> ahcsPolicyDomainDTOs = new ArrayList<AhcsPolicyDomainDTO>();

    private List<AhcsPolicyInfoEntity> policyInfoList = new ArrayList<>();
    private List<AhcsInsuredPresonEntity> insuredPresonList = new ArrayList<>();
    private List<AhcsInsuredPersonExtEntity> insuredPresonExList = new ArrayList<>();
    private List<AhcsSpecialPromiseEntity> specialPromiseList = new ArrayList<>();
    private List<AhcsPolicyPlanEntity> policyPlanList = new ArrayList<>();
    private List<AhcsPolicyDutyEntity> policyDutyList = new ArrayList<>();
    private List<AhcsPolicyDutyDetailEntity> policyDutyDetailList = new ArrayList<>();
    private List<AhcsDutyAttributeEntity> dutyAttributeList = new ArrayList<>();
    private List<AhcsDutyAttributeDetailEntity> dutyAttributeDetailList = new ArrayList<>();
    private List<AhcsCoinsureEntity> coinsureList = new ArrayList<>();
    private List<AhcsPolicyHolderEntity> policyHolderList = new ArrayList<>();

    private AcceptRecordDTO acceptRecordDTO;
    private CaseProcessDTO caseProcessDTO;
    private List<CaseClassDTO> caseClassList;
    private List<PolicyClaimCaseDTO> policyClaimCaseList;
    private List<EstimateDutyRecordDTO> estimateDutyRecordList;
    private List<EstimatePolicyDTO> policyList;
    private List<EstimatePlanDTO> planList;
    private List<EstimateDutyDTO> dutyList;
    private ChannelProcessDTO channelProcess;
    private PersonAccidentDTO personAccidentDTO;
    /**
     * 小条款信息集合
     */
    private List<PlanTermContentEntity> planTermContentEntityList;
    private ReportAccidentPetEntity reportAccidentPet;
    private PetInjureDTO petInjureDTO;
    private List<PetInjureExDTO> petInjureExList;

    /**
     * 查询保单的历史核保信息入参
     */
    private List<String> queryUwInfoPolicys;

    private String riskGroupNo;

    private String riskGroupName;

    public ReportDomainDTO() {
    }

    public ReportDomainDTO(TelReportVO telVo) {
        this.telVo = telVo;
    }

    public ReportDomainDTO(OnlineReportVO onlineReportVO) {
        this.onlineReportVO = onlineReportVO;
    }

    public TelReportVO getTelVo() {
        return telVo;
    }
    public OnlineReportVO getOnlineReportVO() {
        return onlineReportVO;
    }

    public void setOnlineReportVO(OnlineReportVO onlineReportVO) {
        this.onlineReportVO = onlineReportVO;
    }

    public void setTelVo(TelReportVO telVo) {
        this.telVo = telVo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public Integer getCaseTimes() {
        return caseTimes;
    }

    public void setCaseTimes(Integer caseTimes) {
        this.caseTimes = caseTimes;
    }

    public String getAcceptDepartmentCode() {
        return acceptDepartmentCode;
    }

    public void setAcceptDepartmentCode(String acceptDepartmentCode) {
        this.acceptDepartmentCode = acceptDepartmentCode;
    }

    public String getReportAcceptUm() {
        return reportAcceptUm;
    }

    public void setReportAcceptUm(String reportAcceptUm) {
        this.reportAcceptUm = reportAcceptUm;
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo;
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public ReportAccidentEntity getReportAccident() {
        return reportAccident;
    }

    public void setReportAccident(ReportAccidentEntity reportAccident) {
        this.reportAccident = reportAccident;
    }

    public ReportAccidentExEntity getReportAccidentEx() {
        return reportAccidentEx;
    }

    public void setReportAccidentEx(ReportAccidentExEntity reportAccidentEx) {
        this.reportAccidentEx = reportAccidentEx;
    }

    public ReportInfoEntity getReportInfo() {
        return reportInfo;
    }

    public void setReportInfo(ReportInfoEntity reportInfo) {
        this.reportInfo = reportInfo;
    }

    public ReportExcEntity getReportExc() {
        return reportExc;
    }

    public void setReportExc(ReportExcEntity reportExc) {
        this.reportExc = reportExc;
    }

    public ReportInfoExEntity getReportInfoExs() {
        return reportInfoExs;
    }

    public void setReportInfoExs(ReportInfoExEntity reportInfoExs) {
        this.reportInfoExs = reportInfoExs;
    }

    public List<CaseBaseEntity> getCaseBases() {
        return caseBases;
    }

    public void setCaseBases(List<CaseBaseEntity> caseBases) {
        this.caseBases = caseBases;
    }

    public List<LinkManEntity> getLinkMans() {
        return LinkMans;
    }

    public void setLinkMans(List<LinkManEntity> linkMans) {
        LinkMans = linkMans;
    }

    public WholeCaseBaseEntity getWholeCaseBase() {
        return wholeCaseBase;
    }

    public void setWholeCaseBase(WholeCaseBaseEntity wholeCaseBase) {
        this.wholeCaseBase = wholeCaseBase;
    }

    public WholeCaseBaseExEntity getWholeCaseBaseEx() {
        return wholeCaseBaseEx;
    }

    public void setWholeCaseBaseEx(WholeCaseBaseExEntity wholeCaseBaseEx) {
        this.wholeCaseBaseEx = wholeCaseBaseEx;
    }

    public ReportCustomerInfoEntity getReportCustomerInfo() {
        return reportCustomerInfo;
    }

    public void setReportCustomerInfo(ReportCustomerInfoEntity reportCustomerInfo) {
        this.reportCustomerInfo = reportCustomerInfo;
    }

    public List<AhcsPolicyDomainDTO> getAhcsPolicyDomainDTOs() {
        return ahcsPolicyDomainDTOs;
    }

    public void setAhcsPolicyDomainDTOs(List<AhcsPolicyDomainDTO> ahcsPolicyDomainDTOs) {
        this.ahcsPolicyDomainDTOs = ahcsPolicyDomainDTOs;
    }

    public AcceptRecordDTO getAcceptRecordDTO() {
        return acceptRecordDTO;
    }

    public void setAcceptRecordDTO(AcceptRecordDTO acceptRecordDTO) {
        this.acceptRecordDTO = acceptRecordDTO;
    }

    public List<AhcsPolicyInfoEntity> getPolicyInfoList() {
        return policyInfoList;
    }

    public void setPolicyInfoList(List<AhcsPolicyInfoEntity> policyInfoList) {
        this.policyInfoList = policyInfoList;
    }

    public List<AhcsInsuredPresonEntity> getInsuredPresonList() {
        return insuredPresonList;
    }

    public void setInsuredPresonList(List<AhcsInsuredPresonEntity> insuredPresonList) {
        this.insuredPresonList = insuredPresonList;
    }

    public List<AhcsInsuredPersonExtEntity> getInsuredPresonExList() {
        return insuredPresonExList;
    }

    public void setInsuredPresonExList(List<AhcsInsuredPersonExtEntity> insuredPresonExList) {
        this.insuredPresonExList = insuredPresonExList;
    }

    public List<AhcsSpecialPromiseEntity> getSpecialPromiseList() {
        return specialPromiseList;
    }

    public void setSpecialPromiseList(List<AhcsSpecialPromiseEntity> specialPromiseList) {
        this.specialPromiseList = specialPromiseList;
    }

    public List<AhcsPolicyPlanEntity> getPolicyPlanList() {
        return policyPlanList;
    }

    public void setPolicyPlanList(List<AhcsPolicyPlanEntity> policyPlanList) {
        this.policyPlanList = policyPlanList;
    }

    public List<AhcsPolicyDutyEntity> getPolicyDutyList() {
        return policyDutyList;
    }

    public void setPolicyDutyList(List<AhcsPolicyDutyEntity> policyDutyList) {
        this.policyDutyList = policyDutyList;
    }

    public List<AhcsPolicyDutyDetailEntity> getPolicyDutyDetailList() {
        return policyDutyDetailList;
    }

    public void setPolicyDutyDetailList(List<AhcsPolicyDutyDetailEntity> policyDutyDetailList) {
        this.policyDutyDetailList = policyDutyDetailList;
    }

    public List<AhcsDutyAttributeEntity> getDutyAttributeList() {
        return dutyAttributeList;
    }

    public void setDutyAttributeList(List<AhcsDutyAttributeEntity> dutyAttributeList) {
        this.dutyAttributeList = dutyAttributeList;
    }

    public List<AhcsDutyAttributeDetailEntity> getDutyAttributeDetailList() {
        return dutyAttributeDetailList;
    }

    public void setDutyAttributeDetailList(List<AhcsDutyAttributeDetailEntity> dutyAttributeDetailList) {
        this.dutyAttributeDetailList = dutyAttributeDetailList;
    }

    public List<AhcsCoinsureEntity> getCoinsureList() {
        return coinsureList;
    }

    public void setCoinsureList(List<AhcsCoinsureEntity> coinsureList) {
        this.coinsureList = coinsureList;
    }

    public List<AhcsPolicyHolderEntity> getPolicyHolderList() {
        return policyHolderList;
    }

    public void setPolicyHolderList(List<AhcsPolicyHolderEntity> policyHolderList) {
        this.policyHolderList = policyHolderList;
    }

    public CaseProcessDTO getCaseProcessDTO() {
        return caseProcessDTO;
    }

    public void setCaseProcessDTO(CaseProcessDTO caseProcessDTO) {
        this.caseProcessDTO = caseProcessDTO;
    }

    public List<CaseClassDTO> getCaseClassList() {
        return caseClassList;
    }

    public void setCaseClassList(List<CaseClassDTO> caseClassList) {
        this.caseClassList = caseClassList;
    }

    public List<PolicyClaimCaseDTO> getPolicyClaimCaseList() {
        return policyClaimCaseList;
    }

    public void setPolicyClaimCaseList(List<PolicyClaimCaseDTO> policyClaimCaseList) {
        this.policyClaimCaseList = policyClaimCaseList;
    }

    public List<EstimateDutyRecordDTO> getEstimateDutyRecordList() {
        return estimateDutyRecordList;
    }

    public void setEstimateDutyRecordList(List<EstimateDutyRecordDTO> estimateDutyRecordList) {
        this.estimateDutyRecordList = estimateDutyRecordList;
    }

    public List<EstimatePolicyDTO> getPolicyList() {
        return policyList;
    }

    public void setPolicyList(List<EstimatePolicyDTO> policyList) {
        this.policyList = policyList;
    }

    public List<EstimatePlanDTO> getPlanList() {
        return planList;
    }

    public void setPlanList(List<EstimatePlanDTO> planList) {
        this.planList = planList;
    }

    public List<EstimateDutyDTO> getDutyList() {
        return dutyList;
    }

    public void setDutyList(List<EstimateDutyDTO> dutyList) {
        this.dutyList = dutyList;
    }

    public ChannelProcessDTO getChannelProcess() {
        return channelProcess;
    }

    public void setChannelProcess(ChannelProcessDTO channelProcess) {
        this.channelProcess = channelProcess;
    }

    public PersonAccidentDTO getPersonAccidentDTO() {
        return personAccidentDTO;
    }

    public void setPersonAccidentDTO(PersonAccidentDTO personAccidentDTO) {
        this.personAccidentDTO = personAccidentDTO;
    }

    public List<PlanTermContentEntity> getPlanTermContentEntityList() {
        return planTermContentEntityList;
    }

    public void setPlanTermContentEntityList(List<PlanTermContentEntity> planTermContentEntityList) {
        this.planTermContentEntityList = planTermContentEntityList;
    }

    public IChongReportVO getiChongReportVO() {
        return iChongReportVO;
    }

    public void setiChongReportVO(IChongReportVO iChongReportVO) {
        this.iChongReportVO = iChongReportVO;
    }

    public ReportAccidentPetEntity getReportAccidentPet() {
        return reportAccidentPet;
    }

    public void setReportAccidentPet(ReportAccidentPetEntity reportAccidentPet) {
        this.reportAccidentPet = reportAccidentPet;
    }

    public PetInjureDTO getPetInjureDTO() {
        return petInjureDTO;
    }

    public void setPetInjureDTO(PetInjureDTO petInjureDTO) {
        this.petInjureDTO = petInjureDTO;
    }

    public List<PetInjureExDTO> getPetInjureExList() {
        return petInjureExList;
    }

    public void setPetInjureExList(List<PetInjureExDTO> petInjureExList) {
        this.petInjureExList = petInjureExList;
    }

    public List<String> getQueryUwInfoPolicys() {
        return queryUwInfoPolicys;
    }

    public void setQueryUwInfoPolicys(List<String> queryUwInfoPolicys) {
        this.queryUwInfoPolicys = queryUwInfoPolicys;
    }

    public String getRiskGroupNo() {
        return riskGroupNo;
    }

    public void setRiskGroupNo(String riskGroupNo) {
        this.riskGroupNo = riskGroupNo;
    }

    public String getRiskGroupName() {
        return riskGroupName;
    }

    public void setRiskGroupName(String riskGroupName) {
        this.riskGroupName = riskGroupName;
    }
}
