<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.follow.ClmsUserFollowCaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.follow.ClmsUserFollowCase">
        <id column="id" property="id" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="user_code" property="userCode" />
        <result column="is_follow" property="isFollow" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <insert id="saveClmsUserFollowCase">
        INSERT INTO clms_user_follow_case(
          id,
          report_no,
          case_times,
          user_code,
          is_follow,
          created_by,
          sys_ctime,
          updated_by,
          sys_utime
        ) VALUES (
          replace(UUID(), '-', ''),
          #{reportNo},
          #{caseTimes},
          #{userCode},
          #{isFollow},
          #{createdBy},
          now(),
          #{updatedBy},
          now()
        )
    </insert>

    <delete id="delClmsUserFollowCase">
        DELETE FROM clms_user_follow_case
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
        AND user_code = #{userCode}
    </delete>

    <select id="getClmsUserFollowCase" resultType="com.paic.ncbs.claim.dao.entity.follow.ClmsUserFollowCase">
        SELECT
        id,
        report_no,
        case_times,
        user_code,
        is_follow
        FROM clms_user_follow_case
        WHERE user_code = #{userCode}
        AND is_follow = '1'
        ORDER BY sys_ctime DESC
    </select>

    <select id="getCaseFollowStatus" resultType="com.paic.ncbs.claim.dao.entity.follow.ClmsUserFollowCase">
        SELECT
        id,
        report_no,
        case_times,
        user_code,
        is_follow
        FROM clms_user_follow_case
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
        AND user_code = #{userCode}
    </select>

</mapper>
