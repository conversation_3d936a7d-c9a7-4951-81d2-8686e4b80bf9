<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO" id="resultTaskInfoList">
        <result column="ID_AHCS_TASK_INFO" property="idAhcsTaskInfo"/>
        <result column="TASK_ID" property="taskId"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="BUSINESS_KEY" property="businessKey"/>
        <result column="TASK_DEFINITION_BPM_KEY" property="taskDefinitionBpmKey"/>
        <result column="ASSIGNER" property="assigner"/>
        <result column="ASSIGNEE_NAME" property="assigneeName"/>
        <result column="CREATED_TIME" property="createdTime"/>
        <result column="ASSIGNEE_TIME" property="assigneeTime"/>
        <result column="COMPLETE_TIME" property="completeTime"/>
        <result column="DEPARTMENT_CODE" property="departmentCode"/>
        <result column="STATUS" property="status"/>
        <result column="END_DATE" property="endDate"/>
        <result column="APPLYER_NAME" property="applyerName"/>
        <result column="APPLYER" property="applyer"/>
        <result column="PRE_TASK_ID" property="preTaskId"/>
        <result column="TASK_GRADE" property="taskGrade"/>
        <result column="AUDIT_GRADE" property="auditGrade"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.taskdeal.ClaimTaskInfoToESVO" id="ClaimESTaskInfoMap">
        <result property="idAhcsTaskInfo" column="idAhcsTaskInfo" jdbcType="VARCHAR"/>
        <result property="taskId" column="taskId" jdbcType="VARCHAR"/>
        <result property="reportNo" column="reportNo" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="caseTimes" jdbcType="NUMERIC"/>
        <result property="taskDefinitionBpmKey" column="taskDefinitionBpmKey" jdbcType="VARCHAR"/>
        <result property="assigner" column="assigner" jdbcType="VARCHAR"/>
        <result property="assigneeName" column="assigneeName" jdbcType="VARCHAR"/>
        <result property="createdDate" column="createdDate" jdbcType="TIMESTAMP"/>
        <result property="updatedDate" column="updatedDate" jdbcType="TIMESTAMP"/>
        <result property="assigneeTime" column="assigneeTime" jdbcType="TIMESTAMP"/>
        <result property="completeTime" column="completeTime" jdbcType="TIMESTAMP"/>
        <result property="departmentCode" column="departmentCode" jdbcType="VARCHAR"/>
        <result property="departmentName" column="departmentName" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="endDate" column="endDate" jdbcType="TIMESTAMP"/>
        <result property="archiveTime" column="archiveTime" jdbcType="TIMESTAMP"/>
        <result property="applyer" column="applyer" jdbcType="VARCHAR"/>
        <result property="applyerName" column="applyerName" jdbcType="VARCHAR"/>
        <result property="taskGrade" column="taskGrade" jdbcType="VARCHAR"/>
        <result property="auditGrade" column="auditGrade" jdbcType="VARCHAR"/>
        <result property="caseLengthDate" column="caseLengthDate" jdbcType="TIMESTAMP"/>
        <result property="idAhcsInvestigate" column="idAhcsInvestigate" jdbcType="VARCHAR"/>
        <result property="idAhcsInvestigateTask" column="idAhcsInvestigateTask" jdbcType="VARCHAR"/>
        <result property="idAhcsInvestigateTaskAudit" column="idAhcsInvestigateTaskAudit" jdbcType="VARCHAR"/>
        <result property="insuredName" column="insuredName" jdbcType="VARCHAR"/>
        <result property="clientType" column="clientType" jdbcType="VARCHAR"/>
        <result property="insuredLevel" column="insuredLevel" jdbcType="VARCHAR"/>
        <result property="personnelAttribute" column="personnelAttribute" jdbcType="VARCHAR"/>
        <result property="estimateAmount" column="estimateAmount" jdbcType="NUMERIC"/>
        <result property="estimateChangeAmount" column="estimateChangeAmount" jdbcType="NUMERIC"/>
        <result property="restartAmount" column="restartAmount" jdbcType="NUMERIC"/>
        <result property="caseNo" column="caseNo" jdbcType="VARCHAR"/>
        <result property="isQuickPay" column="isQuickPay" jdbcType="VARCHAR"/>
        <!-- 关联预估的责任信息-->
        <collection property="taskPolicyInfoList" column="{reportNo=reportNo}" javaType="ArrayList"
                    ofType="com.paic.ncbs.claim.model.vo.taskdeal.ClaimESPolicyInfoVO"
                    select="com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper.getClaimESPolicyInfoVOList"/>
    </resultMap>

    <insert id="addTaskInfo" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        insert into CLMS_TASK_INFO
        (
        ID_AHCS_TASK_INFO,
        CASE_EXPEDITED,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        TASK_ID,
        REPORT_NO,
        CASE_TIMES,
        BUSINESS_KEY,
        TASK_DEFINITION_BPM_KEY,
        ASSIGNER,
        ASSIGNEE_NAME,
        CREATED_TIME,
        ASSIGNEE_TIME,
        COMPLETE_TIME,
        DEPARTMENT_CODE,
        STATUS,
        END_DATE,
        NOTICE_NO,
        archive_time,
        APPLYER_NAME,
        APPLYER,
        PRE_TASK_ID,
        TASK_GRADE,
        AUDIT_GRADE
        )
        values
        (
        #{idAhcsTaskInfo},
        'N',
        #{createdBy},
        #{createdDate},
        #{updatedBy},
        NOW(),
        #{taskId},
        #{reportNo},
        #{caseTimes},
        #{businessKey,jdbcType=VARCHAR},
        #{taskDefinitionBpmKey},
        #{assigner,jdbcType=VARCHAR},
        #{assigneeName,jdbcType=VARCHAR},
        NOW(),
        #{assigneeTime,jdbcType=TIMESTAMP},
        #{completeTime,jdbcType=TIMESTAMP},
        #{departmentCode},
        #{status},
        #{endDate,jdbcType=TIMESTAMP},
        #{noticeNo,jdbcType=VARCHAR},
        NOW(),
        #{applyerName,jdbcType=VARCHAR},
        #{applyer,jdbcType=VARCHAR},
        #{preTaskId,jdbcType=VARCHAR},
        #{taskGrade,jdbcType=INTEGER},
        #{auditGrade,jdbcType=INTEGER}
        )
    </insert>

    <update id="modifyTaskInfoAssigner" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        update CLMS_TASK_INFO ti
        set ti.UPDATED_DATE = NOW(),
        <if test="assigner != null and assigner != '' ">
            ti.ASSIGNER = #{assigner,jdbcType=VARCHAR},
            ti.ASSIGNEE_TIME = NOW(),
        </if>
        <if test="assigneeName != null and assigneeName != '' ">
            ti.ASSIGNEE_NAME = #{assigneeName,jdbcType=VARCHAR},
        </if>
        <if test="completeTime != null">
            ti.COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
        </if>
        <if test="departmentCode != null and departmentCode != '' ">
            ti.DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
        </if>
        <if test="status != null and status != '' ">
            ti.STATUS = #{status,jdbcType=VARCHAR},
        </if>
        <if test="status == null or status == '' ">
            ti.STATUS = CASE WHEN ti.STATUS ='4' THEN '0' ELSE ti.STATUS END,
        </if>
        <if test="endDate != null ">
            ti.END_DATE = #{endDate,jdbcType=TIMESTAMP},
        </if>
        <if test="applyerName != null ">
            ti.APPLYER_NAME = #{applyerName,jdbcType=VARCHAR},
        </if>
        <if test="applyer != null ">
            ti.APPLYER = #{applyer,jdbcType=VARCHAR},
        </if>
        ti.UPDATED_BY = #{updatedBy},
        ti.ES_UPDATED_DATE = NOW(3)
        where ti.TASK_ID = #{taskId}
        <if test="status != null and status != '' ">
            and ti.STATUS NOT IN ('1','2')
        </if>
    </update>

    <update id="modifyTaskInfo" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        update CLMS_TASK_INFO ti
        set ti.UPDATED_DATE = NOW(),
        <if test="assigner != null and assigner != '' ">
            ti.ASSIGNER = #{assigner,jdbcType=VARCHAR},
            ti.ASSIGNEE_TIME = NOW(),
        </if>
        <if test="assigneeName != null and assigneeName != '' ">
            ti.ASSIGNEE_NAME = #{assigneeName,jdbcType=VARCHAR},
        </if>
        <if test="completeTime != null">
            ti.COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
        </if>
        <if test="departmentCode != null and departmentCode != '' ">
            ti.DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
        </if>
        <if test="status != null and status != '' ">
            ti.STATUS = #{status,jdbcType=VARCHAR},
        </if>
        <if test="status == null or status == '' ">
            ti.STATUS = CASE WHEN ti.STATUS ='4' THEN '0' ELSE ti.STATUS END,
        </if>
        <if test="endDate != null ">
            ti.END_DATE = #{endDate,jdbcType=TIMESTAMP},
        </if>
        ti.UPDATED_BY = #{updatedBy},
        ti.ES_UPDATED_DATE = NOW(3)
        where ti.TASK_ID = #{taskId}
        <if test="status != null and status != '' ">
            and ti.STATUS NOT IN ('1','2')
        </if>
        <if test="assigner != null and assigner != '' ">
            and ti.ASSIGNER = #{assigner,jdbcType=VARCHAR}
        </if>
        <if test="applyerName != null ">
            ti.APPLYER_NAME = #{applyerName,jdbcType=VARCHAR},
        </if>
        <if test="applyer != null ">
            ti.APPLYER = #{applyer,jdbcType=VARCHAR},
        </if>
    </update>

    <select id="getIdAhcsTaskInfo" parameterType="string" resultType="string">
        select ID_AHCS_TASK_INFO
        from CLMS_TASK_INFO
        where TASK_ID = #{taskId}
        <if test=' assigner !=null and assigner !="" '>
            and ASSIGNER = #{assigner}
        </if>
        <if test=' taskDefinitionBpmKey !=null and taskDefinitionBpmKey !="" '>
            AND TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey}
        </if>

    </select>

    <select id="getOutInvestigateTaskInfo" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO"
            resultType="com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO">
        select
            t.REPORT_NO,
            (@rownum := @rownum +1) AS ORDER_NO,
            t.COMPLETE_TIME
        FROM clms_task_info t,(SELECT @rownum := 0) AS rn
        where TASK_DEFINITION_BPM_KEY ='OC_INVESTIGATE_APPROVAL'
            AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
            <if test="caseTimes != null and caseTimes != '' ">
                AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
            </if>
        order by created_date
    </select>
    
    <select id="getEntrustmentTaskInfo" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO"
            resultType="com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO">
        select
            t.REPORT_NO,
            t.BUSINESS_KEY AS ORDER_NO,
            t.COMPLETE_TIME
        FROM clms_task_info t
        where TASK_DEFINITION_BPM_KEY ='OC_ENTRUSTMENT_APPROVAL'
            AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
            <if test="caseTimes != null and caseTimes != '' ">
                AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
            </if>
        order by created_date
    </select>

    <select id="getTaskInfo" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO"
            resultType="com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO">
        SELECT
        T.REPORT_NO reportNo,
        T.CASE_TIMES caseTimes,
        T.TASK_ID taskId,
        T.TASK_DEFINITION_BPM_KEY taskDefinitionBpmKey,
        T.CREATED_TIME startDealTime,
        T.ASSIGNEE_TIME assignTime,
        T.ASSIGNER assignee,
        T.ASSIGNEE_NAME assigneeName,
        T.COMPLETE_TIME completeTime,
        T.DEPARTMENT_CODE departmentCode,
        (SELECT DD.DEPARTMENT_ABBR_NAME FROM DEPARTMENT_DEFINE DD WHERE DD.DEPARTMENT_CODE = T.DEPARTMENT_CODE) departmentName,
        (SELECT DD.DEPARTMENT_ABBR_NAME FROM DEPARTMENT_DEFINE DD WHERE DD.DEPARTMENT_CODE =
        (SELECT P.DEPARTMENT_CODE FROM CLMS_POLICY_INFO P WHERE P.REPORT_NO = TRIM(T.REPORT_NO) limit 1)) policyDepartmentName,
        (select case when a.business_type='P' then '个人' else '团体' end from clms_policy_info a where a.report_no=t.report_no limit 1) policyType,
        T.APPLYER_NAME applyerName ,
        T.APPLYER applyer,
        (select cpi.POLICY_NO from clms_policy_info cpi where cpi.report_no = T.REPORT_NO limit 1) policyNo,
        case when t.TASK_DEFINITION_BPM_KEY in ('OC_REPORT_TRACK','OC_CHECK_DUTY','OC_MANUAL_SETTLE','OC_SETTLE_REVIEW') then
        CASE
        WHEN EXISTS (
        SELECT 1
        FROM clms_task_info t1
        WHERE t1.report_no = t.report_no
        AND t1.case_times = t.case_times
        AND t1.TASK_DEFINITION_BPM_KEY != t.TASK_DEFINITION_BPM_KEY
        and t1.STATUS != '1'
        ) THEN
        CASE
        WHEN EXISTS (
        SELECT 1
        FROM clms_task_info t1
        JOIN clms_task_conflict ctc
        ON ctc.conflict_task_key = t1.TASK_DEFINITION_BPM_KEY
        AND ctc.TASK_DEFINITION_BPM_KEY = t.TASK_DEFINITION_BPM_KEY
        AND ctc.plan_operation = '1'
        AND ctc.constraint_type = '1'
        WHERE t1.report_no = t.report_no
        AND t1.case_times = t.case_times
        AND t1.TASK_DEFINITION_BPM_KEY != t.TASK_DEFINITION_BPM_KEY
        AND t1.status in ('0','3')
        ) THEN '10'
        ELSE '11'
        END
        ELSE '00' end
        else '00' end as subProcessFlag,
        T.BUSINESS_KEY
        FROM CLMS_TASK_INFO T
        WHERE
        T.STATUS in ('0','3')
        <if test=" reportNo != null and reportNo !='' ">
            AND T.REPORT_NO = TRIM(#{reportNo})
        </if>
        <if test=" assigner !=null and assigner !='' ">
            AND T.ASSIGNER = TRIM(#{assigner})
        </if>
        <if test=" assigneeName !=null and assigneeName !='' ">
            AND T.ASSIGNEE_NAME = #{assigneeName}
        </if>
        <if test=' taskDefinitionBpmKey !=null and taskDefinitionBpmKey !="" '>
            AND T.TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey}
        </if>
        <if test=' departmentCode != null and departmentCode != "" '>
            AND T.DEPARTMENT_CODE = #{departmentCode}
        </if>
        <if test='departmentCodes != null and departmentCodes != "" '>
            and T.DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='riskGroupName != null and riskGroupName != "" '>
            AND exists (select 1 from clms_policy_info cpi,ply_risk_group prg where cpi.report_no = T.REPORT_NO and cpi.policy_no = prg.POLICY_NO and prg.RISK_GROUP_NAME=#{riskGroupName})
        </if>
        <if test='productCode != null and productCode != "" '>
            AND exists (select 1 from clms_policy_info cpi where cpi.report_no = T.REPORT_NO and cpi.product_code =#{productCode})
        </if>
        <if test='isQuickPay != null and isQuickPay != "" '>
                AND exists (select 1 from clms_report_info_ex cri where cri.report_no = T.REPORT_NO and cri.is_quick_pay = '1')
        </if>
        ORDER BY CASE
        WHEN assignee IS NULL THEN
        0
        ELSE
        1
        END,
        startDealTime
    </select>

    <select id="getTaskRecord" resultType="java.lang.Integer">
        SELECT count(1)
        FROM CLMS_TASK_INFO t
        JOIN (
        SELECT REPORT_NO, MAX(COMPLETE_TIME) AS MAX_COMPLETE_TIME
        FROM CLMS_TASK_INFO
        WHERE TASK_DEFINITION_BPM_KEY = 'OC_MANUAL_SETTLE'
        AND REPORT_NO = #{reportNo}
        GROUP BY REPORT_NO
        ) t1 ON t.REPORT_NO = t1.REPORT_NO
        WHERE t.REPORT_NO = #{reportNo}
        AND t.ASSIGNER = #{assigner}
        AND t.CASE_TIMES = #{caseTimes}
        AND t.STATUS = '1'
        AND t.TASK_DEFINITION_BPM_KEY = 'OC_SETTLE_REVIEW'
        AND t.COMPLETE_TIME > t1.MAX_COMPLETE_TIME
    </select>

    <select id="getTaskList" resultType="java.util.HashMap">
        select CONCAT(t.report_no,'_',t.case_times) "reportNo",
               (select a.name from CLMS_report_customer a where a.report_no=t.report_no) "insuredName",
               '否' as "caseSource",
               '待处理' as "taskStatus",
               DATE_FORMAT(t.assignee_time,'%Y-%m-%d %H:%i:%s') "claimTime",
               '标准件' as "caseType",
               '人伤-医疗' as "caseClass",
               t.task_id "taskId",
               (select t0.id_ahcs_investigate from CLMS_investigate t0 where t0.report_no=t.report_no and t0.investigate_status != '4'  limit 0,1) "idAhcsInvestigate",
               (select t1.id_ahcs_investigate_task from CLMS_investigate_task t1 where t1.report_no=t.report_no and t1.task_status in ('1','4') limit 0,1) "idAhcsInvestigateTask" ,
               t.APPLYER_NAME applyerName ,
               t.APPLYER applyer
        from CLMS_task_info t
        where t.task_definition_bpm_key = #{defKey,jdbcType=VARCHAR}
          and t.assigner = #{uid,jdbcType=VARCHAR}
          and t.status = '0'
        order by assignee_time desc
    </select>

    <select id="getTaskCount" resultType="java.lang.Integer">
        select count(1)
        from CLMS_task_info t
        where t.assigner = 'ADMIN'
          and t.status = '0'
          and t.task_definition_bpm_key = #{defKey,jdbcType=VARCHAR}
    </select>

   <!-- <update id="updateNoticeNoByBusinessKey">
        <foreach collection="paymentNoticeList" item="item" open="begin" separator=";" close=";end;">
            UPDATE CLMS_task_info T
            SET T.UPDATED_DATE = NOW(),
            T.notice_no=#{item.noticeNo},
            T.ES_UPDATED_DATE = NOW()
            WHERE T.business_key=#{item.idClmPaymentNotice}
        </foreach>
    </update>-->

    <select id="getNoFinishScoreTask"  resultType="string">
        select
        IFNULL(assigner, 'system')
        from CLMS_TASK_INFO
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
        and task_definition_bpm_key ='AHCS_SCORE_PROCESS'
        and END_DATE is null
    </select>

    <select id="getMajoyProcessDepartmentByReport" resultType="string">
        select department_code
        from CLMS_task_info
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
        and status != '1'
        and department_code != '2'
        and task_definition_bpm_key in
        ('AHCS_CHASE_VERIFY', 'AHCS_CHECK_DUTY', 'AHCS_FIELD_SURVEY',
        'AHCS_MANUAL_SETTLE', 'AHCS_MULTI_CLAIM_APPLY_VERIFY',
        'AHCS_SETTLE_REVIEW', 'AHCS_TEL_SURVEY')

    </select>

    <select id="findLatestByReportNoAndBpmKey" resultMap="resultTaskInfoList">
        select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_TASK_INFO,
        TASK_ID,
        REPORT_NO,
        CASE_TIMES,
        BUSINESS_KEY,
        TASK_DEFINITION_BPM_KEY,
        ASSIGNER,
        CREATED_TIME,
        ASSIGNEE_TIME,
        COMPLETE_TIME,
        DEPARTMENT_CODE,
        STATUS,
        END_DATE,
        APPLYER_NAME,
        ASSIGNEE_NAME,
        APPLYER
        from CLMS_TASK_INFO
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
        and TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey}
        and UPDATED_DATE=(select max(UPDATED_DATE) from CLMS_TASK_INFO
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
        and TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey})
        limit 1
    </select>

    <select id="findTaskByReportNoAndBpmKey" resultMap="resultTaskInfoList">
        select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_TASK_INFO,
        TASK_ID,
        REPORT_NO,
        CASE_TIMES,
        BUSINESS_KEY,
        TASK_DEFINITION_BPM_KEY,
        ASSIGNER,
        CREATED_TIME,
        ASSIGNEE_TIME,
        COMPLETE_TIME,
        DEPARTMENT_CODE,
        STATUS,
        END_DATE,
        APPLYER_NAME,
        ASSIGNEE_NAME,
        APPLYER
        from CLMS_TASK_INFO cti
        where REPORT_NO = #{reportNo}
        and TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey}
        and cti.STATUS != 1
    </select>

    <update id="modifyTaskInfoByDefKey">
        update CLMS_TASK_INFO t
        set t.UPDATED_DATE = NOW(),
            t.UPDATED_BY = #{updatedBy},
            t.END_DATE = NOW(),
            t.COMPLETE_TIME = NOW(),
            t.STATUS = #{status,jdbcType=VARCHAR},
            t.ES_UPDATED_DATE = NOW(3)
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        <if test="taskDefinitionBpmKey != null">
            and t.task_definition_bpm_key = #{taskDefinitionBpmKey}
        </if>
        <if test="taskId != null  and taskId !='' ">
            and t.TASK_ID = #{taskId}
        </if>
        and t.STATUS != 1
    </update>

    <update id="suspendOrActiveTask">
        update CLMS_TASK_INFO t
        <set>
        <if test="assigner != null and assigner !=''">
            t.assigner = #{assigner},
            t.ASSIGNEE_TIME = NOW(),
        </if>
        <if test="assigneeName != null and assigneeName !=''">
            t.ASSIGNEE_NAME = #{assigneeName},
        </if>
        <if test="departmentCode != null and departmentCode !=''">
            t.department_code = #{departmentCode},
        </if>
        t.UPDATED_DATE = NOW(),
        t.UPDATED_BY = #{updatedBy},
        t.END_DATE = null,
        t.COMPLETE_TIME = null,
        t.STATUS = #{status,jdbcType=VARCHAR},
        t.ES_UPDATED_DATE = NOW(3)
        </set>
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.task_definition_bpm_key = #{taskDefinitionBpmKey}
        <if test="taskId != null  and taskId !='' ">
            and t.TASK_ID = #{taskId}
        </if>
        <if test="taskDefinitionBpmKey != null and taskDefinitionBpmKey == 'OC_SETTLE_REVIEW' ">
            and t.STATUS != 1
        </if>
    </update>

    <update id="suspendNotUpdateDate">
        update CLMS_TASK_INFO t
        set t.STATUS = #{status,jdbcType=VARCHAR},
            t.ES_UPDATED_DATE = NOW(3)
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.task_definition_bpm_key = #{taskDefinitionBpmKey}
        <if test="taskId != null  and taskId !='' ">
            and t.TASK_ID = #{taskId}
        </if>
    </update>

    <select id="getConclusion" resultType="java.lang.String">
        select t.indemnity_conclusion
        from clm_whole_case_base t
        where t.report_no=#{reportNo}
        and t.case_times=#{caseTimes}
    </select>

    <select id="getTaskInfoForInvestigate" resultMap="resultTaskInfoList">
        select CREATED_BY,
               CREATED_DATE,
               UPDATED_BY,
               UPDATED_DATE,
               ID_AHCS_TASK_INFO,
               TASK_ID,
               REPORT_NO,
               CASE_TIMES,
               BUSINESS_KEY,
               TASK_DEFINITION_BPM_KEY,
               ASSIGNER,
               CREATED_TIME,
               ASSIGNEE_TIME,
               COMPLETE_TIME,
               DEPARTMENT_CODE,
               STATUS,
               END_DATE,
               APPLYER_NAME,
               APPLYER
        from CLMS_TASK_INFO t
        where t.report_no = #{reportNo,jdbcType=VARCHAR}
          and t.case_times = #{caseTimes,jdbcType=NUMERIC}
          and t.status not in ('1', '2')
          and t.task_definition_bpm_key in
              ('AHCS_CHECK_DUTY', 'AHCS_MANUAL_SETTLE', 'AHCS_SETTLE_REVIEW')
          limit 1
    </select>

    <select id="hasNotFinishTaskByTaskKey" resultType="java.lang.Integer">
        select COUNT(1) from CLMS_task_info t
        where t.report_no=#{reportNo}
        and t.case_times=#{caseTimes}
        and t.task_definition_bpm_key = #{taskKey}
        and t.status != 1
        <if test=" taskId != null and taskId !='' ">
            AND t.task_id = #{taskId}
        </if>
    </select>

    <select id="getSuspendTaskCount" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO"
            resultType="java.lang.Integer">
        select COUNT(1)
        FROM CLMS_TASK_INFO T
        WHERE
        T.STATUS = '2'
        <if test=" reportNo != null and reportNo !='' ">
            AND T.REPORT_NO = TRIM(#{reportNo})
        </if>
        <if test=" caseTimes != null ">
            AND T.CASE_TIMES = #{caseTimes}
        </if>
        <if test=' taskDefinitionBpmKey !=null and taskDefinitionBpmKey !="" '>
            AND T.TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey}
        </if>
        <if test=' departmentCode != null and departmentCode != "" '>
            AND T.DEPARTMENT_CODE = #{departmentCode}
        </if>
        <if test='departCodeList != null and departCodeList != "" '>
            and T.DEPARTMENT_CODE in
            <foreach item="item" collection="departCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getPendingTaskCount" resultType="java.lang.String">
        select T.TASK_DEFINITION_BPM_KEY
        FROM CLMS_TASK_INFO T
        WHERE
            T.STATUS = '0'
            AND T.REPORT_NO = TRIM(#{reportNo})
            AND T.CASE_TIMES = #{caseTimes}
            AND T.TASK_DEFINITION_BPM_KEY IN (
            'OC_REPORT_TRACK',
            'OC_REGISTER_REVIEW',
            'OC_CHECK_DUTY',
            'OC_MANUAL_SETTLE',
            'OC_SETTLE_REVIEW'
            )
    </select>


    <update id="reAssign">
        update CLMS_task_info t
        set t.assigner=#{userId} ,
            t.assignee_name = #{userName},
            t.DEPARTMENT_CODE = #{comCode},
            t.updated_date=now(),
            t.assignee_time=now(),
            t.ES_UPDATED_DATE = NOW(3)
        where t.task_id=#{taskId}
          and t.status in ('0','3');
    </update>

    <update id="updateTask">
        update CLMS_task_info t
        set t.status=#{status} ,
            t.updated_date=now(),
            t.COMPLETE_TIME = null ,
            t.END_DATE = null,
            t.ES_UPDATED_DATE = NOW(3)
        where t.task_id=#{taskId}
    </update>

    <update id="updateTaskReback">
        update CLMS_task_info t
        set t.status=#{status} ,
            t.updated_date=now(),
            t.assigner = '',
            t.ES_UPDATED_DATE =NOW(3)
        where t.task_id=#{taskId}
    </update>

    <select id="getNoFinishTaskCount" resultType="Integer">
        select
            count(1)
        from CLMS_TASK_INFO
        where ASSIGNER = TRIM(#{userId})
        and DEPARTMENT_CODE = #{comCode}
        and STATUS = '0'
        and END_DATE is null
    </select>

    <select id="ownNewSuspendProcess" resultType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO" parameterType="list">
        select  *  from  clms_task_info  where   `REPORT_NO`  = #{reportNo} and  `CASE_TIMES`  =#{caseTimes}
        and `TASK_DEFINITION_BPM_KEY`  in
            <foreach collection="caseProcess" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
             and status = #{status}
        order by  FIELD(`TASK_DEFINITION_BPM_KEY`,
        <foreach collection="caseProcess" item="item"  separator=",">
            #{item}
        </foreach>
            )  limit 1
    </select>

    <select id="getWorkBenchTaskList" parameterType="com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskQueryVO"
            resultType="com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO">
        SELECT
            dd.DEPARTMENT_ABBR_NAME AS "policyDepartmentName",
            t.TASK_ID AS "taskId",
            t.report_no AS "reportNo",
            t.APPLYER_NAME AS "submitName",
            t.APPLYER AS "applyer",
            t.case_times AS "caseTimes",
            a.name AS "insuredName",
            CASE a.client_type WHEN '5' THEN 'Y' ELSE 'N' END AS "insuredLevel",
            a.client_cluster AS "personnelAttribute",
            CASE
            WHEN t.TASK_DEFINITION_BPM_KEY IN ('OC_RESTART_CASE_APPROVAL', 'OC_RESTART_CASE_MODIFY') THEN
            TIMESTAMPDIFF(HOUR, crc.UPDATED_DATE, NOW())
            ELSE
            TIMESTAMPDIFF(HOUR, b.REPORT_DATE, NOW())
            END AS "caseLength",
            DATEDIFF(NOW(), t.CREATED_DATE) AS "currentLength",
            cer.ESTIMATE_AMOUNT AS "estimateAmount",
<!--            SUM(cec.CHANGE_AMOUNT) AS "estimateChangeAmount",-->
            cec.CHANGE_AMOUNT AS "estimateChangeAmount",
            cr.RESTART_AMOUNT AS "restartAmount",
            t.ASSIGNEE_TIME AS "assigneeTime",
            t.TASK_DEFINITION_BPM_KEY AS "taskDefinitionBpmKey",
            CASE WHEN t.ASSIGNER = TRIM(#{userCode}) THEN 'Y' ELSE 'N' END AS "operate",
            t.TASK_ID AS "idAhcsCommunicateBase",
            CASE
            WHEN t.TASK_DEFINITION_BPM_KEY IN ('OC_SETTLE_REVIEW', 'OC_REGISTER_REVIEW', 'OC_ESTIMATE_CHANGE_REVIEW') THEN
            CASE t.TASK_GRADE
            WHEN 1 THEN '一级' WHEN 2 THEN '二级' WHEN 3 THEN '三级' WHEN 4 THEN '四级'
            WHEN 5 THEN '五级' WHEN 6 THEN '六级' WHEN 7 THEN '七级' WHEN 8 THEN '八级' WHEN 9 THEN '九级'
            END
            END AS taskGrade,
            CASE
            WHEN t.TASK_DEFINITION_BPM_KEY = 'OC_SETTLE_REVIEW' THEN
            CASE t.AUDIT_GRADE
            WHEN 1 THEN '一级' WHEN 2 THEN '二级' WHEN 3 THEN '三级' WHEN 4 THEN '四级'
            WHEN 5 THEN '五级' WHEN 6 THEN '六级' WHEN 7 THEN '七级' WHEN 8 THEN '八级' WHEN 9 THEN '九级'
            END
            END AS auditGrade,
            case t.TASK_DEFINITION_BPM_KEY when 'OC_INVESTIGATE_APPROVAL'
            then t.TASK_ID
            else (select t0.id_ahcs_investigate from CLMS_investigate t0 where t0.report_no=t.report_no and
            t0.investigate_status in ('1','2','3') and    t0.operate = 1 limit 0,1) end idAhcsInvestigate ,
            case t.TASK_DEFINITION_BPM_KEY
            when 'OC_MAJOR_INVESTIGATE'
            then t.TASK_ID
            else (select t1.id_ahcs_investigate_task from CLMS_investigate_task t1 where t1.report_no=t.report_no and
            t1.task_status in ('1','4') limit 0,1) end idAhcsInvestigateTask ,
            CASE WHEN t.TASK_DEFINITION_BPM_KEY = 'OC_INVESTIGATE_REVIEW' THEN t.TASK_ID END AS idAhcsInvestigateTaskAudit,
            cpi.PRODUCT_NAME AS productName,
            GROUP_CONCAT(DISTINCT cip.SCHEME_NAME SEPARATOR ';') AS riskGroupName,
            cpi.POLICY_NO AS policyNo,
        case when t.TASK_DEFINITION_BPM_KEY in ('OC_REPORT_TRACK','OC_CHECK_DUTY','OC_MANUAL_SETTLE','OC_SETTLE_REVIEW') then
        CASE
        WHEN EXISTS (
        SELECT 1
        FROM clms_task_info t1
        WHERE t1.report_no = t.report_no
        AND t1.case_times = t.case_times
        AND t1.TASK_DEFINITION_BPM_KEY != t.TASK_DEFINITION_BPM_KEY
        and t1.STATUS !='1'
        ) THEN
        CASE
        WHEN EXISTS (
        SELECT 1
        FROM clms_task_info t1
        JOIN clms_task_conflict ctc
        ON ctc.conflict_task_key = t1.TASK_DEFINITION_BPM_KEY
        AND ctc.TASK_DEFINITION_BPM_KEY = t.TASK_DEFINITION_BPM_KEY
        AND ctc.plan_operation = '1'
        AND ctc.constraint_type = '1'
        WHERE t1.report_no = t.report_no
        AND t1.case_times = t.case_times
        AND t1.TASK_DEFINITION_BPM_KEY != t.TASK_DEFINITION_BPM_KEY
        and t1.status in ('0','3')
        ) THEN '10'
        ELSE '11'
        END
        ELSE '00' end
        else '00' end as subProcessFlag,
        t.business_key as businessKey
        FROM clms_task_info t
        LEFT JOIN clms_report_customer a ON a.report_no = t.report_no
        LEFT JOIN clm_report_info b ON b.report_no = t.report_no
        LEFT JOIN clms_policy_info cpi ON cpi.REPORT_NO = t.REPORT_NO
        LEFT JOIN clms_insured_person cip ON cpi.ID_AHCS_POLICY_INFO = cip.ID_AHCS_POLICY_INFO
        LEFT JOIN department_define dd ON dd.DEPARTMENT_CODE = t.DEPARTMENT_CODE
        LEFT JOIN clm_restart_case_record crc ON crc.report_no = t.report_no AND crc.case_times = t.case_times
        LEFT JOIN clms_estimate_record cer ON cer.report_no = t.report_no AND cer.case_times = t.case_times AND cer.ESTIMATE_TYPE = '02'
        LEFT JOIN clms_estimate_change cec ON cec.report_no = t.report_no AND cec.case_times = t.case_times AND cec.IS_EFFECTIVE = 'Y'
        LEFT JOIN clm_restart_case_record cr ON cr.report_no = t.report_no AND cr.case_times = t.case_times
        LEFT JOIN CLMS_investigate ci ON ci.report_no = t.report_no AND ci.investigate_status IN ('1', '2', '3') AND ci.operate = 1
        LEFT JOIN CLMS_investigate_task cit ON cit.report_no = t.report_no AND cit.task_status IN ('1', '4')
        WHERE t.status IN ('0', '3')
        <if test="reportNo != null and reportNo != ''">
            AND t.REPORT_NO = TRIM(#{reportNo})
        </if>
        <if test="insuredName != null and insuredName != ''">
            AND a.name = #{insuredName}
        </if>
        <if test="caseNo != null and caseNo != ''">
            AND t.REPORT_NO IN (SELECT d.REPORT_NO FROM clm_case_base d WHERE d.CASE_NO = TRIM(#{caseNo}))
        </if>
        <if test="policyNo != null and policyNo != ''">
            AND t.REPORT_NO IN (SELECT e.REPORT_NO FROM clms_policy_info e WHERE e.POLICY_NO = TRIM(#{policyNo}))
        </if>
        <if test="taskDefinitionBpmKey != null and taskDefinitionBpmKey != ''">
            AND t.TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey}
        </if>
        <if test='isMyCase == "Y"'>
            AND t.ASSIGNER = TRIM(#{userCode})
        </if>
        <if test='isMyCase != "Y"'>
            AND (t.ASSIGNER = '' OR t.ASSIGNER IS NULL)
            <if test='taskDefinitionBpmKeys != null and taskDefinitionBpmKeys.size() > 0'>
                AND t.TASK_DEFINITION_BPM_KEY IN
                <foreach collection="taskDefinitionBpmKeys" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test='taskDefinitionBpmKeys == null or taskDefinitionBpmKeys.size() == 0'>
                AND t.TASK_DEFINITION_BPM_KEY IS NULL
            </if>
        </if>
        <if test='isIncludeSubordinates != "Y"'>
            AND t.DEPARTMENT_CODE = #{departmentCode}
        </if>
        <if test="assignStartTime != null and assignEndTime != null">
            AND DATE_FORMAT(t.ASSIGNEE_TIME, '%Y-%m-%d') BETWEEN DATE_FORMAT(#{assignStartTime}, '%Y-%m-%d') AND DATE_FORMAT(#{assignEndTime}, '%Y-%m-%d')
        </if>
        <if test='isIncludeSubordinates == "Y"'>
            AND t.DEPARTMENT_CODE IN
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='auditGrade != null and auditGrade != ""'>
            AND t.AUDIT_GRADE = #{auditGrade}
        </if>
        <if test='packageName != null and packageName != ""'>
            AND cip.SCHEME_NAME = #{packageName}
        </if>
        <if test='productCode != null and productCode != ""'>
            AND cpi.product_code = #{productCode}
        </if>
        <if test='isQuickPay != null and isQuickPay != ""'>
            AND EXISTS (SELECT 1 FROM clms_report_info_ex cri WHERE cri.report_no = t.REPORT_NO AND cri.is_quick_pay = '1')
        </if>
        <if test='businessNo != null and businessNo != ""'>
            AND (t.REPORT_NO = TRIM(#{businessNo}) or a.name = #{businessNo} or cpi.POLICY_NO = TRIM(#{businessNo}))
        </if>

        GROUP BY
            t.report_no,
            t.TASK_DEFINITION_BPM_KEY,
            case
                when t.TASK_DEFINITION_BPM_KEY in ('OC_PAY_BACK_MODIFY_REVIEW', 'OC_PAY_BACK_MODIFY','OC_FEE_INVOICE_MODIFY')
                    then t.task_id
                when t.TASK_DEFINITION_BPM_KEY in ('OC_QUALITY_REVIEW', 'OC_QUALITY_CHECK_REVIEW')
                    then t.task_id
                when t.TASK_DEFINITION_BPM_KEY = 'OC_BLACK_LIST'
                    then t.ID_AHCS_TASK_INFO
                when t.TASK_DEFINITION_BPM_KEY = 'OC_REPLEVY_FEE_REVIEW'
                    then t.business_key
                when t.TASK_DEFINITION_BPM_KEY in ('OC_LITIGATION_APPROVAL', 'OC_LITIGATION_WAS_CONCLUDED')
                    then t.ID_AHCS_TASK_INFO
                else null
            end
        ORDER BY caseTimes DESC, insuredLevel DESC, caseLength DESC, assignee_time
    </select>

    <select id="historyDocumentFullDate" resultType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        select CREATED_BY,
               CREATED_DATE,
               UPDATED_BY,
               UPDATED_DATE,
               ID_AHCS_TASK_INFO,
               TASK_ID,
               REPORT_NO,
               CASE_TIMES,
               BUSINESS_KEY,
               TASK_DEFINITION_BPM_KEY,
               ASSIGNER,
               CREATED_TIME,
               ASSIGNEE_TIME,
               COMPLETE_TIME,
               DEPARTMENT_CODE,
               STATUS,
               END_DATE,
               APPLYER_NAME,
               APPLYER
          from CLMS_TASK_INFO
          where REPORT_NO = #{reportNo}
          and CASE_TIMES = #{caseTimes}
          and TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey}
          and  status = 1
        limit 1
    </select>

    <select id="getReportNoById" resultType="java.lang.String">
        select report_no
        from clms_task_info
        where task_id = #{taskId,jdbcType=VARCHAR}
    </select>

    <select id="getPolicyHolder" resultType="java.lang.String">
        select distinct b.name from clms_policy_info a ,clms_policy_holder b
        where a.id_ahcs_policy_info=b.id_ahcs_policy_info
        and a.report_no = #{reportNo}
    </select>

    <select id="getAccidentDate" resultType="java.util.Date">
        select accident_date
        from  clm_report_accident
        where report_no = #{reportNo}
    </select>

    <select id="getAccidentType" resultType="java.lang.String">
        select case_sub_class
        from clms_case_class
        where report_no = #{reportNo}
        and case_times = 1
        and task_id ='report1'
        and status ='1'
        limit 1
    </select>

    <select id="selectTaskByTaskId" resultMap="resultTaskInfoList">
        select CREATED_BY,
               CREATED_DATE,
               UPDATED_BY,
               UPDATED_DATE,
               ID_AHCS_TASK_INFO,
               TASK_ID,
               REPORT_NO,
               CASE_TIMES,
               BUSINESS_KEY,
               TASK_DEFINITION_BPM_KEY,
               ASSIGNER,
               CREATED_TIME,
               ASSIGNEE_TIME,
               COMPLETE_TIME,
               DEPARTMENT_CODE,
               STATUS,
               END_DATE,
               APPLYER_NAME,
               APPLYER,
               PRE_TASK_ID ,
               TASK_GRADE ,
               AUDIT_GRADE
        from CLMS_TASK_INFO
        where TASK_ID = #{taskId}
        limit 1
    </select>

    <select id="getAssigner" resultType="java.lang.String">
        select concat(ASSIGNER,concat('-',ASSIGNEE_NAME))
        from clms_task_info
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and CASE_TIMES  = #{caseTimes,jdbcType=NUMERIC}
        and STATUS = 0
        order by CREATED_DATE desc
        limit 1
    </select>

    <select id="getTaskDtoByTaskId" resultType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        select
        TASK_ID taskId,
        REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        TASK_DEFINITION_BPM_KEY taskDefinitionBpmKey,
        ASSIGNER assigner,
        ASSIGNEE_NAME assigneeName,
        DEPARTMENT_CODE departmentCode,
        APPLYER applyer,
        APPLYER_NAME applyerName,
        PRE_TASK_ID preTaskId,
        TASK_GRADE taskGrade,
        STATUS status,
        PRE_TASK_ID preTaskId,
        AUDIT_GRADE auditGrade,
        BUSINESS_KEY businessKey
        from CLMS_TASK_INFO
        where TASK_ID = #{taskId,jdbcType=VARCHAR}
    </select>

    <update id="updateTaskDtoByTaskId">
        update CLMS_TASK_INFO
        set UPDATED_BY = #{userId,jdbcType=VARCHAR},
            UPDATED_DATE = now(),
            STATUS = '0',
            ES_UPDATED_DATE = NOW(3)
        where TASK_ID = #{taskId,jdbcType=VARCHAR}
    </update>

    <select id="checkWorkflow" resultType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        select  *  from  clms_task_info  where   `REPORT_NO`  = #{reportNo} and  `CASE_TIMES`  =#{caseTimes}
        and `TASK_DEFINITION_BPM_KEY` = #{bpmConstants} and status = #{status}
    </select>

    <select id="getTaskAssignerName"  resultType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        select ASSIGNEE_NAME assigneeName,ASSIGNER from clms_task_info
        where REPORT_NO=#{reportNo}
        and case_times=#{caseTimes}
        and TASK_DEFINITION_BPM_KEY= #{taskKey}
        and status='1'
    </select>
    <update id="updateTaskAssigner" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        update clms_task_info
        set ASSIGNER=#{assigner},
            ASSIGNEE_NAME=#{assigneeName},
            <if test="updatedBy != null and updatedBy !=''">
                UPDATED_BY=#{updatedBy},
            </if>
            UPDATED_DATE=#{updatedDate},
            ES_UPDATED_DATE = NOW(3)
        where REPORT_NO=#{reportNo}
        and case_times=#{caseTimes}
        and status=#{status}
        and TASK_DEFINITION_BPM_KEY=#{taskDefinitionBpmKey}
    </update>
    <select id="getTpaTaskInfo" resultType="java.lang.Integer">
        select count(*)  from CLMS_task_info t
        where t.report_no=#{reportNo}
        and t.case_times=#{caseTimes}
    </select>

    <select id="getTaskIsTPA"  resultType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        select ASSIGNEE_NAME assigneeName,ASSIGNER from clms_task_info
        where REPORT_NO=#{reportNo}
        and case_times=#{caseTimes}
        and TASK_DEFINITION_BPM_KEY= #{taskKey}
    </select>

    <select id="getTaskId" resultType="java.lang.String">
        select TASK_ID as taskId
            from clms_task_info a
        where a.report_no = #{reportNo}
        and a.case_times = #{caseTimes}
        and a.status = '0'
        and a.TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey}
    </select>

    <delete id="deleteTaskInfo" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        delete from clms_task_info
        where REPORT_NO=#{reportNo}
          and case_times=#{caseTimes}
          and TASK_DEFINITION_BPM_KEY= #{taskDefinitionBpmKey}
    </delete>

    <delete id="deleteQualityTaskInfo" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        delete from clms_task_info
        where status=#{status}
        and TASK_ID=#{taskId}
        and TASK_DEFINITION_BPM_KEY= #{taskDefinitionBpmKey}
    </delete>
    <!--收单完成时间 -->
    <select id="getCompleteDate" resultType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        select COMPLETE_TIME from clms_task_info where report_no=#{reportNo}
        and TASK_DEFINITION_BPM_KEY='OC_CHECK_DUTY' and STATUS='1'
    </select>
    <!--获取主任务挂起后，未处理的任务信息 -->
    <select id="getUndoTaskInfoList" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO"
            resultType="com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO">
        select  REPORT_NO reportNo,
                CASE_TIMES caseTimes,
                TASK_ID taskId,
                TASK_DEFINITION_BPM_KEY taskDefinitionBpmKey,
                CREATED_TIME startDealTime,
                ASSIGNEE_TIME assignTime,
                ASSIGNER assignee,
                ASSIGNEE_NAME assigneeName,
                COMPLETE_TIME completeTime,
                DEPARTMENT_CODE departmentCode,
                APPLYER_NAME applyerName ,
                APPLYER applyer,
                STATUS status
        FROM CLMS_TASK_INFO T
        WHERE
          T.STATUS in ('0')
          and exists (select 1 from CLMS_TASK_INFO b where t.REPORT_NO = b.REPORT_NO and t.CASE_TIMES = b.CASE_TIMES and b.`STATUS` = '2')
          AND T.REPORT_NO = TRIM(#{reportNo})
          AND T.CASE_TIMES = #{caseTimes}
    </select>

    <select id="getClaimESTaskInfoList" resultMap="ClaimESTaskInfoMap">
        select
            t.ID_AHCS_TASK_INFO  "idAhcsTaskInfo",
            t.TASK_ID "taskId",
            t.report_no "reportNo",
            t.case_times "caseTimes",
            t.TASK_DEFINITION_BPM_KEY "taskDefinitionBpmKey",
            t.ASSIGNER "assigner",
            t.ASSIGNEE_NAME "assigneeName",
            t.CREATED_DATE "createdDate",
            t.UPDATED_DATE  "updatedDate",
            t.ASSIGNEE_TIME "assigneeTime",
            t.COMPLETE_TIME "completeTime",
            t.DEPARTMENT_CODE "departmentCode",
            dd.DEPARTMENT_ABBR_NAME "departmentName",
            t.status "status",
            t.END_DATE "endDate",
            t.ARCHIVE_TIME "archiveTime",
            t.APPLYER  "applyer",
            t.APPLYER_NAME  "applyerName",
            t.TASK_GRADE "taskGrade",
            t.AUDIT_GRADE "auditGrade",
            case t.TASK_DEFINITION_BPM_KEY when 'OC_RESTART_CASE_APPROVAL' then (select crc.UPDATED_DATE from clm_restart_case_record crc where crc.report_no=t.report_no and crc.case_times=t.case_times order by crc.CREATED_DATE desc limit 1) else b.REPORT_DATE end "caseLengthDate",
            case t.TASK_DEFINITION_BPM_KEY when 'OC_INVESTIGATE_APPROVAL' then t.TASK_ID else (select t0.id_ahcs_investigate from CLMS_investigate t0 where t0.report_no=t.report_no and t0.investigate_status in ('1','2','3') and t0.operate = 1  limit 0,1) end "idAhcsInvestigate" ,
            case t.TASK_DEFINITION_BPM_KEY when 'OC_MAJOR_INVESTIGATE' then t.TASK_ID else (select t1.id_ahcs_investigate_task from CLMS_investigate_task t1 where t1.report_no=t.report_no and t1.task_status in ('1','4') limit 0,1) end "idAhcsInvestigateTask" ,
            case t.TASK_DEFINITION_BPM_KEY when 'OC_INVESTIGATE_REVIEW' then t.TASK_ID end "idAhcsInvestigateTaskAudit",
            a.name "insuredName",
            a.client_type "clientType",
            case a.client_type when '5' then 'Y' else 'N' end "insuredLevel",
            a.client_cluster "personnelAttribute",
            (select c.ESTIMATE_AMOUNT from clms_estimate_record c where c.report_no=t.report_no and c.case_times=t.case_times order by c.EFFECTIVE_TIME desc limit 1) "estimateAmount",
            (select sum(ce.CHANGE_AMOUNT) from clms_estimate_change ce where ce.report_no=t.report_no and ce.case_times=t.case_times and ce.IS_EFFECTIVE='Y') "estimateChangeAmount",
            (select cr.RESTART_AMOUNT from clm_restart_case_record cr where cr.report_no=t.report_no and cr.case_times=t.case_times order by cr.CREATED_DATE desc limit 1) "restartAmount",
            d.CASE_NO "caseNo",
            cri.is_quick_pay "isquickpay"
        from clms_task_info t
            JOIN clms_report_customer a ON a.report_no = t.report_no
            JOIN clm_report_info b ON b.report_no = t.report_no
            JOIN clms_report_info_ex cri ON cri.report_no = T.REPORT_NO
            JOIN clm_case_base d ON t.report_no = d.report_no and t.CASE_TIMES = d.CASE_TIMES
            JOIN department_define dd on dd.DEPARTMENT_CODE = t.DEPARTMENT_CODE
        where t.ID_AHCS_TASK_INFO =#{idAhcsTaskInfo}
    </select>

    <select id="getClaimESPolicyInfoVOList" resultType="com.paic.ncbs.claim.model.vo.taskdeal.ClaimESPolicyInfoVO">
        select
            cpi.REPORT_NO "reportNo",
            cpi.POLICY_NO "policyNo",
            cip.SCHEME_NO "schemeNo",
            cip.SCHEME_NAME "schemeName",
            cpi.product_code "productcode",
            cpi.product_name  "productName"
        from  clms_policy_info cpi
                  JOIN clms_insured_person cip ON cpi.ID_AHCS_POLICY_INFO = cip.ID_AHCS_POLICY_INFO
        where cpi.REPORT_NO =#{reportNo}
    </select>

    <update id="modifyTaskInfoOnES">
        UPDATE clms_task_info a
        SET a.es_updated_date = NOW(3)
        WHERE a.REPORT_NO = #{reportNo}
    </update>

    <update id="pushAllTaskInfoToES">
        UPDATE clms_task_info a
        SET a.es_updated_date = NOW(3)
        WHERE a.STATUS = #{status}
        <if test= ' updatedStarTDate != null and updatedStarTDate != "" '>
            and <![CDATA[ date_format(a.es_updated_date,'%Y-%m-%d') >= date_format( #{updatedStarTDate},'%Y-%m-%d') ]]>
        </if>
        <if test= ' updatedEndDate != null and updatedEndDate != "" '>
            and <![CDATA[ date_format(a.es_updated_date,'%Y-%m-%d') <= date_format( #{updatedEndDate},'%Y-%m-%d') ]]>
        </if>
    </update>

    <select id="getLatestTaskId" resultType="java.lang.String">
        select TASK_ID as taskId
        from clms_task_info a
        where a.report_no = #{reportNo}
          and a.case_times = #{caseTimes}
          and a.TASK_DEFINITION_BPM_KEY = #{taskKey}
        order by a.UPDATED_DATE desc limit 1
    </select>
    <select id="getUndealCaseList" resultMap="resultTaskInfoList">
        select ID_AHCS_TASK_INFO,
               TASK_ID,
               REPORT_NO,
               CASE_TIMES,
               BUSINESS_KEY,
               TASK_DEFINITION_BPM_KEY,
               ASSIGNER,
               ASSIGNEE_TIME,
               DEPARTMENT_CODE,
               STATUS
        from clms_task_info where status in ('0','3')
    </select>
    <select id="getClaimInfoToES" resultType="com.paic.ncbs.claim.model.vo.taskdeal.ClaimInfoToESVO">
        SELECT
        ccb.ID_CLM_CASE_BASE AS "idCase",
        ccb.REPORT_NO AS "reportNo",
        ccb.CASE_TIMES AS "caseTimes",
        cpi.POLICY_NO AS "policyNo",
        cpi.INSURANCE_BEGIN_TIME AS "insuranceBeginDate",
        cpi.INSURANCE_END_TIME AS "insuranceEndDate",
        cpi.LAST_POLICY_NO AS "lastPolicyNo",
        cpi.PRODUCT_CODE AS "productCode",
        cpi.PRODUCT_NAME AS "productName",
        ( CASE WHEN ccb.risk_group_no IS  NULL or ccb.risk_group_no='' THEN cip.SCHEME_NO ELSE ccb.risk_group_no END ) AS "packageCode",
        ( CASE WHEN ccb.risk_group_name IS  NULL or ccb.risk_group_name='' THEN cip.SCHEME_NAME ELSE ccb.risk_group_name END ) AS "packageName",
        cip.CLIENT_NO AS "insuredClientCode",
        cip.`NAME` AS "insuredName",
        ( CASE WHEN crpc.NAME IS NOT NULL THEN NULL ELSE cip.CLIENT_NO END ) "objectClientCode",
        IFNULL( crpc.NAME, cip.`NAME` ) AS "objectName",
        IFNULL( crpc.CERTIFICATE_NO, cip.CERTIFICATE_NO ) AS "objectCertNo",
        cri.REPORT_DATE AS "reportDate",
        cra.ACCIDENT_DATE AS "accidentDate",
        (
        SELECT
        GROUP_CONCAT( DISTINCT cph.HOSPITAL_NAME ORDER BY cph.HOSPITAL_NAME SEPARATOR ', ' )
        FROM
        clms_person_hospital cph
        WHERE
        cph.REPORT_NO = ccb.REPORT_NO
        AND cph.CASE_TIMES = ccb.CASE_TIMES
        AND cph.TASK_ID = 'checkDuty'
        AND cph.`STATUS` = '1'
        ) AS "hospitalName",
        (
        SELECT
        GROUP_CONCAT( DISTINCT cdd.DIAGNOSE_NAME ORDER BY cpd.diagnose_code SEPARATOR ', ' )
        FROM
        clms_person_diagnose cpd
        LEFT JOIN clms_diagnose_define cdd ON cpd.diagnose_code = cdd.diagnose_code
        WHERE
        cpd.REPORT_NO = ccb.REPORT_NO
        AND cpd.CASE_TIMES = ccb.CASE_TIMES
        AND cpd.TASK_ID = 'checkDuty'
        AND cpd.`STATUS` = '1'
        ) AS "diagnoseCode",
        ccb.CASE_STATUS AS "caseStatus",
        ccb.INDEMNITY_CONCLUSION AS "caseNature",
        (
        SELECT
        sum( pp.POLICY_SUM_PAY )
        FROM
        CLM_POLICY_PAY pp
        WHERE
        pp.REPORT_NO = ccb.REPORT_NO
        AND pp.CASE_TIMES = ccb.CASE_TIMES
        ) AS "paidAmount",
        "NCBS" AS "dataSource"
        FROM
        clm_case_base ccb
        JOIN clms_policy_info cpi ON ccb.REPORT_NO = cpi.REPORT_NO
        AND ccb.POLICY_NO = cpi.POLICY_NO
        JOIN clms_insured_person cip ON cpi.ID_AHCS_POLICY_INFO = cip.ID_AHCS_POLICY_INFO
        JOIN clm_report_info cri ON ccb.REPORT_NO = cri.REPORT_NO
        JOIN clm_report_accident cra ON ccb.REPORT_NO = cra.REPORT_NO
        LEFT JOIN clms_risk_property_case crpc ON ccb.REPORT_NO = crpc.REPORT_NO
        AND ccb.CASE_TIMES = crpc.CASE_TIMES
        AND crpc.IS_EFFECTIVE = 'Y'
        WHERE
        ccb.ID_CLM_CASE_BASE = #{idCase}
        limit 1
    </select>
    <select id="getTaskInfoList"  parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO" resultMap="resultTaskInfoList">
        select ID_AHCS_TASK_INFO,
        TASK_ID,
        REPORT_NO,
        CASE_TIMES,
        BUSINESS_KEY,
        TASK_DEFINITION_BPM_KEY,
        ASSIGNER,
        ASSIGNEE_TIME,
        DEPARTMENT_CODE,
        STATUS,
        complete_time,
        created_Date
        from clms_task_info
        where 1=1
        <if test="reportNo != null and reportNo !=''">
           and REPORT_NO=#{reportNo}
        </if>
        <if test="caseTimes != null">
            and CASE_TIMES=#{caseTimes}
        </if>
        <if test="taskDefinitionBpmKey != null and taskDefinitionBpmKey !=''">
            and TASK_DEFINITION_BPM_KEY=#{taskDefinitionBpmKey}
        </if>
        <if test="status != null and status !=''">
            and STATUS=#{status}
        </if>
        <if test="idAhcsTaskInfo != null and idAhcsTaskInfo !=''">
            and ID_AHCS_TASK_INFO=#{idAhcsTaskInfo}
        </if>
           order by created_date desc
    </select>
    <select id="getTaskAssignerNameNew" resultType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        select ASSIGNEE_NAME assigneeName,ASSIGNER from clms_task_info
                where REPORT_NO=#{reportNo}
                and case_times=#{caseTimes}
                and TASK_DEFINITION_BPM_KEY= #{taskKey}
                and status='0'
    </select>


    <update id="updateTaskInfo" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO">
        update CLMS_TASK_INFO ti
        set ti.UPDATED_DATE = NOW(),
        <if test="assigner != null and assigner != '' ">
            ti.ASSIGNER = #{assigner,jdbcType=VARCHAR},
        </if>
        <if test="assigneeName != null and assigneeName != '' ">
            ti.ASSIGNEE_NAME = #{assigneeName,jdbcType=VARCHAR},
        </if>
        <if test="completeTime != null">
            ti.COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
        </if>
        <if test="departmentCode != null and departmentCode != '' ">
            ti.DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
        </if>
        <if test="status != null and status != '' ">
            ti.STATUS = #{status,jdbcType=VARCHAR},
        </if>
        <if test="status == null or status == '' ">
            ti.STATUS = CASE WHEN ti.STATUS ='4' THEN '0' ELSE ti.STATUS END,
        </if>
        <if test="endDate != null ">
            ti.END_DATE = #{endDate,jdbcType=TIMESTAMP},
        </if>
        <if test="applyerName != null ">
            ti.APPLYER_NAME = #{applyerName,jdbcType=VARCHAR},
        </if>
        <if test="applyer != null ">
            ti.APPLYER = #{applyer,jdbcType=VARCHAR},
        </if>
        ti.UPDATED_BY = #{updatedBy},
        ti.ES_UPDATED_DATE = NOW(3)
        where ti.TASK_ID = #{taskId}
        <if test="status != null and status != '' ">
            and ti.STATUS NOT IN ('1','2')
        </if>
    </update>

    <select id="selectTaskByTaskKey" resultMap="resultTaskInfoList">
        select CREATED_BY,
               CREATED_DATE,
               UPDATED_BY,
               UPDATED_DATE,
               ID_AHCS_TASK_INFO,
               TASK_ID,
               REPORT_NO,
               CASE_TIMES,
               BUSINESS_KEY,
               TASK_DEFINITION_BPM_KEY,
               ASSIGNER,
               CREATED_TIME,
               ASSIGNEE_TIME,
               COMPLETE_TIME,
               DEPARTMENT_CODE,
               STATUS,
               END_DATE,
               APPLYER_NAME,
               APPLYER,
               PRE_TASK_ID ,
               TASK_GRADE ,
               AUDIT_GRADE
        from CLMS_TASK_INFO
        where TASK_ID = #{taskId}
            and TASK_DEFINITION_BPM_KEY=#{taskKey}
        limit 1
    </select>

    <select id="getQualityCaseList" parameterType="com.paic.ncbs.claim.model.vo.taskdeal.ClaimQuanlityCaseVO"
            resultType="com.paic.ncbs.claim.model.vo.taskdeal.WorkBenchTaskVO">
        SELECT
        cwb.REPORT_NO AS "reportNo",
        cwb.CASE_TIMES AS "caseTimes",
        dd.DEPARTMENT_ABBR_NAME AS "policyDepartmentName",
        cpi.POLICY_NO AS "policyNo",
        cpi.INSURANCE_BEGIN_TIME AS "policyBeginTime",
        cpi.INSURANCE_END_TIME AS "policyEndTime",
        cpi.PRODUCT_NAME AS "productName",
        cri.REPORT_DATE AS "reportDate",
        cra.ACCIDENT_DATE AS "accidentDate",
        cwb.END_CASE_DATE AS "completeTime",
        cwb.REGISTER_DATE AS "registerDate",
        crc.NAME AS "insuredName",
        CASE
        WHEN cs.TOTAL_SETTLE_AMOUNT IS NOT NULL AND cs.TOTAL_SETTLE_AMOUNT > 0 THEN cs.TOTAL_SETTLE_AMOUNT
        ELSE cer.ESTIMATE_AMOUNT
        END AS "paymentAmount"
        FROM clm_whole_case_base cwb
        JOIN clms_policy_info cpi ON cpi.REPORT_NO = cwb.REPORT_NO
        JOIN clm_report_info cri ON cri.REPORT_NO = cwb.REPORT_NO
        JOIN clm_report_accident cra ON cra.REPORT_NO = cwb.REPORT_NO
        JOIN clms_report_customer crc ON crc.REPORT_NO = cwb.REPORT_NO
        LEFT JOIN department_define dd ON dd.DEPARTMENT_CODE = cri.ACCEPT_DEPARTMENT_CODE
        LEFT JOIN (
        SELECT REPORT_NO, CASE_TIMES, SUM(POLICY_SUM_PAY) AS TOTAL_SETTLE_AMOUNT
        FROM CLM_POLICY_PAY
        GROUP BY REPORT_NO, CASE_TIMES
        ) cs ON cs.REPORT_NO = cwb.REPORT_NO AND cs.CASE_TIMES = cwb.CASE_TIMES
        LEFT JOIN clms_estimate_record cer ON cer.report_no = cwb.REPORT_NO AND cer.case_times = cwb.CASE_TIMES AND cer.ESTIMATE_TYPE = '02'
       <where>
           <if test="insuredName != null and insuredName != ''">
               AND crc.name = #{insuredName}
           </if>
           <if test='isIncludeSubordinates == "Y"'>
               AND cpi.DEPARTMENT_CODE IN
               <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                   #{item}
               </foreach>
           </if>
           <if test="accidStartTime != null">
               AND cra.ACCIDENT_DATE >= #{accidStartTime}
           </if>
           <if test="accidEndTime != null">
               AND cra.ACCIDENT_DATE &lt;= #{accidEndTime}
               AND cra.ACCIDENT_DATE IS NOT NULL
           </if>
           <if test="registStartTime != null">
               AND cwb.REGISTER_DATE >= #{registStartTime}
           </if>
           <if test="registEndTime != null">
               AND cwb.REGISTER_DATE &lt;= #{registEndTime}
               AND cwb.REGISTER_DATE IS NOT NULL
           </if>
           <if test="endCaseStartTime != null">
               AND cwb.END_CASE_DATE >= #{endCaseStartTime}
           </if>
           <if test="endCaseEndTime != null">
               AND cwb.END_CASE_DATE &lt;= #{endCaseEndTime}
               AND cwb.END_CASE_DATE IS NOT NULL
           </if>
           <if test="reportStartTime != null">
               AND cri.report_date >= #{reportStartTime}
           </if>
           <if test="reportEndTime != null">
               AND cri.report_date &lt;= #{reportEndTime}
               AND cri.report_date IS NOT NULL
           </if>
           <if test="productCode != null and productCode.size() > 0">
               AND cpi.product_code IN
               <foreach collection="productCode" item="item" open="(" close=")" separator=",">
                   #{item}
               </foreach>
           </if>
           <if test="exProductCode != null and exProductCode.size() > 0">
               AND cpi.product_code NOT IN
               <foreach collection="exProductCode" item="item" open="(" close=")" separator=",">
                   #{item}
               </foreach>
           </if>
           <if test="packageName != null and packageName != ''">
               AND EXISTS (
               SELECT 1
               FROM clms_insured_person cip
               WHERE cip.ID_AHCS_POLICY_INFO = cpi.ID_AHCS_POLICY_INFO
               AND cip.SCHEME_NAME = #{packageName}
               )
           </if>
           <if test="minmoney != null and minmoney != ''">
               AND (
               CASE
               WHEN cs.TOTAL_SETTLE_AMOUNT IS NOT NULL AND cs.TOTAL_SETTLE_AMOUNT > 0 THEN cs.TOTAL_SETTLE_AMOUNT
               ELSE cer.ESTIMATE_AMOUNT
               END >= #{minmoney}
               )
           </if>
           <if test="maxmoney != null and maxmoney != ''">
               AND (
               CASE
               WHEN cs.TOTAL_SETTLE_AMOUNT IS NOT NULL AND cs.TOTAL_SETTLE_AMOUNT > 0 THEN cs.TOTAL_SETTLE_AMOUNT
               ELSE cer.ESTIMATE_AMOUNT
               END &lt;= #{maxmoney}
               )
           </if>
           <if test="userCode != null and userCode != ''">
               AND EXISTS (
               SELECT 1
               FROM clms_task_info t
               WHERE t.REPORT_NO = cwb.REPORT_NO
               AND t.CASE_TIMES = cwb.CASE_TIMES
               AND t.APPLYER = #{userCode}
               )
           </if>
       </where>
        GROUP BY
        cwb.REPORT_NO,
        cwb.CASE_TIMES,
        dd.DEPARTMENT_ABBR_NAME,
        cpi.POLICY_NO,
        cpi.INSURANCE_BEGIN_TIME,
        cpi.INSURANCE_END_TIME,
        cpi.PRODUCT_NAME,
        cri.REPORT_DATE,
        cra.ACCIDENT_DATE,
        cwb.END_CASE_DATE,
        cwb.REGISTER_DATE,
        crc.NAME,
        cs.TOTAL_SETTLE_AMOUNT,
        cer.ESTIMATE_AMOUNT
        ORDER BY cwb.CASE_TIMES DESC, cri.REPORT_DATE
    </select>
    <!-- 根据报案号、赔付次数和节点代码查询任务处理人姓名，合并处理人姓名 -->
    <select id="getTaskAssignerNameq" resultType="java.lang.String">
        SELECT
        GROUP_CONCAT(DISTINCT assignee_name ORDER BY created_time DESC SEPARATOR ',')
        FROM clms_task_info
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
        AND task_definition_bpm_key = #{nodeCode}
        AND status = '1'
    </select>

    <!-- 根据报案号、赔付次数和节点代码查询任务处理人姓名，合并处理人姓名 -->
    <select id="getTaskAssignerNameHP" resultType="java.lang.String">
        SELECT
        GROUP_CONCAT(DISTINCT APPLYER_NAME ORDER BY created_time DESC SEPARATOR ',')
        FROM clms_task_info
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
        AND task_definition_bpm_key = #{nodeCode}
    </select>
</mapper>