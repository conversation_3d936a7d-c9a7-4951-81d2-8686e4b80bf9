<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace =  "com.paic.ncbs.claim.dao.mapper.other.MailSendMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.message.MailSendDTO" id="BaseResultMap">
            <id property="id" column="id" jdbcType="VARCHAR" />
            <result property="businessNo" column="business_no" jdbcType="VARCHAR" />
            <result property="mailType" column="mail_type" jdbcType="VARCHAR" />
            <result property="sendFrom" column="send_from" jdbcType="VARCHAR" />
            <result property="sendTime" column="send_time" jdbcType="TIMESTAMP" />
            <result property="handler" column="handler" jdbcType="VARCHAR" />
            <result property="status" column="status" jdbcType="VARCHAR" />
            <result property="title" column="title" jdbcType="VARCHAR" />
            <result property="content" column="content" jdbcType="VARCHAR" />
            <result property="format" column="format" jdbcType="VARCHAR" />
            <result property="sendTo" column="send_to" jdbcType="VARCHAR" />
            <result property="mailCc" column="mail_cc" jdbcType="VARCHAR" />
            <result property="resources" column="resources" jdbcType="VARCHAR" />
            <result property="attachments" column="attachments" jdbcType="VARCHAR" />
            <result property="failReason" column="fail_reason" jdbcType="VARCHAR" />
            <result property="createdBy" column="created_by" jdbcType="VARCHAR" />
            <result property="sysCtime" column="sys_ctime" jdbcType="TIMESTAMP" />
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR" />
            <result property="sysUtime" column="sys_utime" jdbcType="TIMESTAMP" />
    </resultMap>
    <update id="updateMailSendById" parameterType="com.paic.ncbs.claim.model.dto.message.MailInfoDTO">
        update clms_mail_send
        <set>
            status = #{status},
            fail_reason = #{failReason}
        </set>
        where id = #{id}
    </update>
    <select id="getMailSendByBusinessNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
         business_no,
         mail_type,
         send_from,
         send_time,
         handler,
         status
        from clms_mail_send where business_no = #{businessNo}
        and mail_type in('REINS_ACTUAL','REINS_ESTIMATED')
    </select>
</mapper>
