<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.other.TextTemplateMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.other.TextTemplateEntity">
        <!--@mbg.generated-->
        <!--@Table clms_text_template-->
        <id column="ID_AHCS_TEXT_TEMPLATE" property="idAhcsTextTemplate"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="TEMPLATE_CODE" property="templateCode"/>
        <result column="TEMPLATE_CONTENT" property="templateContent"/>
        <result column="TEMPLATE_NAME" property="templateName"/>
        <result column="TEMPLATE_DESC" property="templateDesc"/>
        <result column="STATUS" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID_AHCS_TEXT_TEMPLATE,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        TEMPLATE_CODE,
        TEMPLATE_CONTENT,
        TEMPLATE_NAME,
        TEMPLATE_DESC,
        `STATUS`
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from clms_text_template
        where ID_AHCS_TEXT_TEMPLATE = #{idAhcsTextTemplate}
    </select>

    <select id="selectByTemplateCode" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from clms_text_template
        where TEMPLATE_CODE = #{templateCode}
          AND `STATUS` = 'Y'
          AND `FLAG` = 'WB'
        <if test="templateDesc != null">
            <bind name="templateDescSql" value="'%' + templateDesc + '%'"/>
            AND (TEMPLATE_DESC = 'ALL' OR TEMPLATE_DESC LIKE #{templateDescSql})
        </if>
    </select>

    <select id="selectBlackInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from clms_text_template
        where TEMPLATE_CODE = #{templateCode}
        AND `STATUS` = 'Y'
        <if test="templateDesc != null">
            <bind name="templateDescSql" value="'%' + templateDesc + '%'"/>
            AND (TEMPLATE_DESC = 'ALL' OR TEMPLATE_DESC LIKE #{templateDescSql})
        </if>
    </select>

    <select id="getStandardTemplate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from clms_text_template
        where TEMPLATE_CODE = #{templateCode}
        AND `STATUS` = 'Y'
        AND `FLAG` = 'STANDARD'
    </select>

    <select id="getBackStopTemplate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from clms_text_template
        where TEMPLATE_CODE = #{templateCode}
        AND `STATUS` = 'Y'
        AND `FLAG` = 'BACKSTOP'
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete
        from clms_text_template
        where ID_AHCS_TEXT_TEMPLATE = #{idAhcsTextTemplate}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.other.TextTemplateEntity">
        <!--@mbg.generated-->
        insert into clms_text_template (ID_AHCS_TEXT_TEMPLATE, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE,
                                        TEMPLATE_CODE, TEMPLATE_CONTENT, TEMPLATE_NAME, TEMPLATE_DESC, `STATUS`)
        values (#{idAhcsTextTemplate}, #{createdBy}, #{createdDate}, #{updatedBy}, #{updatedDate},
                #{templateCode}, #{templateContent}, #{templateName}, #{templateDesc}, #{status})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.other.TextTemplateEntity">
        <!--@mbg.generated-->
        update clms_text_template
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate},
            </if>
            <if test="templateCode != null">
                TEMPLATE_CODE = #{templateCode},
            </if>
            <if test="templateContent != null">
                TEMPLATE_CONTENT = #{templateContent},
            </if>
            <if test="templateName != null">
                TEMPLATE_NAME = #{templateName},
            </if>
            <if test="templateDesc != null">
                TEMPLATE_DESC = #{templateDesc},
            </if>
            <if test="status != null">
                `STATUS` = #{status},
            </if>
        </set>
        where ID_AHCS_TEXT_TEMPLATE = #{idAhcsTextTemplate}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.other.TextTemplateEntity">
        <!--@mbg.generated-->
        update clms_text_template
        set CREATED_BY       = #{createdBy},
            CREATED_DATE     = #{createdDate},
            UPDATED_BY       = #{updatedBy},
            UPDATED_DATE     = #{updatedDate},
            TEMPLATE_CODE    = #{templateCode},
            TEMPLATE_CONTENT = #{templateContent},
            TEMPLATE_NAME    = #{templateName},
            TEMPLATE_DESC    = #{templateDesc},
            `STATUS`         = #{status}
        where ID_AHCS_TEXT_TEMPLATE = #{idAhcsTextTemplate}
    </update>
</mapper>